---
title: "Using Tools with Agents | Agents | Mastra Docs"
description: Learn how to create tools, add them to Mastra agents, and integrate tools from MCP servers.
---

# Using Tools with Agents

Tools are typed functions that can be executed by agents or workflows. Each tool has a schema defining its inputs, an executor function implementing its logic, and optional access to configured integrations.

## Creating Tools

Here's a basic example of creating a tool:

```typescript filename="src/mastra/tools/weatherInfo.ts" copy
import { createTool } from "@mastra/core/tools";
import { z } from "zod";

export const weatherInfo = createTool({
  id: "Get Weather Information",
  inputSchema: z.object({
    city: z.string(),
  }),
  description: `Fetches the current weather information for a given city`,
  execute: async ({ context: { city } }) => {
    // Tool logic here (e.g., API call)
    console.log("Using tool to fetch weather information for", city);
    return { temperature: 20, conditions: "Sunny" }; // Example return
  },
});
```

For details on creating and designing tools, see the [Tools Overview](/docs/tools-mcp/overview).

## Adding Tools to an Agent

To make a tool available to an agent, add it to the `tools` property in the agent's configuration.

```typescript filename="src/mastra/agents/weatherAgent.ts" {3,11}
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { weatherInfo } from "../tools/weatherInfo";

export const weatherAgent = new Agent({
  name: "Weather Agent",
  instructions:
    "You are a helpful assistant that provides current weather information. When asked about the weather, use the weather information tool to fetch the data.",
  model: openai("gpt-4o-mini"),
  tools: {
    weatherInfo,
  },
});
```

When you call the agent, it can now decide to use the configured tool based on its instructions and the user's prompt.

## Adding MCP Tools to an Agent

[Model Context Protocol (MCP)](https://modelcontextprotocol.io/introduction) provides a standardized way for AI models to discover and interact with external tools and resources. You can connect your Mastra agent to MCP servers to use tools provided by third parties.

For more details on MCP concepts and how to set up MCP clients and servers, see the [MCP Overview](/docs/tools-mcp/mcp-overview).

### Installation

First, install the Mastra MCP package:

```bash npm2yarn copy
npm install @mastra/mcp@latest
```

### Using MCP Tools

Because there are so many MCP server registries to choose from, we've created an [MCP Registry Registry](https://mastra.ai/mcp-registry-registry) to help you find MCP servers.

Once you have a server you want to use with your agent, import the Mastra `MCPClient` and add the server configuration.

```typescript filename="src/mastra/mcp.ts" {1,7-16}
import { MCPClient } from "@mastra/mcp";

// Configure MCPClient to connect to your server(s)
export const mcp = new MCPClient({
  servers: {
    filesystem: {
      command: "npx",
      args: [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Downloads",
      ],
    },
  },
});
```

Then connect your agent to the server tools:

```typescript filename="src/mastra/agents/mcpAgent.ts" {7}
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { mcp } from "../mcp";

// Create an agent and add tools from the MCP client
const agent = new Agent({
  name: "Agent with MCP Tools",
  instructions: "You can use tools from connected MCP servers.",
  model: openai("gpt-4o-mini"),
  tools: await mcp.getTools(),
});
```

For more details on configuring `MCPClient` and the difference between static and dynamic MCP server configurations, see the [MCP Overview](/docs/tools-mcp/mcp-overview).

## Accessing MCP Resources

In addition to tools, MCP servers can also expose resources - data or content that can be retrieved and used in your application.

```typescript filename="src/mastra/resources.ts" {3-8}
import { mcp } from "./mcp";

// Get resources from all connected MCP servers
const resources = await mcp.getResources();

// Access resources from a specific server
if (resources.filesystem) {
  const resource = resources.filesystem.find(
    (r) => r.uri === "filesystem://Downloads",
  );
  console.log(`Resource: ${resource?.name}`);
}
```

Each resource has a URI, name, description, and MIME type. The `getResources()` method handles errors gracefully - if a server fails or doesn't support resources, it will be omitted from the results.

## Accessing MCP Prompts

MCP servers can also expose prompts, which represent structured message templates or conversational context for agents.

### Listing Prompts

```typescript filename="src/mastra/prompts.ts"
import { mcp } from "./mcp";

// Get prompts from all connected MCP servers
const prompts = await mcp.prompts.list();

// Access prompts from a specific server
if (prompts.weather) {
  const prompt = prompts.weather.find(
    (p) => p.name === "current"
  );
  console.log(`Prompt: ${prompt?.name}`);
}
```

Each prompt has a name, description, and (optional) version.

### Retrieving a Prompt and Its Messages

```typescript filename="src/mastra/prompts.ts"
const { prompt, messages } = await mcp.prompts.get({ serverName: "weather", name: "current" });
console.log(prompt);    // { name: "current", version: "v1", ... }
console.log(messages);  // [ { role: "assistant", content: { type: "text", text: "..." } }, ... ]
```

## Exposing Agents as Tools via MCPServer

In addition to using tools from MCP servers, your Mastra Agents themselves can be exposed as tools to any MCP-compatible client using Mastra's `MCPServer`.

When an `Agent` instance is provided to an `MCPServer` configuration:

- It is automatically converted into a callable tool.
- The tool is named `ask_<agentKey>`, where `<agentKey>` is the identifier you used when adding the agent to the `MCPServer`'s `agents` configuration.
- The agent's `description` property (which must be a non-empty string) is used to generate the tool's description.

This allows other AI models or MCP clients to interact with your Mastra Agents as if they were standard tools, typically by "asking" them a question.

**Example `MCPServer` Configuration with an Agent:**

```typescript filename="src/mastra/mcp.ts"
import { Agent } from "@mastra/core/agent";
import { MCPServer } from "@mastra/mcp";
import { openai } from "@ai-sdk/openai";
import { weatherInfo } from "../tools/weatherInfo";
import { generalHelper } from "../agents/generalHelper";

const server = new MCPServer({
  name: "My Custom Server with Agent-Tool",
  version: "1.0.0",
  tools: {
    weatherInfo,
  },
  agents: { generalHelper }, // Exposes 'ask_generalHelper' tool
});
```

For an agent to be successfully converted into a tool by `MCPServer`, its `description` property must be set to a non-empty string in its constructor configuration. If the description is missing or empty, `MCPServer` will throw an error during initialization.

For more details on setting up and configuring `MCPServer`, refer to the [MCPServer reference documentation](/reference/tools/mcp-server).
