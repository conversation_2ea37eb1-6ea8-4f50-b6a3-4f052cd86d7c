---
title: "Inngest Workflows | Workflows | Mastra Docs"
description: "Inngest workflow allows you to run Mastra workflows with Inngest"
---

# Inngest Workflow

[Inngest](https://www.inngest.com/docs) is a developer platform for building and running background workflows, without managing infrastructure.

## How Inngest Works with Mastra

Inngest and Mastra integrate by aligning their workflow models: Inngest organizes logic into functions composed of steps, and Mastra workflows defined using `createWorkflow` and `createStep` map directly onto this paradigm. Each Mastra workflow becomes an Inngest function with a unique identifier, and each step within the workflow maps to an Inngest step.

The `serve` function bridges the two systems by registering Mastra workflows as Inngest functions and setting up the necessary event handlers for execution and monitoring.

When an event triggers a workflow, Inngest executes it step by step, memoizing each step’s result. This means if a workflow is retried or resumed, completed steps are skipped, ensuring efficient and reliable execution. Control flow primitives in Mastra, such as loops, conditionals, and nested workflows are seamlessly translated into the same Inngest’s function/step model, preserving advanced workflow features like composition, branching, and suspension.

Real-time monitoring, suspend/resume, and step-level observability are enabled via Inngest’s publish-subscribe system and dashboard. As each step executes, its state and output are tracked using Mastra storage and can be resumed as needed.

## Setup

```sh
npm install @mastra/inngest @mastra/core @mastra/deployer
```

## Building an Inngest Workflow

This guide walks through creating a workflow with Inngest and Mastra, demonstrating a counter application that increments a value until it reaches 10.

### Inngest Initialization

Initialize the Inngest integration to obtain Mastra-compatible workflow helpers. The createWorkflow and createStep functions are used to create workflow and step objects that are compatible with Mastra and inngest.

In development

```ts showLineNumbers copy filename="src/mastra/inngest/index.ts"
import { Inngest } from "inngest";
import { realtimeMiddleware } from "@inngest/realtime";

export const inngest = new Inngest({
  id: "mastra",
  baseUrl:"http://localhost:8288",
  isDev: true,
  middleware: [realtimeMiddleware()],
});
```

In production

```ts showLineNumbers copy filename="src/mastra/inngest/index.ts"
import { Inngest } from "inngest";
import { realtimeMiddleware } from "@inngest/realtime";

export const inngest = new Inngest({
  id: "mastra",
  middleware: [realtimeMiddleware()],
});
```

### Creating Steps

Define the individual steps that will compose your workflow:

```ts showLineNumbers copy filename="src/mastra/workflows/index.ts"
import { z } from "zod";
import { inngest } from "../inngest";
import { init } from "@mastra/inngest";

// Initialize Inngest with Mastra, pointing to your local Inngest server
const { createWorkflow, createStep } = init(inngest);

// Step: Increment the counter value
const incrementStep = createStep({
  id: "increment",
  inputSchema: z.object({
    value: z.number(),
  }),
  outputSchema: z.object({
    value: z.number(),
  }),
  execute: async ({ inputData }) => {
    return { value: inputData.value + 1 };
  },
});
```

### Creating the Workflow

Compose the steps into a workflow using the `dountil` loop pattern. The createWorkflow function creates a function on inngest server that is invocable.

```ts showLineNumbers copy filename="src/mastra/workflows/index.ts"
// workflow that is registered as a function on inngest server
const workflow = createWorkflow({
  id: "increment-workflow",
  inputSchema: z.object({
    value: z.number(),
  }),
  outputSchema: z.object({
    value: z.number(),
  }),
}).then(incrementStep);

workflow.commit();

export { workflow as incrementWorkflow };
```

### Configuring the Mastra Instance and Executing the Workflow

Register the workflow with Mastra and configure the Inngest API endpoint:

```ts showLineNumbers copy filename="src/mastra/index.ts"
import { Mastra } from "@mastra/core/mastra";
import { serve as inngestServe } from "@mastra/inngest";
import { incrementWorkflow } from "./workflows";
import { inngest } from "./inngest";
import { PinoLogger } from "@mastra/loggers";

// Configure Mastra with the workflow and Inngest API endpoint
export const mastra = new Mastra({
  workflows: {
    incrementWorkflow,
  },
  server: {
    // The server configuration is required to allow local docker container can connect to the mastra server
    host: "0.0.0.0",
    apiRoutes: [
      // This API route is used to register the Mastra workflow (inngest function) on the inngest server
      {
        path: "/api/inngest",
        method: "ALL",
        createHandler: async ({ mastra }) => inngestServe({ mastra, inngest }),
        // The inngestServe function integrates Mastra workflows with Inngest by:
        // 1. Creating Inngest functions for each workflow with unique IDs (workflow.${workflowId})
        // 2. Setting up event handlers that:
        //    - Generate unique run IDs for each workflow execution
        //    - Create an InngestExecutionEngine to manage step execution
        //    - Handle workflow state persistence and real-time updates
        // 3. Establishing a publish-subscribe system for real-time monitoring
        //    through the workflow:${workflowId}:${runId} channel
      },
    ],
  },
  logger: new PinoLogger({
    name: "Mastra",
    level: "info",
  }),
});
```

### Running the Workflow locally

> **Prerequisites:**
>
> - Docker installed and running
> - Mastra project set up
> - Dependencies installed (`npm install`)

1. Run `npx mastra dev` to start the Mastra server on local to serve the server on port 4111.
2. Start the Inngest Dev Server (via Docker)
   In a new terminal, run:

```sh
docker run --rm -p 8288:8288 \
  inngest/inngest \
  inngest dev -u http://host.docker.internal:4111/api/inngest
```

> **Note:** The URL after `-u` tells the Inngest dev server where to find your Mastra `/api/inngest` endpoint.

3. Open the Inngest Dashboard

- Visit [http://localhost:8288](http://localhost:8288) in your browser.
- Go to the **Apps** section in the sidebar.
- You should see your Mastra workflow registered.
  ![Inngest Dashboard](/inngest-apps-dashboard.png)

4. Invoke the Workflow

- Go to the **Functions** section in the sidebar.
- Select your Mastra workflow.
- Click **Invoke** and use the following input:

```json
{
  "data": {
    "inputData": {
      "value": 5
    }
  }
}
```

![Inngest Function](/inngest-function-dashboard.png)

5. **Monitor the Workflow Execution**

- Go to the **Runs** tab in the sidebar.
- Click on the latest run to see step-by-step execution progress.
  ![Inngest Function Run](/inngest-runs-dashboard.png)

### Running the Workflow in Production

> **Prerequisites:**
>
> - Vercel account and Vercel CLI installed (`npm i -g vercel`)
> - Inngest account
> - Vercel token (recommended: set as environment variable)

1. Add Vercel Deployer to Mastra instance

```ts showLineNumbers copy filename="src/mastra/index.ts"
import { VercelDeployer } from "@mastra/deployer-vercel";

export const mastra = new Mastra({
  // ...other config
  deployer: new VercelDeployer({
    teamSlug: "your_team_slug",
    projectName: "your_project_name",
    // you can get your vercel token from the vercel dashboard by clicking on the user icon in the top right corner
    // and then clicking on "Account Settings" and then clicking on "Tokens" on the left sidebar.
    token: "your_vercel_token",
  }),
});
```

> **Note:** Set your Vercel token in your environment:
>
> ```sh
> export VERCEL_TOKEN=your_vercel_token
> ```

2. Build the mastra instance

```sh
npx mastra build
```

3. Deploy to Vercel

```sh
cd .mastra/output
vercel --prod
```

> **Tip:** If you haven't already, log in to Vercel CLI with `vercel login`.

4. Sync with Inngest Dashboard

- Go to the [Inngest dashboard](https://app.inngest.com/env/production/apps).
- Click **Sync new app with Vercel** and follow the instructions.
- You should see your Mastra workflow registered as an app.
  ![Inngest Dashboard](/inngest-apps-dashboard-prod.png)

5. Invoke the Workflow

- In the **Functions** section, select `workflow.increment-workflow`.
- Click **All actions** (top right) > **Invoke**.
- Provide the following input:

```json
{
  "data": {
    "inputData": {
      "value": 5
    }
  }
}
```

![Inngest Function Run](/inngest-function-dashboard-prod.png)

6.  Monitor Execution

- Go to the **Runs** tab.
- Click the latest run to see step-by-step execution progress.
  ![Inngest Function Run](/inngest-runs-dashboard-prod.png)
