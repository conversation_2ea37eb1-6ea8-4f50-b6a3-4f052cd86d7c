---
title: "Retrieval, Semantic Search, Reranking | RAG | Mastra Docs"
description: Guide on retrieval processes in Mastra's RAG systems, including semantic search, filtering, and re-ranking.
---

import { Tabs } from "nextra/components";

## Retrieval in RAG Systems

After storing embeddings, you need to retrieve relevant chunks to answer user queries.

Mastra provides flexible retrieval options with support for semantic search, filtering, and re-ranking.

## How Retrieval Works

1. The user's query is converted to an embedding using the same model used for document embeddings
2. This embedding is compared to stored embeddings using vector similarity
3. The most similar chunks are retrieved and can be optionally:

- Filtered by metadata
- Re-ranked for better relevance
- Processed through a knowledge graph

## Basic Retrieval

The simplest approach is direct semantic search. This method uses vector similarity to find chunks that are semantically similar to the query:

```ts showLineNumbers copy
import { openai } from "@ai-sdk/openai";
import { embed } from "ai";
import { PgVector } from "@mastra/pg";

// Convert query to embedding
const { embedding } = await embed({
  value: "What are the main points in the article?",
  model: openai.embedding("text-embedding-3-small"),
});

// Query vector store
const pgVector = new PgVector({
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
});
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 10,
});

// Display results
console.log(results);
```

Results include both the text content and a similarity score:

```ts showLineNumbers copy
[
  {
    text: "Climate change poses significant challenges...",
    score: 0.89,
    metadata: { source: "article1.txt" },
  },
  {
    text: "Rising temperatures affect crop yields...",
    score: 0.82,
    metadata: { source: "article1.txt" },
  },
  // ... more results
];
```

For an example of how to use the basic retrieval method, see the [Retrieve Results](../../examples/rag/query/retrieve-results.mdx) example.

## Advanced Retrieval options

### Metadata Filtering

Filter results based on metadata fields to narrow down the search space. This is useful when you have documents from different sources, time periods, or with specific attributes. Mastra provides a unified MongoDB-style query syntax that works across all supported vector stores.

For detailed information about available operators and syntax, see the [Metadata Filters Reference](/reference/rag/metadata-filters).

Basic filtering examples:

```ts showLineNumbers copy
// Simple equality filter
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 10,
  filter: {
    source: "article1.txt",
  },
});

// Numeric comparison
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 10,
  filter: {
    price: { $gt: 100 },
  },
});

// Multiple conditions
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 10,
  filter: {
    category: "electronics",
    price: { $lt: 1000 },
    inStock: true,
  },
});

// Array operations
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 10,
  filter: {
    tags: { $in: ["sale", "new"] },
  },
});

// Logical operators
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 10,
  filter: {
    $or: [{ category: "electronics" }, { category: "accessories" }],
    $and: [{ price: { $gt: 50 } }, { price: { $lt: 200 } }],
  },
});
```

Common use cases for metadata filtering:

- Filter by document source or type
- Filter by date ranges
- Filter by specific categories or tags
- Filter by numerical ranges (e.g., price, rating)
- Combine multiple conditions for precise querying
- Filter by document attributes (e.g., language, author)

For an example of how to use metadata filtering, see the [Hybrid Vector Search](../../examples/rag/query/hybrid-vector-search.mdx) example.

### Vector Query Tool

Sometimes you want to give your agent the ability to query a vector database directly. The Vector Query Tool allows your agent to be in charge of retrieval decisions, combining semantic search with optional filtering and reranking based on the agent's understanding of the user's needs.

```ts showLineNumbers copy
const vectorQueryTool = createVectorQueryTool({
  vectorStoreName: "pgVector",
  indexName: "embeddings",
  model: openai.embedding("text-embedding-3-small"),
});
```

When creating the tool, pay special attention to the tool's name and description - these help the agent understand when and how to use the retrieval capabilities. For example, you might name it "SearchKnowledgeBase" and describe it as "Search through our documentation to find relevant information about X topic."

This is particularly useful when:

- Your agent needs to dynamically decide what information to retrieve
- The retrieval process requires complex decision-making
- You want the agent to combine multiple retrieval strategies based on context

#### Database-Specific Configurations

The Vector Query Tool supports database-specific configurations that enable you to leverage unique features and optimizations of different vector stores:

```ts showLineNumbers copy
// Pinecone with namespace
const pineconeQueryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  databaseConfig: {
    pinecone: {
      namespace: "production"  // Isolate data by environment
    }
  }
});

// pgVector with performance tuning
const pgVectorQueryTool = createVectorQueryTool({
  vectorStoreName: "postgres",
  indexName: "embeddings", 
  model: openai.embedding("text-embedding-3-small"),
  databaseConfig: {
    pgvector: {
      minScore: 0.7,    // Filter low-quality results
      ef: 200,          // HNSW search parameter
      probes: 10        // IVFFlat probe parameter
    }
  }
});

// Chroma with advanced filtering
const chromaQueryTool = createVectorQueryTool({
  vectorStoreName: "chroma",
  indexName: "documents",
  model: openai.embedding("text-embedding-3-small"),
  databaseConfig: {
    chroma: {
      where: { "category": "technical" },
      whereDocument: { "$contains": "API" }
    }
  }
});

// LanceDB with table specificity
const lanceQueryTool = createVectorQueryTool({
  vectorStoreName: "lance",
  indexName: "documents",
  model: openai.embedding("text-embedding-3-small"),
  databaseConfig: {
    lance: {
      tableName: "myVectors",     // Specify which table to query
      includeAllColumns: true     // Include all metadata columns in results
    }
  }
});
```

**Key Benefits:**
- **Pinecone namespaces**: Organize vectors by tenant, environment, or data type
- **pgVector optimization**: Control search accuracy and speed with ef/probes parameters
- **Quality filtering**: Set minimum similarity thresholds to improve result relevance
- **LanceDB tables**: Separate data into tables for better organization and performance
- **Runtime flexibility**: Override configurations dynamically based on context

**Common Use Cases:**
- Multi-tenant applications using Pinecone namespaces
- Performance optimization in high-load scenarios
- Environment-specific configurations (dev/staging/prod)
- Quality-gated search results
- Embedded, file-based vector storage with LanceDB for edge deployment scenarios

You can also override these configurations at runtime using the runtime context:

```ts showLineNumbers copy
import { RuntimeContext } from '@mastra/core/runtime-context';

const runtimeContext = new RuntimeContext();
runtimeContext.set('databaseConfig', {
  pinecone: {
    namespace: 'runtime-namespace'
  }
});

await pineconeQueryTool.execute({
  context: { queryText: 'search query' },
  mastra,
  runtimeContext
});
```

For detailed configuration options and advanced usage, see the [Vector Query Tool Reference](/reference/tools/vector-query-tool).

### Vector Store Prompts

Vector store prompts define query patterns and filtering capabilities for each vector database implementation.
When implementing filtering, these prompts are required in the agent's instructions to specify valid operators and syntax for each vector store implementation.

{/* 
LLM CONTEXT: This Tabs component displays vector store configuration examples for different database providers.
Each tab shows how to configure a RAG agent with the appropriate prompt for that specific vector store.
The tabs demonstrate the consistent pattern of importing the store-specific prompt and adding it to agent instructions.
This helps users understand how to properly configure their RAG agents for different vector database backends.
The providers include Pg Vector, Pinecone, Qdrant, Chroma, Astra, LibSQL, Upstash, Cloudflare, MongoDB, and OpenSearch.
*/}
<Tabs items={['Pg Vector', 'Pinecone', 'Qdrant', 'Chroma', 'Astra', 'LibSQL', 'Upstash', 'Cloudflare', 'MongoDB', 'OpenSearch']}>
  <Tabs.Tab>
```ts showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { PGVECTOR_PROMPT } from "@mastra/pg";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${PGVECTOR_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { PINECONE_PROMPT } from "@mastra/pinecone";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${PINECONE_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { QDRANT_PROMPT } from "@mastra/qdrant";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${QDRANT_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { CHROMA_PROMPT } from "@mastra/chroma";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${CHROMA_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { ASTRA_PROMPT } from "@mastra/astra";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${ASTRA_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { LIBSQL_PROMPT } from "@mastra/libsql";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${LIBSQL_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { UPSTASH_PROMPT } from "@mastra/upstash";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${UPSTASH_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { VECTORIZE_PROMPT } from "@mastra/vectorize";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${VECTORIZE_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { MONGODB_PROMPT } from "@mastra/mongodb";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${MONGODB_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

<Tabs.Tab>
```ts filename="vector-store.ts" showLineNumbers copy
import { openai } from '@ai-sdk/openai';
import { OPENSEARCH_PROMPT } from "@mastra/opensearch";

export const ragAgent = new Agent({
  name: 'RAG Agent',
  model: openai('gpt-4o-mini'),
  instructions: `
  Process queries using the provided context. Structure responses to be concise and relevant.
  ${OPENSEARCH_PROMPT}
  `,
  tools: { vectorQueryTool },
});
```
</Tabs.Tab>

</Tabs>

### Re-ranking

Initial vector similarity search can sometimes miss nuanced relevance. Re-ranking is a more computationally expensive process, but more accurate algorithm that improves results by:

- Considering word order and exact matches
- Applying more sophisticated relevance scoring
- Using a method called cross-attention between query and documents

Here's how to use re-ranking:

```ts showLineNumbers copy
import { openai } from "@ai-sdk/openai";
import { 
  rerankWithScorer as rerank, 
  MastraAgentRelevanceScorer 
} from "@mastra/rag";

// Get initial results from vector search
const initialResults = await pgVector.query({
  indexName: "embeddings",
  queryVector: queryEmbedding,
  topK: 10,
});

// Create a relevance scorer
const relevanceProvider = new MastraAgentRelevanceScorer('relevance-scorer', openai("gpt-4o-mini"));

// Re-rank the results
const rerankedResults = await rerank({
  results: initialResults,
  query,
  provider: relevanceProvider,
  options: {
    topK: 10,
  },
);
```

> **Note:** For semantic scoring to work properly during re-ranking, each result must include the text content in its `metadata.text` field.

You can also use other relevance score providers like Cohere or ZeroEntropy:

```ts showLineNumbers copy
const relevanceProvider = new CohereRelevanceScorer('rerank-v3.5');
```

```ts showLineNumbers copy
const relevanceProvider = new ZeroEntropyRelevanceScorer('zerank-1');
```

The re-ranked results combine vector similarity with semantic understanding to improve retrieval quality.

For more details about re-ranking, see the [rerank()](/reference/rag/rerankWithScorer) method.

For an example of how to use the re-ranking method, see the [Re-ranking Results](../../examples/rag/rerank/rerank.mdx) example.

### Graph-based Retrieval

For documents with complex relationships, graph-based retrieval can follow connections between chunks. This helps when:

- Information is spread across multiple documents
- Documents reference each other
- You need to traverse relationships to find complete answers

Example setup:

```ts showLineNumbers copy
const graphQueryTool = createGraphQueryTool({
  vectorStoreName: "pgVector",
  indexName: "embeddings",
  model: openai.embedding("text-embedding-3-small"),
  graphOptions: {
    threshold: 0.7,
  },
});
```

For more details about graph-based retrieval, see the [GraphRAG](/reference/rag/graph-rag) class and the [createGraphQueryTool()](/reference/tools/graph-rag-tool) function.

For an example of how to use the graph-based retrieval method, see the [Graph-based Retrieval](../../examples/rag/usage/graph-rag.mdx) example.
