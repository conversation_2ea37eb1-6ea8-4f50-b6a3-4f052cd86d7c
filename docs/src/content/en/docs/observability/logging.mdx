---
title: "Logging | Mastra Observability Documentation"
description: Documentation on effective logging in Mastra, crucial for understanding application behavior and improving AI accuracy.
---

import Image from "next/image";

# Logging

In Mastra, logs can detail when certain functions run, what input data they receive, and how they respond.

## Basic Setup

Here's a minimal example that sets up a **console logger** at the `INFO` level. This will print out informational messages and above (i.e., `DEBUG`, `INFO`, `WARN`, `ERROR`) to the console.

```typescript filename="mastra.config.ts" showLineNumbers copy
import { Mastra } from "@mastra/core";
import { PinoLogger } from "@mastra/loggers";

export const mastra = new Mastra({
  // Other Mastra configuration...
  logger: new PinoLogger({
    name: "Mastra",
    level: "info",
  }),
});
```

In this configuration:

- `name: "<PERSON><PERSON>"` specifies the name to group logs under.
- `level: "info"` sets the minimum severity of logs to record.

## Configuration

- For more details on the options you can pass to `<PERSON><PERSON>Logger()`, see the [PinoLogger reference documentation](/reference/observability/logger).
- Once you have a `Logger` instance, you can call its methods (e.g., `.info()`, `.warn()`, `.error()`) in the [Logger instance reference documentation](/reference/observability/logger).
- If you want to send your logs to an external service for centralized collection, analysis, or storage, you can configure other logger types such as Upstash Redis. Consult the [Logger reference documentation](/reference/observability/logger) for details on parameters like `url`, `token`, and `key` when using the `UPSTASH` logger type.
