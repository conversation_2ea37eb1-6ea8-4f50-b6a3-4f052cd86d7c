---
title: "Tracing | Mastra Observability Documentation"
description: "Set up OpenTelemetry tracing for Mastra applications"
---

import Image from "next/image";

# Tracing

<PERSON><PERSON> supports the OpenTelemetry Protocol (OTLP) for tracing and monitoring your application. When telemetry is enabled, <PERSON><PERSON> automatically traces all core primitives including agent operations, LLM interactions, tool executions, integration calls, workflow runs, and database operations. Your telemetry data can then be exported to any OTEL collector.

### Basic Configuration

Here's a simple example of enabling telemetry:

```ts filename="mastra.config.ts" showLineNumbers copy
export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "my-app",
    enabled: true,
    sampling: {
      type: "always_on",
    },
    export: {
      type: "otlp",
      endpoint: "http://localhost:4318", // SigNoz local endpoint
    },
  },
});
```

### Configuration Options

The telemetry config accepts these properties:

```ts
type OtelConfig = {
  // Name to identify your service in traces (optional)
  serviceName?: string;

  // Enable/disable telemetry (defaults to true)
  enabled?: boolean;

  // Control how many traces are sampled
  sampling?: {
    type: "ratio" | "always_on" | "always_off" | "parent_based";
    probability?: number; // For ratio sampling
    root?: {
      probability: number; // For parent_based sampling
    };
  };

  // Where to send telemetry data
  export?: {
    type: "otlp" | "console";
    endpoint?: string;
    headers?: Record<string, string>;
  };
};
```

See the [OtelConfig reference documentation](../../reference/observability/otel-config.mdx) for more details.

### Environment Variables

You can configure the OTLP endpoint and headers through environment variables:

```env filename=".env" copy
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
OTEL_EXPORTER_OTLP_HEADERS=x-api-key=your-api-key
```

Then in your config:

```ts filename="mastra.config.ts" showLineNumbers copy
export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "my-app",
    enabled: true,
    export: {
      type: "otlp",
      // endpoint and headers will be picked up from env vars
    },
  },
});
```

### Example: SigNoz Integration

Here's what a traced agent interaction looks like in [SigNoz](https://signoz.io):

<img
  src="/image/signoz-telemetry-demo.png"
  alt="Agent interaction trace showing spans, LLM calls, and tool executions"
  style={{ maxWidth: "800px", width: "100%", margin: "8px 0" }}
  className="nextra-image rounded-md"
  data-zoom
  width={800}
  height={400}
/>

### Other Supported Providers

For a complete list of supported observability providers and their configuration details, see the [Observability Providers reference](../../reference/observability/providers/).

### Custom Instrumentation files

You can define custom instrumentation files in your Mastra project by placing them in the `/mastra` folder. Mastra automatically detects and bundles these files instead of using the default instrumentation.

#### Supported File Types

Mastra looks for instrumentation files with these extensions:
- `instrumentation.js`
- `instrumentation.ts` 
- `instrumentation.mjs`

#### Example

```ts filename="/mastra/instrumentation.ts" showLineNumbers copy
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';

const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    url: 'http://localhost:4318/v1/traces',
  }),
  instrumentations: [getNodeAutoInstrumentations()],
});

sdk.start();
```

When Mastra finds a custom instrumentation file, it automatically replaces the default instrumentation and bundles it during the build process.

### Next.js-specific Tracing steps

If you're using Next.js, you have three additional configuration steps:

1. Enable the instrumentation hook in `next.config.ts`
2. Configure Mastra telemetry settings
3. Set up an OpenTelemetry exporter

For implementation details, see the [Next.js Tracing](./nextjs-tracing) guide.
