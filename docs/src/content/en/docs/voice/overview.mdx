---
title: Voice in Mastra | Mastra Docs
description: Overview of voice capabilities in Mastra, including text-to-speech, speech-to-text, and real-time speech-to-speech interactions.
---

import { Tabs } from "nextra/components";
import { AudioPlayback } from "@/components/audio-playback";

# Voice in Mastra

Mastra's Voice system provides a unified interface for voice interactions, enabling text-to-speech (TTS), speech-to-text (STT), and real-time speech-to-speech (STS) capabilities in your applications.

## Adding Voice to Agents

To learn how to integrate voice capabilities into your agents, check out the [Adding Voice to Agents](../agents/adding-voice.mdx) documentation. This section covers how to use both single and multiple voice providers, as well as real-time interactions.

```typescript
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { OpenAIVoice } from "@mastra/voice-openai";

// Initialize OpenAI voice for TTS

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions:
    "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new OpenAIVoice(),
});
```

You can then use the following voice capabilities:

### Text to Speech (TTS)

Turn your agent's responses into natural-sounding speech using Mastra's TTS capabilities.
Choose from multiple providers like OpenAI, ElevenLabs, and more.

For detailed configuration options and advanced features, check out our [Text-to-Speech guide](./text-to-speech).

{/*
LLM CONTEXT: This Tabs component demonstrates Text-to-Speech (TTS) implementation across different voice providers.
Each tab shows how to set up and use a specific TTS provider (OpenAI, Azure, ElevenLabs, etc.) with Mastra agents.
The tabs help users compare different TTS providers and choose the one that best fits their needs.
Each tab includes complete code examples showing agent setup, text generation, and audio playback.
The providers include OpenAI, Azure, ElevenLabs, PlayAI, Google, Cloudflare, Deepgram, Speechify, Sarvam, and Murf.
*/}

<Tabs items={["OpenAI", "Azure", "ElevenLabs", "PlayAI", "Google", "Cloudflare", "Deepgram", "Speechify", "Sarvam", "Murf"]}>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { OpenAIVoice } from "@mastra/voice-openai";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new OpenAIVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
speaker: "default", // Optional: specify a speaker
responseFormat: "wav", // Optional: specify a response format
});

playAudio(audioStream);

````

Visit the [OpenAI Voice Reference](/reference/voice/openai) for more information on the OpenAI voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { AzureVoice } from "@mastra/voice-azure";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new AzureVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
  speaker: "en-US-JennyNeural", // Optional: specify a speaker
});

playAudio(audioStream);
````

Visit the [Azure Voice Reference](/reference/voice/azure) for more information on the Azure voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { ElevenLabsVoice } from "@mastra/voice-elevenlabs";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new ElevenLabsVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
speaker: "default", // Optional: specify a speaker
});

playAudio(audioStream);

````

Visit the [ElevenLabs Voice Reference](/reference/voice/elevenlabs) for more information on the ElevenLabs voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { PlayAIVoice } from "@mastra/voice-playai";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new PlayAIVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
  speaker: "default", // Optional: specify a speaker
});

playAudio(audioStream);
````

Visit the [PlayAI Voice Reference](/reference/voice/playai) for more information on the PlayAI voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { GoogleVoice } from "@mastra/voice-google";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new GoogleVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
speaker: "en-US-Studio-O", // Optional: specify a speaker
});

playAudio(audioStream);

````

Visit the [Google Voice Reference](/reference/voice/google) for more information on the Google voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { CloudflareVoice } from "@mastra/voice-cloudflare";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new CloudflareVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
  speaker: "default", // Optional: specify a speaker
});

playAudio(audioStream);
````

Visit the [Cloudflare Voice Reference](/reference/voice/cloudflare) for more information on the Cloudflare voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { DeepgramVoice } from "@mastra/voice-deepgram";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new DeepgramVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
speaker: "aura-english-us", // Optional: specify a speaker
});

playAudio(audioStream);

````

Visit the [Deepgram Voice Reference](/reference/voice/deepgram) for more information on the Deepgram voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { SpeechifyVoice } from "@mastra/voice-speechify";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new SpeechifyVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
  speaker: "matthew", // Optional: specify a speaker
});

playAudio(audioStream);
````

Visit the [Speechify Voice Reference](/reference/voice/speechify) for more information on the Speechify voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { SarvamVoice } from "@mastra/voice-sarvam";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new SarvamVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
speaker: "default", // Optional: specify a speaker
});

playAudio(audioStream);

````

Visit the [Sarvam Voice Reference](/reference/voice/sarvam) for more information on the Sarvam voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { MurfVoice } from "@mastra/voice-murf";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new MurfVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
  speaker: "default", // Optional: specify a speaker
});

playAudio(audioStream);
````

Visit the [Murf Voice Reference](/reference/voice/murf) for more information on the Murf voice provider.

  </Tabs.Tab>
</Tabs>

### Speech to Text (STT)

Transcribe spoken content using various providers like OpenAI, ElevenLabs, and more. For detailed configuration options and more, check out [Speech to Text](./speech-to-text).

You can download a sample audio file from [here](https://github.com/mastra-ai/realtime-voice-demo/raw/refs/heads/main/how_can_i_help_you.mp3).

<br />
<AudioPlayback audio="https://github.com/mastra-ai/realtime-voice-demo/raw/refs/heads/main/how_can_i_help_you.mp3" />

{/*
LLM CONTEXT: This Tabs component demonstrates Speech-to-Text (STT) implementation across different voice providers.
Each tab shows how to set up and use a specific STT provider for transcribing audio to text.
The tabs help users understand how to implement speech recognition with different providers.
Each tab includes code examples showing audio file handling, transcription, and response generation.
*/}

<Tabs items={["OpenAI", "Azure", "ElevenLabs", "Google", "Cloudflare", "Deepgram", "Sarvam"]}>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { OpenAIVoice } from "@mastra/voice-openai";
import { createReadStream } from 'fs';

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new OpenAIVoice(),
});

// Use an audio file from a URL
const audioStream = await createReadStream("./how_can_i_help_you.mp3");

// Convert audio to text
const transcript = await voiceAgent.voice.listen(audioStream);
console.log(`User said: ${transcript}`);

// Generate a response based on the transcript
const { text } = await voiceAgent.generate(transcript);

````

Visit the [OpenAI Voice Reference](/reference/voice/openai) for more information on the OpenAI voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { createReadStream } from 'fs';
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { AzureVoice } from "@mastra/voice-azure";
import { createReadStream } from 'fs';

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new AzureVoice(),
});

// Use an audio file from a URL
const audioStream = await createReadStream("./how_can_i_help_you.mp3");

// Convert audio to text
const transcript = await voiceAgent.voice.listen(audioStream);
console.log(`User said: ${transcript}`);

// Generate a response based on the transcript
const { text } = await voiceAgent.generate(transcript);
````

Visit the [Azure Voice Reference](/reference/voice/azure) for more information on the Azure voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { ElevenLabsVoice } from "@mastra/voice-elevenlabs";
import { createReadStream } from 'fs';

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new ElevenLabsVoice(),
});

// Use an audio file from a URL
const audioStream = await createReadStream("./how_can_i_help_you.mp3");

// Convert audio to text
const transcript = await voiceAgent.voice.listen(audioStream);
console.log(`User said: ${transcript}`);

// Generate a response based on the transcript
const { text } = await voiceAgent.generate(transcript);

````

Visit the [ElevenLabs Voice Reference](/reference/voice/elevenlabs) for more information on the ElevenLabs voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { GoogleVoice } from "@mastra/voice-google";
import { createReadStream } from 'fs';

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new GoogleVoice(),
});

// Use an audio file from a URL
const audioStream = await createReadStream("./how_can_i_help_you.mp3");

// Convert audio to text
const transcript = await voiceAgent.voice.listen(audioStream);
console.log(`User said: ${transcript}`);

// Generate a response based on the transcript
const { text } = await voiceAgent.generate(transcript);
````

Visit the [Google Voice Reference](/reference/voice/google) for more information on the Google voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { CloudflareVoice } from "@mastra/voice-cloudflare";
import { createReadStream } from 'fs';

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new CloudflareVoice(),
});

// Use an audio file from a URL
const audioStream = await createReadStream("./how_can_i_help_you.mp3");

// Convert audio to text
const transcript = await voiceAgent.voice.listen(audioStream);
console.log(`User said: ${transcript}`);

// Generate a response based on the transcript
const { text } = await voiceAgent.generate(transcript);

````

Visit the [Cloudflare Voice Reference](/reference/voice/cloudflare) for more information on the Cloudflare voice provider.
  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { DeepgramVoice } from "@mastra/voice-deepgram";
import { createReadStream } from 'fs';

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new DeepgramVoice(),
});

// Use an audio file from a URL
const audioStream = await createReadStream("./how_can_i_help_you.mp3");

// Convert audio to text
const transcript = await voiceAgent.voice.listen(audioStream);
console.log(`User said: ${transcript}`);

// Generate a response based on the transcript
const { text } = await voiceAgent.generate(transcript);
````

Visit the [Deepgram Voice Reference](/reference/voice/deepgram) for more information on the Deepgram voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { SarvamVoice } from "@mastra/voice-sarvam";
import { createReadStream } from 'fs';

const voiceAgent = new Agent({
name: "Voice Agent",
instructions: "You are a voice assistant that can help users with their tasks.",
model: openai("gpt-4o"),
voice: new SarvamVoice(),
});

// Use an audio file from a URL
const audioStream = await createReadStream("./how_can_i_help_you.mp3");

// Convert audio to text
const transcript = await voiceAgent.voice.listen(audioStream);
console.log(`User said: ${transcript}`);

// Generate a response based on the transcript
const { text } = await voiceAgent.generate(transcript);

````

Visit the [Sarvam Voice Reference](/reference/voice/sarvam) for more information on the Sarvam voice provider.
  </Tabs.Tab>
</Tabs>

### Speech to Speech (STS)

Create conversational experiences with speech-to-speech capabilities. The unified API enables real-time voice interactions between users and AI agents.
For detailed configuration options and advanced features, check out [Speech to Speech](./speech-to-speech).

{/*
  LLM CONTEXT: This Tabs component demonstrates Speech-to-Speech (STS) implementation for real-time voice interactions.
  Currently only shows OpenAI's realtime voice implementation for bidirectional voice conversations.
  The tab shows how to set up real-time voice communication with event handling for audio responses.
  This enables conversational AI experiences with continuous audio streaming.
*/}
<Tabs items={["OpenAI"]}>
  <Tabs.Tab>
```typescript
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { playAudio, getMicrophoneStream } from '@mastra/node-audio';
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new OpenAIRealtimeVoice(),
});

// Listen for agent audio responses
voiceAgent.voice.on('speaker', ({ audio }) => {
  playAudio(audio);
});

// Initiate the conversation
await voiceAgent.voice.speak('How can I help you today?');

// Send continuous audio from the microphone
const micStream = getMicrophoneStream();
await voiceAgent.voice.send(micStream);
````

Visit the [OpenAI Voice Reference](/reference/voice/openai-realtime) for more information on the OpenAI voice provider.

  </Tabs.Tab>
</Tabs>

## Voice Configuration

Each voice provider can be configured with different models and options. Below are the detailed configuration options for all supported providers:

{/*
LLM CONTEXT: This Tabs component shows detailed configuration options for all supported voice providers.
Each tab demonstrates how to configure a specific voice provider with all available options and settings.
The tabs help users understand the full configuration capabilities of each provider including models, languages, and advanced settings.
Each tab shows both speech and listening model configurations where applicable.
*/}

<Tabs items={["OpenAI", "Azure", "ElevenLabs", "PlayAI", "Google", "Cloudflare", "Deepgram", "Speechify", "Sarvam", "Murf", "OpenAI Realtime"]}>
  <Tabs.Tab>
```typescript
// OpenAI Voice Configuration
const voice = new OpenAIVoice({
  speechModel: {
    name: "gpt-3.5-turbo", // Example model name
    apiKey: process.env.OPENAI_API_KEY,
    language: "en-US", // Language code
    voiceType: "neural", // Type of voice model
  },
  listeningModel: {
    name: "whisper-1", // Example model name
    apiKey: process.env.OPENAI_API_KEY,
    language: "en-US", // Language code
    format: "wav", // Audio format
  },
  speaker: "alloy", // Example speaker name
});
```

Visit the [OpenAI Voice Reference](/reference/voice/openai) for more information on the OpenAI voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// Azure Voice Configuration
const voice = new AzureVoice({
  speechModel: {
    name: "en-US-JennyNeural", // Example model name
    apiKey: process.env.AZURE_SPEECH_KEY,
    region: process.env.AZURE_SPEECH_REGION,
    language: "en-US", // Language code
    style: "cheerful", // Voice style
    pitch: "+0Hz", // Pitch adjustment
    rate: "1.0", // Speech rate
  },
  listeningModel: {
    name: "en-US", // Example model name
    apiKey: process.env.AZURE_SPEECH_KEY,
    region: process.env.AZURE_SPEECH_REGION,
    format: "simple", // Output format
  },
});
```

Visit the [Azure Voice Reference](/reference/voice/azure) for more information on the Azure voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// ElevenLabs Voice Configuration
const voice = new ElevenLabsVoice({
  speechModel: {
    voiceId: "your-voice-id", // Example voice ID
    model: "eleven_multilingual_v2", // Example model name
    apiKey: process.env.ELEVENLABS_API_KEY,
    language: "en", // Language code
    emotion: "neutral", // Emotion setting
  },
  // ElevenLabs may not have a separate listening model
});
```

Visit the [ElevenLabs Voice Reference](/reference/voice/elevenlabs) for more information on the ElevenLabs voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// PlayAI Voice Configuration
const voice = new PlayAIVoice({
  speechModel: {
    name: "playai-voice", // Example model name
    speaker: "emma", // Example speaker name
    apiKey: process.env.PLAYAI_API_KEY,
    language: "en-US", // Language code
    speed: 1.0, // Speech speed
  },
  // PlayAI may not have a separate listening model
});
```

Visit the [PlayAI Voice Reference](/reference/voice/playai) for more information on the PlayAI voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// Google Voice Configuration
const voice = new GoogleVoice({
  speechModel: {
    name: "en-US-Studio-O", // Example model name
    apiKey: process.env.GOOGLE_API_KEY,
    languageCode: "en-US", // Language code
    gender: "FEMALE", // Voice gender
    speakingRate: 1.0, // Speaking rate
  },
  listeningModel: {
    name: "en-US", // Example model name
    sampleRateHertz: 16000, // Sample rate
  },
});
```

Visit the [PlayAI Voice Reference](/reference/voice/playai) for more information on the PlayAI voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// Cloudflare Voice Configuration
const voice = new CloudflareVoice({
  speechModel: {
    name: "cloudflare-voice", // Example model name
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
    apiToken: process.env.CLOUDFLARE_API_TOKEN,
    language: "en-US", // Language code
    format: "mp3", // Audio format
  },
  // Cloudflare may not have a separate listening model
});
```

Visit the [Cloudflare Voice Reference](/reference/voice/cloudflare) for more information on the Cloudflare voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// Deepgram Voice Configuration
const voice = new DeepgramVoice({
  speechModel: {
    name: "nova-2", // Example model name
    speaker: "aura-english-us", // Example speaker name
    apiKey: process.env.DEEPGRAM_API_KEY,
    language: "en-US", // Language code
    tone: "formal", // Tone setting
  },
  listeningModel: {
    name: "nova-2", // Example model name
    format: "flac", // Audio format
  },
});
```

Visit the [Deepgram Voice Reference](/reference/voice/deepgram) for more information on the Deepgram voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// Speechify Voice Configuration
const voice = new SpeechifyVoice({
  speechModel: {
    name: "speechify-voice", // Example model name
    speaker: "matthew", // Example speaker name
    apiKey: process.env.SPEECHIFY_API_KEY,
    language: "en-US", // Language code
    speed: 1.0, // Speech speed
  },
  // Speechify may not have a separate listening model
});
```

Visit the [Speechify Voice Reference](/reference/voice/speechify) for more information on the Speechify voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// Sarvam Voice Configuration
const voice = new SarvamVoice({
  speechModel: {
    name: "sarvam-voice", // Example model name
    apiKey: process.env.SARVAM_API_KEY,
    language: "en-IN", // Language code
    style: "conversational", // Style setting
  },
  // Sarvam may not have a separate listening model
});
```

Visit the [Sarvam Voice Reference](/reference/voice/sarvam) for more information on the Sarvam voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// Murf Voice Configuration
const voice = new MurfVoice({
  speechModel: {
    name: "murf-voice", // Example model name
    apiKey: process.env.MURF_API_KEY,
    language: "en-US", // Language code
    emotion: "happy", // Emotion setting
  },
  // Murf may not have a separate listening model
});
```

Visit the [Murf Voice Reference](/reference/voice/murf) for more information on the Murf voice provider.

  </Tabs.Tab>
  <Tabs.Tab>
```typescript
// OpenAI Realtime Voice Configuration
const voice = new OpenAIRealtimeVoice({
  speechModel: {
    name: "gpt-3.5-turbo", // Example model name
    apiKey: process.env.OPENAI_API_KEY,
    language: "en-US", // Language code
  },
  listeningModel: {
    name: "whisper-1", // Example model name
    apiKey: process.env.OPENAI_API_KEY,
    format: "ogg", // Audio format
  },
  speaker: "alloy", // Example speaker name
});
```

For more information on the OpenAI Realtime voice provider, refer to the [OpenAI Realtime Voice Reference](/reference/voice/openai-realtime).

  </Tabs.Tab>
</Tabs>

### Using Multiple Voice Providers

This example demonstrates how to create and use two different voice providers in Mastra: OpenAI for speech-to-text (STT) and PlayAI for text-to-speech (TTS).

Start by creating instances of the voice providers with any necessary configuration.

```typescript
import { OpenAIVoice } from "@mastra/voice-openai";
import { PlayAIVoice } from "@mastra/voice-playai";
import { CompositeVoice } from "@mastra/core/voice";
import { playAudio, getMicrophoneStream } from "@mastra/node-audio";

// Initialize OpenAI voice for STT
const input = new OpenAIVoice({
  listeningModel: {
    name: "whisper-1",
    apiKey: process.env.OPENAI_API_KEY,
  },
});

// Initialize PlayAI voice for TTS
const output = new PlayAIVoice({
  speechModel: {
    name: "playai-voice",
    apiKey: process.env.PLAYAI_API_KEY,
  },
});

// Combine the providers using CompositeVoice
const voice = new CompositeVoice({
  input,
  output,
});

// Implement voice interactions using the combined voice provider
const audioStream = getMicrophoneStream(); // Assume this function gets audio input
const transcript = await voice.listen(audioStream);

// Log the transcribed text
console.log("Transcribed text:", transcript);

// Convert text to speech
const responseAudio = await voice.speak(`You said: ${transcript}`, {
  speaker: "default", // Optional: specify a speaker,
  responseFormat: "wav", // Optional: specify a response format
});

// Play the audio response
playAudio(responseAudio);
```

For more information on the CompositeVoice, refer to the [CompositeVoice Reference](/reference/voice/composite-voice).

## More Resources

- [CompositeVoice](../../reference/voice/composite-voice.mdx)
- [MastraVoice](../../reference/voice/mastra-voice.mdx)
- [OpenAI Voice](../../reference/voice/openai.mdx)
- [Azure Voice](../../reference/voice/azure.mdx)
- [Google Voice](../../reference/voice/google.mdx)
- [Deepgram Voice](../../reference/voice/deepgram.mdx)
- [PlayAI Voice](../../reference/voice/playai.mdx)
- [Voice Examples](../../examples/voice/text-to-speech.mdx)
