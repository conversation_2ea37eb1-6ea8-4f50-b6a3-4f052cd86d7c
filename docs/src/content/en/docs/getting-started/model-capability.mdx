# Model Capabilities

import { ProviderTable } from "@/components/provider-table";

The AI providers support different language models with various capabilities. Not all models support structured output, image input, object generation, tool usage, or tool streaming.

Here are the capabilities of popular models:

<ProviderTable />

Source: [AI SDK | Model Capabilities](https://sdk.vercel.ai/docs/foundations/providers-and-models#model-capabilities)
