---
title: "Using with Cursor/Windsurf | Getting Started | Mastra Docs"
description: "Learn how to use the Mastra MCP documentation server in your IDE to turn it into an agentic Mastra expert."
---

import YouTube from "@/components/youtube";
import { Tabs } from "nextra/components";

# Mastra Tools for your agentic IDE

`@mastra/mcp-docs-server` provides direct access to Mastra's complete knowledge base in Cursor, Windsurf, Cline, or any other IDE that supports MCP.

It has access to documentation, code examples, technical blog posts / feature announcements, and package changelogs which your IDE can read to help you build with <PERSON><PERSON>.

<YouTube id="vciV57lF0og" />

The MCP server tools have been designed to allow an agent to query the specific information it needs to complete a Mastra related task - for example: adding a Mastra feature to an agent, scaffolding a new project, or helping you understand how something works.

## How it works

Once it's installed in your IDE you can write prompts and assume the agent will understand everything about Mastra.

### Add features

- "Add evals to my agent and write tests"
- "Write me a workflow that does the following `[task]`"
- "Make a new tool that allows my agent to access `[3rd party API]`"

### Ask about integrations

- "Does <PERSON><PERSON> work with the AI SDK?
  How can I use it in my `[React/Svelte/etc]` project?"
- "What's the latest Mastra news around MCP?"
- "Does Mastra support `[provider]` speech and voice APIs? Show me an example in my code of how I can use it."

### Debug or update existing code

- "I'm running into a bug with agent memory, have there been any related changes or bug fixes recently?"
- "How does working memory behave in Mastra and how can I use it to do `[task]`? It doesn't seem to work the way I expect."
- "I saw there are new workflow features, explain them to me and then update `[workflow]` to use them."

**And more** - if you have a question, try asking your IDE and let it look it up for you.

## Automatic Installation

For **new** projects, the MCP Docs Server can be added during installation either through the [interactive](/docs/getting-started/installation#interactive) setup prompts, or by specifying the `-m` flag using the [non-interactive](/docs/getting-started/installation#non-interactive) command.

## Manual Installation

To add the MCP Docs Server to an existing project, install it manually.

- **Cursor**: Edit `.cursor/mcp.json` in your project root, or `~/.cursor/mcp.json` for global configuration
- **Windsurf**: Edit `~/.codeium/windsurf/mcp_config.json` (only supports global configuration)
- **VSCode**: Edit `~/.vscode/mcp.json` in your project root
  Add the following configuration:

### MacOS/Linux

{/*
LLM CONTEXT: This Tabs component shows MCP server configuration for different IDEs on MacOS/Linux.
Each tab displays the JSON configuration needed to set up the Mastra MCP docs server in that specific IDE.
The tabs help users find the correct configuration format for their IDE (Cursor, Windsurf, or VSCode).
Each tab shows the exact JSON structure and file paths needed for that IDE's MCP configuration.
*/}

<Tabs items={["cursor", "windsurf", "vscode"]}>
  <Tabs.Tab>
```json
{
  "mcpServers": {
    "mastra": {
      "command": "npx",
      "args": ["-y", "@mastra/mcp-docs-server"]
    }
  }
}
```
  </Tabs.Tab>
  <Tabs.Tab>
```json
{
  "mcpServers": {
    "mastra": {
      "command": "npx",
      "args": ["-y", "@mastra/mcp-docs-server"]
    }
  }
}
```
  </Tabs.Tab>
  <Tabs.Tab>
```json
{
  "servers": {
    "mastra": {
      "command": "npx",
      "args": ["-y", "@mastra/mcp-docs-server"],
      "type": "stdio"
    }
  }
}
```
  </Tabs.Tab>
</Tabs>

### Windows

{/*
LLM CONTEXT: This Tabs component shows MCP server configuration for different IDEs on Windows.
Each tab displays the Windows-specific JSON configuration needed to set up the Mastra MCP docs server.
The tabs help Windows users find the correct configuration format for their IDE, using cmd instead of direct npx.
Each tab shows the Windows-specific command structure needed for that IDE's MCP configuration.
On latest Windsurf and Cursor the direct npx command works, while it's still unconfirmed if this has been fixed for VSCode.
*/}

<Tabs items={["cursor", "windsurf", "vscode"]}>
  <Tabs.Tab>
```json
{
  "mcpServers": {
    "mastra": {
      "command": "npx",
      "args": ["-y", "@mastra/mcp-docs-server"]
    }
  }
}
```
  </Tabs.Tab>
  <Tabs.Tab>
```json
{
  "mcpServers": {
    "mastra": {
      "command": "npx",
      "args": ["-y", "@mastra/mcp-docs-server"]
    }
  }
}
```
  </Tabs.Tab>
  <Tabs.Tab>
```json
{
  "servers": {
    "mastra": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@mastra/mcp-docs-server"],
      "type": "stdio"
    }
  }
}
```
  </Tabs.Tab>
</Tabs>

## After Configuration

### Cursor

If you followed the automatic installation, you'll see a popup when you open cursor in the bottom left corner to prompt you to enable the Mastra Docs MCP Server.

<img
  src="/image/enable-mastra-docs-cursor.png"
  alt="Diagram showing cursor prompt to enable Mastra docs MCP server"
  width={800}
/>

Otherwise, for manual installation, do the following.

1. Open Cursor settings
2. Navigate to MCP settings
3. Click "enable" on the Mastra MCP server
4. If you have an agent chat open, you'll need to re-open it or start a new chat to use the MCP server

### Windsurf

1. Fully quit and re-open Windsurf
2. If tool calls start failing, go to Windsurfs MCP settings and re-start the MCP server. This is a common Windsurf MCP issue and isn't related to Mastra. Right now Cursor's MCP implementation is more stable than Windsurfs is.

In both IDEs it may take a minute for the MCP server to start the first time as it needs to download the package from npm.

### VSCode

1. Open VSCode settings
2. Navigate to MCP settings
3. Click "enable" on the Chat > MCP option

<br />

<img
  src="/image/vscode-mcp-setting.png"
  alt="Settings page of VSCode to enable MCP"
  width={800}
  className="rounded-lg"
/>

MCP only works in Agent mode in VSCode. Once you are in agent mode, open the `mcp.json` file and click the "start" button.

<br />
<img
  src="/image/vscode-start-mcp.png"
  alt="Settings page of VSCode to enable MCP"
  width={800}
  className="rounded-lg"
/>

After starting the mcp server, click the tools button in the copilot pane to see available tools.

<br />
<img
  src="/image/vscode-mcp-running.png"
  alt="Tools page of VSCode to see available tools"
  width={800}
  className="rounded-lg"
/>

## Available Agent Tools

### Documentation

Access Mastra's complete documentation:

- Getting started / installation
- Guides and tutorials
- API references

### Examples

Browse code examples:

- Complete project structures
- Implementation patterns
- Best practices

### Blog Posts

Search the blog for:

- Technical posts
- Changelog and feature announcements
- AI news and updates

### Package Changes

Track updates for Mastra and `@mastra/*` packages:

- Bug fixes
- New features
- Breaking changes

## Common Issues

1. **Server Not Starting**

   - Ensure npx is installed and working
   - Check for conflicting MCP servers
   - Verify your configuration file syntax
   - On Windows, make sure to use the Windows-specific configuration

2. **Tool Calls Failing**
   - Restart the MCP server and/or your IDE
   - Update to the latest version of your IDE
