---
title: "Installing <PERSON>stra | Getting Started | Mastra Docs"
description: Guide on installing Mastra and setting up the necessary prerequisites for running it with various LLM providers.
---

import { Callout, Steps } from "nextra/components";
import { Tabs, Tab } from "@/components/tabs";

# Install Mastra

To get started with <PERSON><PERSON>, you’ll need access to a large language model (LLM). By default, <PERSON><PERSON> is set up to work with [OpenAI](https://platform.openai.com/), so you’ll need an API key to begin.

Mastra also supports other LLM providers. For a full list of supported models and setup instructions, see [Model Providers](/docs/getting-started/model-providers).


## Prerequisites

- Node.js `v20.0` or higher
- An API key from a supported [Model Provider](/docs/getting-started/model-providers)

<Steps>

## Install using the `create-mastra` CLI

Our CLI is the fastest way to get started with <PERSON><PERSON>. Run the following command to start the interactive setup:

{/*
LLM CONTEXT: This Tabs component shows different package manager commands for creating a new Mastra project.
Each tab displays the equivalent command for that specific package manager (npx, npm, yarn, pnpm, bun).
This helps users choose their preferred package manager while following the same installation process.
All commands achieve the same result - creating a new Mastra project with the interactive setup.
*/}

<Tabs items={["npx", "npm", "yarn", "pnpm", "bun"]}>
  <Tab>
    ```bash copy
    npx create-mastra@latest
    ```
  </Tab>
  <Tab>
    ```bash copy
    npm create mastra@latest
    ```
  </Tab>
  <Tab>
    ```bash copy
    yarn create mastra@latest
    ```
  </Tab>
  <Tab>
    ```bash copy
    pnpm create mastra@latest
    ```
  </Tab>
  <Tab>
    ```bash copy
    bun create mastra@latest
    ```
  </Tab>
</Tabs>


**Install using CLI flags**

You can also run the Mastra CLI in non-interactive mode by passing all required flags, for example:

```bash copy
npx create-mastra@latest --project-name hello-mastra --example --components tools,agents,workflows --llm openai
```

> See the [create-mastra](/reference/cli/create-mastra) documentation for a full list of available CLI options.

### Add your API key

Add your API key to the `.env` file:

```bash filename=".env" copy
OPENAI_API_KEY=<your-api-key>
```
> This example uses OpenAI. Each LLM provider uses a unique name. See [Model Capabilities](/docs/getting-started/model-capability) for more information.

You can now launch the [Mastra Development Server](/docs/server-db/local-dev-playground) and test your agent using the Mastra Playground.

</Steps>

## Install manually

The following steps will walk you through installing Mastra manually.

<Steps>

### Create a new project

Create a new project and change directory:

```bash copy
mkdir hello-mastra && cd hello-mastra
```

Initialize a TypeScript project including the `@mastra/core` package:

{/*
LLM CONTEXT: This Tabs component shows manual installation commands for different package managers.
Each tab displays the complete setup process for that package manager including project initialization,
dev dependencies installation, and core Mastra packages installation.
This helps users manually set up a Mastra project with their preferred package manager.
*/}

<Tabs items={["npm", "pnpm", "yarn", "bun"]}>

  <Tab>
    ```bash copy
    npm init -y

    npm install typescript tsx @types/node mastra@latest --save-dev

    npm install @mastra/core@latest zod@^3 @ai-sdk/openai
    ```

  </Tab>
  <Tab>
    ```bash copy
    pnpm init

    pnpm add typescript tsx @types/node mastra@latest --save-dev

    pnpm add @mastra/core@latest zod@^3 @ai-sdk/openai
    ```

  </Tab>
  <Tab>
    ```bash copy
    yarn init -y

    yarn add typescript tsx @types/node mastra@latest --dev

    yarn add @mastra/core@latest zod@^3 @ai-sdk/openai
    ```

  </Tab>
  <Tab>
    ```bash copy
    bun init -y

    bun add typescript tsx @types/node mastra@latest --dev

    bun add @mastra/core@latest zod@^3 @ai-sdk/openai
    ```

  </Tab>
</Tabs>

Add the `dev` and `build` scripts to `package.json`:

```json filename="package.json" copy
{
  "scripts": {
    // ...
    "dev": "mastra dev",
    "build": "mastra build"
  }
}
```

### Initialize TypeScript

Create a `tsconfig.json` file:

```bash copy
touch tsconfig.json
```

Add the following configuration:

```json filename="tsconfig.json" copy
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "bundler",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "noEmit": true,
    "outDir": "dist"
  },
  "include": [
    "src/**/*"
  ]
}
```

> This TypeScript configuration is optimized for Mastra projects, using modern module resolution and strict type checking.

### Set up your API key

Create `.env` file:

```bash copy
touch .env
```

Add your API key:

```bash filename=".env" copy
OPENAI_API_KEY=<your-api-key>
```

> This example uses OpenAI. Each LLM provider uses a unique name. See [Model Capabilities](/docs/getting-started/model-capability) for more information.

### Create a Tool

Create a `weather-tool.ts` file:

```bash copy
mkdir -p src/mastra/tools && touch src/mastra/tools/weather-tool.ts
```

Add the following code:

```ts filename="src/mastra/tools/weather-tool.ts" showLineNumbers copy
import { createTool } from "@mastra/core/tools";
import { z } from "zod";

export const weatherTool = createTool({
  id: "get-weather",
  description: "Get current weather for a location",
  inputSchema: z.object({
    location: z.string().describe("City name")
  }),
  outputSchema: z.object({
    output: z.string()
  }),
  execute: async () => {
    return {
      output: "The weather is sunny"
    };
  }
});
```

> See the full weatherTool example in [Giving an Agent a Tool](/examples/agents/using-a-tool).

### Create an Agent

Create a `weather-agent.ts` file:

```bash copy
mkdir -p src/mastra/agents && touch src/mastra/agents/weather-agent.ts
```

Add the following code:

```ts filename="src/mastra/agents/weather-agent.ts" showLineNumbers copy
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { weatherTool } from "../tools/weather-tool";

export const weatherAgent = new Agent({
  name: 'Weather Agent',
  instructions: `
      You are a helpful weather assistant that provides accurate weather information.

      Your primary function is to help users get weather details for specific locations. When responding:
      - Always ask for a location if none is provided
      - If the location name isn’t in English, please translate it
      - If giving a location with multiple parts (e.g. "New York, NY"), use the most relevant part (e.g. "New York")
      - Include relevant details like humidity, wind conditions, and precipitation
      - Keep responses concise but informative

      Use the weatherTool to fetch current weather data.
`,
  model: openai('gpt-4o-mini'),
  tools: { weatherTool }
});
```

### Register the Agent

Create the Mastra entry point and register agent:

```bash copy
touch src/mastra/index.ts
```

Add the following code:

```ts filename="src/mastra/index.ts" showLineNumbers copy
import { Mastra } from "@mastra/core/mastra";
import { weatherAgent } from "./agents/weather-agent";

export const mastra = new Mastra({
  agents: { weatherAgent }
});
```

You can now launch the [Mastra Development Server](/docs/server-db/local-dev-playground) and test your agent using the Mastra Playground.

</Steps>

## Add to an existing project

Mastra can be installed and integrated into a wide range of projects. Below are links to integration guides to help you get started:

- [Next.js](/docs/frameworks/web-frameworks/next-js)
- [Vite + React](/docs/frameworks/web-frameworks/vite-react)
- [Astro](/docs/frameworks/web-frameworks/astro)
- [Express](/docs/frameworks/servers/express)


### `mastra init`

To install Mastra in an existing project, use the `mastra init` command.

> See [mastra init](/reference/cli/init) for more information.

## Next steps

- [Local Development](/docs/server-db/local-dev-playground)
- [Deploy to Mastra Cloud](/docs/deployment/overview)


/docs/server-db/local-dev-playground
