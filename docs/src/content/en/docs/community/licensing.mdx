---
title: "Licensing"
description: "Mastra License"
---

# License

## Apache License 2.0

Mastra is licensed under the Apache License 2.0, a permissive open-source license that provides users with broad rights to use, modify, and distribute the software.

### What is Apache License 2.0?

The Apache License 2.0 is a permissive open-source license that grants users extensive rights to use, modify, and distribute the software. It allows:

- Free use for any purpose, including commercial use
- Viewing, modifying, and redistributing the source code
- Creating and distributing derivative works
- Commercial use without restrictions
- Patent protection from contributors

The Apache License 2.0 is one of the most permissive and business-friendly open-source licenses available.

### Why We Chose Apache License 2.0

We selected the Apache License 2.0 for several important reasons:

1. **True Open Source**: It's a recognized open-source license that aligns with open-source principles and community expectations.

2. **Business Friendly**: It allows for unrestricted commercial use and distribution, making it ideal for businesses of all sizes.

3. **Patent Protection**: It includes explicit patent protection for users, providing additional legal security.

4. **Community Focus**: It encourages community contributions and collaboration without restrictions.

5. **Widely Adopted**: It's one of the most popular and well-understood open-source licenses in the industry.

### Building Your Business with Mastra

The Apache License 2.0 provides maximum flexibility for building businesses with Mastra:

#### Allowed Business Models

- **Building Applications**: Create and sell applications built with Mastra
- **Offering Consulting Services**: Provide expertise, implementation, and customization services
- **Developing Custom Solutions**: Build bespoke AI solutions for clients using Mastra
- **Creating Add-ons and Extensions**: Develop and sell complementary tools that extend Mastra's functionality
- **Training and Education**: Offer courses and educational materials about using Mastra effectively
- **Hosted Services**: Offer Mastra as a hosted or managed service
- **SaaS Platforms**: Build SaaS platforms powered by Mastra

#### Examples of Compliant Usage

- A company builds an AI-powered customer service application using Mastra and sells it to clients
- A consulting firm offers implementation and customization services for Mastra
- A developer creates specialized agents and tools with Mastra and licenses them to other businesses
- A startup builds a vertical-specific solution (e.g., healthcare AI assistant) powered by Mastra
- A company offers Mastra as a hosted service to their customers
- A SaaS platform integrates Mastra as their AI backend

#### Compliance Requirements

The Apache License 2.0 has minimal requirements:

- **Attribution**: Maintain copyright notices and license information (including NOTICE file)
- **State Changes**: If you modify the software, state that you have made changes
- **Include License**: Include a copy of the Apache License 2.0 when distributing

### Questions About Licensing?

If you have specific questions about how the Apache License 2.0 applies to your use case, please [contact us](https://discord.gg/BTYqqHKUrf) on Discord for clarification. We're committed to supporting all legitimate use cases while maintaining the open-source nature of the project.
