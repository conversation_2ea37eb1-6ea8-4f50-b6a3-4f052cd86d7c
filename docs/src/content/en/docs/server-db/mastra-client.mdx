---
title: "MastraClient"
description: "Learn how to set up and use the Mastra Client SDK"
---

# Mastra Client SDK

The Mastra Client SDK provides a simple and type-safe interface for interacting with your [Mastra Server](/docs/deployment/server) from your client environment.

## Development Requirements

To ensure smooth local development, make sure you have:

- Node.js 18.x or later installed
- TypeScript 4.7+ (if using TypeScript)
- A modern browser environment with Fetch API support
- Your local Mastra server running (typically on port 4111)

## Installation

import { Tabs } from "nextra/components";

{/*
LLM CONTEXT:
This Tabs component shows installation commands for the Mastra Client SDK using different package managers.
Each tab displays the installation command for that specific package manager (npm, yarn, pnpm).
This helps users install the client SDK with their preferred package manager.
All commands install the same @mastra/client-js package but use different package manager syntax.
*/}

<Tabs items={["npm", "yarn", "pnpm"]}>
  <Tabs.Tab>
    ```bash copy
    npm install @mastra/client-js@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    yarn add @mastra/client-js@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    pnpm add @mastra/client-js@latest
    ```
  </Tabs.Tab>
</Tabs>

## Initialize Mastra Client

To get started you'll need to initialize your MastraClient with necessary parameters:

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient({
  baseUrl: "http://localhost:4111", // Default Mastra development server port
});
```

### Configuration Options

You can customize the client with various options:

```typescript
const client = new MastraClient({
  // Required
  baseUrl: "http://localhost:4111",

  // Optional configurations for development
  retries: 3, // Number of retry attempts
  backoffMs: 300, // Initial retry backoff time
  maxBackoffMs: 5000, // Maximum retry backoff time
  headers: {
    // Custom headers for development
    "X-Development": "true",
  },
});
```

## AbortSignal

The Mastra Client SDK supports request cancellation using the standard Web API `AbortSignal`. Pass an `AbortSignal` to the client constructor to enable cancellation for all requests:

```typescript
const controller = new AbortController();

const client = new MastraClient({
  baseUrl: "http://localhost:4111",
  abortSignal: controller.signal,
});

// Cancel all requests from this client
controller.abort();
```

## Example

Once your MastraClient is initialized you can start making client calls via the type-safe
interface

```typescript
// Get a reference to your local agent
const agent = client.getAgent("dev-agent-id");

// Generate responses
const response = await agent.generate({
  messages: [
    {
      role: "user",
      content: "Hello, I'm testing the local development setup!",
    },
  ],
});
```

## Available Features

Mastra client exposes all resources served by the Mastra Server

- [**Agents**](/reference/client-js/agents): Create and manage AI agents, generate responses, and handle streaming interactions
- [**Memory**](/reference/client-js/memory): Manage conversation threads and message history
- [**Tools**](/reference/client-js/tools): Access and execute tools available to agents
- [**Workflows**](/reference/client-js/workflows): Create and manage automated workflows
- [**Vectors**](/reference/client-js/vectors): Handle vector operations for semantic search and similarity matching

## Best Practices

1. **Error Handling**: Implement proper error handling for development scenarios
2. **Environment Variables**: Use environment variables for configuration
3. **Debugging**: Enable detailed logging when needed

```typescript
// Example with error handling and logging
try {
  const agent = client.getAgent("dev-agent-id");
  const response = await agent.generate({
    messages: [{ role: "user", content: "Test message" }],
  });
  console.log("Response:", response);
} catch (error) {
  console.error("Development error:", error);
}
```

## Debug

- Sometimes when using MastraClient on the server instead of the client e.g `/api/chat`,
  you might need to recreate the response to your client:

```typescript
const result = agent.stream(/* get your agent stream */);
return new Response(result.body);
```
