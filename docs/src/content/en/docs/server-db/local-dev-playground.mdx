---
title: "Inspecting agents and workflows with mastra dev | Mastra Local Dev Docs"
description: Documentation for the Mastra local development environment for Mastra applications.
---

import YouTube from "@/components/youtube";
import { VideoPlayer } from "@/components/video-player"
import { Tabs, Tab } from "@/components/tabs";

# Playground

Mastra provides a local development environment where you can test your agents, workflows, and tools during development.

Start the local development server by running:

<Tabs items={["npm", "Mastra CLI"]}>
  <Tab>
    ```bash copy
    npm run dev
    ```
  </Tab>
  <Tab>
    ```bash copy
    mastra dev
    ```
  </Tab>
</Tabs>

The local development server provides access to the following interfaces:

- Playground: [http://localhost:4111/](http://localhost:4111/)
- Mastra API: [http://localhost:4111/api](http://localhost:4111/api)
- OpenAPI Spec: [http://localhost:4111/openapi.json](http://localhost:4111/openapi.json)
- Swagger UI – API explorer: [http://localhost:4111/swagger-ui](http://localhost:4111/swagger-ui)

## Local Development Playground

The Playground lets you interact with your agents, workflows, and tools. It provides dedicated interfaces for testing each component of your Mastra application during development and is available at: [http://localhost:4111/](http://localhost:4111/).

<YouTube id="spGlcTEjuXY" startTime={126}/>

### Agents

Quickly test and debug your agents during development using the interactive chat interface in the Agent Playground.

<VideoPlayer
  src="https://res.cloudinary.com/dygi6femd/video/upload/v1751406022/local-dev-agents-playground_100_m3begx.mp4"
/>

Key features:

- **Chat Interface**: Talk to your agent and see how it responds in real time.
- **Model Settings**: Tweak settings like temperature and top-p to see how they affect output.
- **Agent Endpoints**: See the available REST API routes your agent exposes and how to use them.
- **Agent Traces**: Step through what the agent did behind the scenes, tool calls, decisions, and more.
- **Agent Evals**: Run tests against your agent and see how well it performs.

### Workflows

Validate workflows by supplying defined inputs and visualizing each step within the Workflow Playground.

<VideoPlayer
  src="https://res.cloudinary.com/dygi6femd/video/upload/v1751406027/local-dev-workflows-playground_100_rbc466.mp4"
/>

Key features:

- **Workflow Visualization**: See your workflow as a visual graph so you can follow the steps and branches at a glance.
- **Step Inputs & Outputs**: Check the data going into and coming out of each step to see how everything flows.
- **Run Workflows**: Test your workflow with real inputs to validate the logic and debug any issues.
- **Execution JSON**: Get the full picture of a run as raw JSON—inputs, outputs, errors, and results included.
- **Workflow Traces**: Dig into a detailed breakdown of each step, including data flow, tool calls, and any errors along the way.

### Tools

Quickly test and debug custom tools in isolation using the Tools Playground, without running a full agent or workflow.

<VideoPlayer
  src="https://res.cloudinary.com/dygi6femd/video/upload/v1751406316/local-dev-agents-tools_100_fe1jdt.mp4"
/>

Key features:

- **Test Tools in Isolation**: Try out individual tools on their own without running a full agent or workflow.
- **Input & Responses**: Send sample inputs to see how the tool responds.
- **Tool Usage**: Find out which agents rely on this tool and how they’re using it.


### MCP Servers

Explore connection details, tool usage, and IDE configuration for local MCP server development.

![MCP Servers Playground](/image/local-dev/local-dev-mcp-server-playground.jpg)

Key features:

- **Connection Details**: Access the endpoints and config needed to wire up your MCP environment.
- **Available Tools**:  See all tools currently published, including their names, versions, and which agents use them.
- **IDE Configuration**: Grab ready-to-use config you can drop into your local setup for testing and publishing tools.


## REST API Endpoints

The local development server exposes a set of REST API routes via the [Mastra Server](/docs/deployment/server), allowing you to test and interact with your agents and workflows before deployment.

For a full overview of available API routes, including agents, tools, and workflows, see the [Routes reference](/reference/cli/dev#routes).

## OpenAPI Specification

The local development server includes an OpenAPI specification available at: [http://localhost:4111/openapi.json](http://localhost:4111/openapi.json).

To include OpenAPI documentation in your production server, enable it in the Mastra instance:

```typescript {7} filename="src/mastra/index.ts" showLineNumbers copy
import { Mastra } from "@mastra/core/mastra";

export const mastra = new Mastra({
  // ...
  server: {
    build: {
      openAPIDocs: true
    }
  },
});
```

## Swagger UI

The local development server includes an interactive Swagger UI - API explorer available at: [http://localhost:4111/swagger-ui](http://localhost:4111/swagger-ui).

To include Swagger UI in your production server, enable it in the Mastra instance:

```typescript {7} filename="src/mastra/index.ts" showLineNumbers copy
import { Mastra } from "@mastra/core/mastra";

export const mastra = new Mastra({
  // ...
  server: {
    build: {
      swaggerUI: true
    },
  },
});
```

## Architecture

The local development server runs fully self-contained without external dependencies or containers. It leverages:

- **Dev Server** powered by [Hono](https://hono.dev) for the core [Mastra Server](/docs/deployment/server).
- **In-Memory Storage** via [LibSQL](https://libsql.org/) adapters for agent memory, traces, evals, and workflow snapshots.
- **Vector Storage** using [FastEmbed](https://github.com/qdrant/fastembed) for embeddings, vector search, and semantic retrieval.

This setup lets you start developing immediately with production-like behavior, no database or vector store setup required.

## Configuration

By default, the server runs on port `4111`. You can customize the host and port through the Mastra server configuration.

```typescript {6,7} filename="src/mastra/index.ts" showLineNumbers copy
import { Mastra } from "@mastra/core/mastra";

export const mastra = new Mastra({
  // ...
  server: {
    port: 8080,
    host: "0.0.0.0",
  },
});
```


## Next steps

- [Mastra Cloud](/docs/mastra-cloud/overview)
- [Deployment Overview](/docs/deployment/overview)
- [Mastra Client SDK](/docs/client-js/overview)
