---
title: "MCP Overview | Tools & MCP | Mastra Docs"
description: Learn about the Model Context Protocol (MCP), how to use third-party tools via MCPClient, connect to registries, and share your own tools using MCPServer.
---

import { Tabs } from "nextra/components";

# MCP Overview

[Model Context Protocol (MCP)](https://modelcontextprotocol.io/introduction) is an open standard designed to let AI models discover and interact with external tools and resources. Think of it as a universal plugin system for AI agents, allowing them to use tools regardless of the language they were written in or where they are hosted.

<PERSON><PERSON> uses MCP to connect agents to external tool servers.

## Use third-party tools with an MCP Client

Mastra provides the `MCPClient` class to manage connections to one or more MCP servers and access their tools.

### Installation

If you haven't already, install the Mastra MCP package:

```bash npm2yarn copy
npm install @mastra/mcp@latest
```

### Registering the MCPServer

Register your MCP server with <PERSON><PERSON> to enable logging and access to configured tools and integrations:

```ts showLineNumbers filename="src/mastra/index.ts" copy
import { <PERSON><PERSON> } from "@mastra/core";
import { myMcpServer } from "./mcpServers";

export const mastra = new Mastra({
  mcpServers: { myMcpServer },
});
```

### Configuring `MCPClient`

You configure `MCPClient` with a map of servers you want to connect to. It supports connections via subprocess (Stdio) or HTTP (Streamable HTTP with SSE fallback).

```typescript
import { MCPClient } from "@mastra/mcp";

const mcp = new MCPClient({
  servers: {
    // Stdio example
    sequential: {
      command: "npx",
      args: ["-y", "@modelcontextprotocol/server-sequential-thinking"],
    },
    // HTTP example
    weather: {
      url: new URL("http://localhost:8080/mcp"),
      requestInit: {
        headers: {
          Authorization: "Bearer your-token",
        },
      },
    },
  },
});
```

For detailed configuration options, see the [`MCPClient` reference documentation](/reference/tools/mcp-client).

### Static vs Dynamic Tool Configurations

`MCPClient` offers two approaches to retrieving tools from connected servers, suitable for different application architectures:

| Feature           | Static Configuration (`await mcp.getTools()`) | Dynamic Configuration (`await mcp.getToolsets()`)  |
| :---------------- | :-------------------------------------------- | :------------------------------------------------- |
| **Use Case**      | Single-user, static config (e.g., CLI tool)   | Multi-user, dynamic config (e.g., SaaS app)        |
| **Configuration** | Fixed at agent initialization                 | Per-request, dynamic                               |
| **Credentials**   | Shared across all uses                        | Can vary per user/request                          |
| **Agent Setup**   | Tools added in `Agent` constructor            | Tools passed in `generate()` or `stream()` options |

- **Static Configuration (`getTools()`):** Fetches all tools from all configured servers. Best when the tool configuration (like API keys) is static and shared across all users or requests. You typically call this once and pass the result to the `tools` property when defining your `Agent`.
  [Reference: `getTools()`](/reference/tools/mcp-client#gettools)

  ```typescript
  import { Agent } from "@mastra/core/agent";
  // ... mcp client setup

  const agent = new Agent({
    // ... other agent config
    tools: await mcp.getTools(),
  });
  ```

- **Dynamic Configuration (`getToolsets()`):** Designed for scenarios where configuration might change per request or per user (e.g., different API keys for different tenants in a multi-user application). You pass the result of `getToolsets()` to the `toolsets` option in the agent's `generate()` or `stream()` method.
  [Reference: `getToolsets()`](/reference/tools/mcp-client#gettoolsets)

  ```typescript
  import { Agent } from "@mastra/core/agent";
  // ... agent setup without tools initially

  async function handleRequest(userPrompt: string, userApiKey: string) {
    const userMcp = new MCPClient({
      /* config with userApiKey */
    });
    const toolsets = await userMcp.getToolsets();

    const response = await agent.stream(userPrompt, {
      toolsets, // Pass dynamic toolsets
    });
    // ... handle response
    await userMcp.disconnect();
  }
  ```

## Connecting to an MCP registry

MCP servers can be discovered through registries. Here's how to connect to some popular ones using `MCPClient`:


{/*
LLM CONTEXT: This Tabs component shows how to connect to different MCP (Model Context Protocol) registries.
Each tab demonstrates the configuration for a specific MCP registry service (mcp.run, Composio.dev, Smithery.ai).
The tabs help users understand how to connect to various MCP server providers and their different authentication methods.
Each tab shows the specific URL patterns and configuration needed for that registry service.
*/}

<Tabs items={["mcp.run", "Composio.dev", "Smithery.ai", "Ampersand"]}>
  <Tabs.Tab>
    [mcp.run](https://www.mcp.run/) provides pre-authenticated, managed MCP servers. Tools are grouped into Profiles, each with a unique, signed URL.

    ```typescript
    import { MCPClient } from "@mastra/mcp";

    const mcp = new MCPClient({
      servers: {
        marketing: { // Example profile name
          url: new URL(process.env.MCP_RUN_SSE_URL!), // Get URL from mcp.run profile
        },
      },
    });
    ```

    > **Important:** Treat the mcp.run SSE URL like a password. Store it securely, for example, in an environment variable.
    > ```bash filename=".env"
    > MCP_RUN_SSE_URL=https://www.mcp.run/api/mcp/sse?nonce=...
    > ```

  </Tabs.Tab>
  <Tabs.Tab>
    [Composio.dev](https://composio.dev) offers a registry of [SSE-based MCP servers](https://mcp.composio.dev). You can use the SSE URL generated for tools like Cursor directly.

    ```typescript
    import { MCPClient } from "@mastra/mcp";

    const mcp = new MCPClient({
      servers: {
        googleSheets: {
          url: new URL("https://mcp.composio.dev/googlesheets/[private-url-path]"),
        },
        gmail: {
          url: new URL("https://mcp.composio.dev/gmail/[private-url-path]"),
        },
      },
    });
    ```

    Authentication with services like Google Sheets often happens interactively through the agent conversation.

    *Note: Composio URLs are typically tied to a single user account, making them best suited for personal automation rather than multi-tenant applications.*

  </Tabs.Tab>
  <Tabs.Tab>
    [Smithery.ai](https://smithery.ai) provides a registry accessible via their CLI.

    ```typescript
    // Unix/Mac
    import { MCPClient } from "@mastra/mcp";

    const mcp = new MCPClient({
      servers: {
        sequentialThinking: {
          command: "npx",
          args: [
            "-y",
            "@smithery/cli@latest",
            "run",
            "@smithery-ai/server-sequential-thinking",
            "--config",
            "{}",
          ],
        },
      },
    });
    ```

    ```typescript
    // Windows
    import { MCPClient } from "@mastra/mcp";

    const mcp = new MCPClient({
      servers: {
        sequentialThinking: {
          command: "npx",
          args: [
            "-y",
            "@smithery/cli@latest",
            "run",
            "@smithery-ai/server-sequential-thinking",
            "--config",
            "{}",
          ],
        },
      },
    });
    ```

  </Tabs.Tab>
    <Tabs.Tab>

    [Ampersand](https://withampersand.com?utm_source=mastra-docs) offers an [MCP Server](https://docs.withampersand.com/mcp) that allows you to connect your agent to 150+ integrations with SaaS products like Salesforce, Hubspot, and Zendesk.
    

    ```typescript

    // MCPClient with Ampersand MCP Server using SSE
    export const mcp = new MCPClient({
        servers: {
        "@amp-labs/mcp-server": {
          "url": `https://mcp.withampersand.com/v1/sse?${new URLSearchParams({
            apiKey: process.env.AMPERSAND_API_KEY,
            project: process.env.AMPERSAND_PROJECT_ID,
            integrationName: process.env.AMPERSAND_INTEGRATION_NAME,
            groupRef: process.env.AMPERSAND_GROUP_REF
          })}`
        }
      }
    });

    ```

    ```typescript
    // If you prefer to run the MCP server locally:
    
    import { MCPClient } from "@mastra/mcp";

    // MCPClient with Ampersand MCP Server using stdio transport
    export const mcp = new MCPClient({
        servers: {
          "@amp-labs/mcp-server": {
            command: "npx",
            args: [
              "-y",
              "@amp-labs/mcp-server@latest",
              "--transport",
              "stdio",
              "--project",
              process.env.AMPERSAND_PROJECT_ID,
              "--integrationName",
              process.env.AMPERSAND_INTEGRATION_NAME,
              "--groupRef",
              process.env.AMPERSAND_GROUP_REF, // optional
            ],
            env: {
              AMPERSAND_API_KEY: process.env.AMPERSAND_API_KEY,
            },
          },
        },
    });
    ```

    As an alternative to MCP, Ampersand's AI SDK also has an adapter for Mastra, so you can [directly import Ampersand tools](https://docs.withampersand.com/ai-sdk#use-with-mastra) for your agent to access.

  </Tabs.Tab>
</Tabs>

## Share your tools with an MCP server

If you have created your own Mastra tools, you can expose them to any MCP-compatible client using Mastra's `MCPServer` class.

Similarly, Mastra `Agent` and `Workflow` instances can also be exposed as tools via `MCPServer`. This allows other MCP clients to interact with your agents by "asking" them questions or run your workflows. Each agent provided in the `MCPServer` configuration will be converted into a tool named `ask_<agentKey>`, using the agent's `description` property. Each workflow will be converted into a tool named `run_<workflowKey>`, using its `inputSchema` and `description`.

This allows others to use your tools, agents, and workflows without needing direct access to your codebase.

### Using `MCPServer`

You initialize `MCPServer` with a name, version, and the Mastra tools, agents, and/or workflows you want to share.

```typescript
import { MCPServer } from "@mastra/mcp";
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { weatherTool } from "./tools"; // Your Mastra tool
import { weatherAgent } from "./agents"; // Your Mastra Agent
import { dataWorkflow } from "./workflows"; // Your Mastra Workflow

const server = new MCPServer({
  name: "My Custom Server",
  version: "1.0.0",
  tools: { weatherTool }, // Provide your tool(s) here
  agents: { weatherAgent }, // Provide your agent(s) here
  workflows: { dataWorkflow }, // Provide your workflow(s) here
});

// Start the server (e.g., using stdio for a CLI tool)
// await server.startStdio();

// Or integrate with an HTTP server using startSSE()
// See MCPServer reference for details
```

For an agent to be exposed as a tool, it must have a non-empty `description` string. Similarly, for a workflow to be exposed, its `description` must also be a non-empty string. If the description is missing or empty for either, `MCPServer` will throw an error during initialization.
Workflows will use their `inputSchema` for the tool's input.

### Tools with Structured Outputs

You can define an `outputSchema` for your tools to enforce a specific structure for the tool's output. This is useful for ensuring that the tool returns data in a consistent and predictable format, which can then be validated by the client.

When a tool includes an `outputSchema`, its `execute` function **must** return an object. The value of the object must conform to the `outputSchema`. Mastra will automatically validate this output on both the server and client sides.

Here's an example of a tool with an `outputSchema`:

```typescript filename="src/tools/structured-tool.ts"
import { createTool } from '@mastra/core';
import { z } from 'zod';

export const structuredTool = createTool({
  description: 'A test tool that returns structured data.',
  parameters: z.object({
    input: z.string().describe('Some input string.'),
  }),
  outputSchema: z.object({
    processedInput: z.string().describe('The processed input string.'),
    timestamp: z.string().describe('An ISO timestamp.'),
  }),
  execute: async ({ input }) => {
    // When outputSchema is defined, you must return an object
    return {
      processedInput: `processed: ${input}`,
      timestamp: new Date().toISOString(),
    };
  },
});
```

When this tool is called, the MCP client will receive both the structured data and a text representation of it.

```
Tool result

{
  "content": [
    {
      "type": "text",
      "text": "{\"processedInput\": \"hello\", \"timestamp\": \"2025-06-19T16:53:16.472Z\"}"
    }
  ],
  "structuredContent": {
    "processedInput": "processed: hello",
    "timestamp": "2025-06-19T16:53:16.472Z",
  }
}
```

For detailed usage and examples, see the [`MCPServer` reference documentation](/reference/tools/mcp-server).
