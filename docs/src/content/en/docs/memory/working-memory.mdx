import YouTube from "@/components/youtube";

# Working Memory

While [conversation history](/docs/memory/overview#conversation-history) and [semantic recall](./semantic-recall.mdx) help agents remember conversations, working memory allows them to maintain persistent information about users across interactions.

Think of it as the agent's active thoughts or scratchpad – the key information they keep available about the user or task. It's similar to how a person would naturally remember someone's name, preferences, or important details during a conversation.

This is useful for maintaining ongoing state that's always relevant and should always be available to the agent.

Working memory can persist at two different scopes:
- **Thread-scoped** (default): Memory is isolated per conversation thread
- **Resource-scoped**: Memory persists across all conversation threads for the same user

**Important:** Switching between scopes means the agent won't see memory from the other scope - thread-scoped memory is completely separate from resource-scoped memory.


## Quick Start

Here's a minimal example of setting up an agent with working memory:

```typescript {12-15}
import { Agent } from "@mastra/core/agent";
import { Memory } from "@mastra/memory";
import { openai } from "@ai-sdk/openai";

// Create agent with working memory enabled
const agent = new Agent({
  name: "PersonalAssistant",
  instructions: "You are a helpful personal assistant.",
  model: openai("gpt-4o"),
  memory: new Memory({
    options: {
      workingMemory: {
        enabled: true,
      },
    },
  }),
});
```

## How it Works

Working memory is a block of Markdown text that the agent is able to update over time to store continuously relevant information:

<YouTube id="ik-ld_XA96s" />

## Memory Persistence Scopes

Working memory can operate in two different scopes, allowing you to choose how memory persists across conversations:

### Thread-Scoped Memory (Default)

By default, working memory is scoped to individual conversation threads. Each thread maintains its own isolated memory:

```typescript
const memory = new Memory({
  storage,
  options: {
    workingMemory: {
      enabled: true,
      scope: 'thread', // Default - memory is isolated per thread
      template: `# User Profile
- **Name**: 
- **Interests**: 
- **Current Goal**: 
`,
    },
  },
});
```

**Use cases:**
- Different conversations about separate topics
- Temporary or session-specific information
- Workflows where each thread needs working memory but threads are ephemeral and not related to each other

### Resource-Scoped Memory

Resource-scoped memory persists across all conversation threads for the same user (resourceId), enabling persistent user memory:

```typescript
const memory = new Memory({
  storage,
  options: {
    workingMemory: {
      enabled: true,
      scope: 'resource', // Memory persists across all user threads
      template: `# User Profile
- **Name**: 
- **Location**: 
- **Interests**: 
- **Preferences**: 
- **Long-term Goals**: 
`,
    },
  },
});
```

**Use cases:**
- Personal assistants that remember user preferences
- Customer service bots that maintain customer context
- Educational applications that track student progress

### Usage with Agents

When using resource-scoped memory, make sure to pass the `resourceId` parameter:

```typescript
// Resource-scoped memory requires resourceId
const response = await agent.generate("Hello!", {
  threadId: "conversation-123",
  resourceId: "user-alice-456" // Same user across different threads
});
```

## Storage Adapter Support

Resource-scoped working memory requires specific storage adapters that support the `mastra_resources` table:

### ✅ Supported Storage Adapters
- **LibSQL** (`@mastra/libsql`)
- **PostgreSQL** (`@mastra/pg`)
- **Upstash** (`@mastra/upstash`)

## Custom Templates

Templates guide the agent on what information to track and update in working memory. While a default template is used if none is provided, you'll typically want to define a custom template tailored to your agent's specific use case to ensure it remembers the most relevant information.

Here's an example of a custom template. In this example the agent will store the users name, location, timezone, etc as soon as the user sends a message containing any of the info:

```typescript {5-28}
const memory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      template: `
# User Profile
 
## Personal Info
 
- Name:
- Location:
- Timezone:
 
## Preferences
 
- Communication Style: [e.g., Formal, Casual]
- Project Goal:
- Key Deadlines:
  - [Deadline 1]: [Date]
  - [Deadline 2]: [Date]
 
## Session State
 
- Last Task Discussed:
- Open Questions:
  - [Question 1]
  - [Question 2]
`,
    },
  },
});
```

## Designing Effective Templates

A well-structured template keeps the information easy for the agent to parse and update. Treat the
template as a short form that you want the assistant to keep up to date.

- **Short, focused labels.** Avoid paragraphs or very long headings. Keep labels brief (for example
  `## Personal Info` or `- Name:`) so updates are easy to read and less likely to be truncated.
- **Use consistent casing.** Inconsistent capitalization (`Timezone:` vs `timezone:`) can cause messy
  updates. Stick to Title Case or lower case for headings and bullet labels.
- **Keep placeholder text simple.** Use hints such as `[e.g., Formal]` or `[Date]` to help the LLM
  fill in the correct spots.
- **Abbreviate very long values.** If you only need a short form, include guidance like
  `- Name: [First name or nickname]` or `- Address (short):` rather than the full legal text.
- **Mention update rules in `instructions`.** You can instruct how and when to fill or clear parts of
  the template directly in the agent's `instructions` field.

### Alternative Template Styles

Use a shorter single block if you only need a few items:

```typescript
const basicMemory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      template: `User Facts:\n- Name:\n- Favorite Color:\n- Current Topic:`,
    },
  },
});
```

You can also store the key facts in a short paragraph format if you prefer a more narrative style:

```typescript
const paragraphMemory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      template: `Important Details:\n\nKeep a short paragraph capturing the user's important facts (name, main goal, current task).`,
    },
  },
});
```

## Structured Working Memory

Working memory can also be defined using a structured schema instead of a Markdown template. This allows you to specify the exact fields and types that should be tracked, using a [Zod](https://zod.dev/) schema. When using a schema, the agent will see and update working memory as a JSON object matching your schema.

**Important:** You must specify either `template` or `schema`, but not both.

### Example: Schema-Based Working Memory

```typescript
import { z } from 'zod';
import { Memory } from '@mastra/memory';

const userProfileSchema = z.object({
  name: z.string().optional(),
  location: z.string().optional(),
  timezone: z.string().optional(),
  preferences: z.object({
    communicationStyle: z.string().optional(),
    projectGoal: z.string().optional(),
    deadlines: z.array(z.string()).optional(),
  }).optional(),
});

const memory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      schema: userProfileSchema,
      // template: ... (do not set)
    },
  },
});
```

When a schema is provided, the agent receives the working memory as a JSON object. For example:

```json
{
  "name": "Sam",
  "location": "Berlin",
  "timezone": "CET",
  "preferences": {
    "communicationStyle": "Formal",
    "projectGoal": "Launch MVP",
    "deadlines": ["2025-07-01"]
  }
}
```

## Choosing Between Template and Schema

- Use a **template** (Markdown) if you want the agent to maintain memory as a free-form text block, such as a user profile or scratchpad.
- Use a **schema** if you need structured, type-safe data that can be validated and programmatically accessed as JSON.
- Only one mode can be active at a time: setting both `template` and `schema` is not supported.

## Example: Multi-step Retention

Below is a simplified view of how the `User Profile` template updates across a short user
conversation:

```nohighlight
# User Profile

## Personal Info

- Name:
- Location:
- Timezone:

--- After user says "My name is **Sam** and I'm from **Berlin**" ---

# User Profile
- Name: Sam
- Location: Berlin
- Timezone:

--- After user adds "By the way I'm normally in **CET**" ---

# User Profile
- Name: Sam
- Location: Berlin
- Timezone: CET
```

The agent can now refer to `Sam` or `Berlin` in later responses without requesting the information
again because it has been stored in working memory.

If your agent is not properly updating working memory when you expect it to, you can add system
instructions on _how_ and _when_ to use this template in your agent's `instructions` setting.

## Examples

- [Streaming working memory](/examples/memory/streaming-working-memory)
- [Using a working memory template](/examples/memory/streaming-working-memory-advanced)
- [Using a working memory schema](/examples/memory/streaming-working-memory-structured)
- [Per-resource working memory](https://github.com/mastra-ai/mastra/tree/main/examples/memory-per-resource-example) - Complete example showing resource-scoped memory persistence
