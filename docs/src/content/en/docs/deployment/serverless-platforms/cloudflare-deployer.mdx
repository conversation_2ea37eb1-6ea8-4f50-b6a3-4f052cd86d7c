---
title: "Cloudflare Deployer"
description: "Learn how to deploy a Mastra application to Cloudflare using the Mastra CloudflareDeployer"
---

import { FileTree } from "nextra/components";

# CloudflareDeployer

The `CloudflareDeployer` class handles deployment of standalone Mastra applications to Cloudflare Workers. It manages configuration, deployment, and extends the base [Deployer](/reference/deployer/deployer) class with Cloudflare specific functionality.

## Installation

```bash copy
npm install @mastra/deployer-cloudflare@latest
```

## Usage example

```typescript filename="src/mastra/index.ts" showLineNumbers copy
import { Mastra } from "@mastra/core/mastra";
import { CloudflareDeployer } from "@mastra/deployer-cloudflare";

export const mastra = new Mastra({
  // ...
  deployer: new CloudflareDeployer({
    scope: "your-account-id",
    projectName: "hello-mastra",
    scope: "",
    auth: {
      apiToken: process.env.CLOUDFLARE_API_TOKEN!,
      apiEmail: "<EMAIL>"
    }
  })
});
```

> See the [CloudflareDeployer](/reference/deployer/cloudflare) API reference for all available configuration options.

## Manual deployment

Manual deployments are also possible using the [Cloudflare Wrangler CLI](https://developers.cloudflare.com/workers/wrangler/install-and-update/). With the Wrangler CLI installed run the following from your project root to deploy your application.

With the Wrangler CLI installed, login and authenticate with your Cloudflare logins:

```bash copy
npx wrangler login
```

Run the following to build and deploy your application to Cloudflare

```bash copy
npm run build && wrangler deploy --config .mastra/output/wrangler.json
```

> You can also run `wrangler dev --config .mastra/output/wrangler.json` from your project root to test your Mastra application locally.

## Build output

The build output for Mastra applications using the `CloudflareDeployer` includes all agents, tools, and workflows in your project, along with Mastra specific files required to run your application on Cloudflare.

<FileTree>
  <FileTree.Folder name=".mastra" defaultOpen>
    <FileTree.Folder name="output" defaultOpen>
      <FileTree.File name="index.mjs" />
      <FileTree.File name="wrangler.json" />
      </FileTree.Folder>
  </FileTree.Folder>
  <FileTree.File name="package.json" />
</FileTree>

The `CloudflareDeployer` automatically generates a `wrangler.json` configuration file in `.mastra/output` with the following settings:

```json
{
  "name": "hello-mastra",
  "main": "./index.mjs",
  "compatibility_date": "2025-04-01",
  "compatibility_flags": ["nodejs_compat", "nodejs_compat_populate_process_env"],
  "observability": { "logs": { "enabled": true } },
  "vars": {
    "OPENAI_API_KEY": "...",
    "CLOUDFLARE_API_TOKEN": "..."
  }
}

```
## Next steps

- [Mastra Client SDK](/docs/client-js/overview)
