---
title: "Deploying <PERSON><PERSON> with a Web Framework"
description: "Learn how Mastra can be deployed when integrated with a Web Framework"
---

# Web Framework Integration

This guide covers deploying integrated Mastra applications. Mastra can be integrated with a variety of web frameworks, see one of the following for a detailed guide.

- [With Next.js](/docs/frameworks/web-frameworks/next-js)
- [With Astro](/docs/frameworks/web-frameworks/astro)

When integrated with a framework, <PERSON><PERSON> typically requires no additional configuration for deployment.

## With Next.js on Vercel

If you've integrated Mastra with Next.js [by following our guide](/docs/frameworks/web-frameworks/next-js) and plan to deploy to Vercel, no additional setup is required.

The only thing to verify is that you've added the following to your `next.config.ts` and removed any usage of [LibSQLStore](/docs/deployment/deployment#libsqlstore), which is not supported in serverless environments:

```typescript {4} filename="next.config.ts" showLineNumbers copy
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ["@mastra/*"],
};

export default nextConfig;
```

## With Astro on Vercel

If you've integrated Mastra with Astro [by following our guide](/docs/frameworks/web-frameworks/astro) and plan to deploy to Vercel, no additional setup is required.

The only thing to verify is that you've added the following to your `astro.config.mjs` and removed any usage of [LibSQLStore](/docs/deployment/deployment#libsqlstore), which is not supported in serverless environments:

```javascript {2,6,7} filename="astro.config.mjs" showLineNumbers copy
import { defineConfig } from 'astro/config';
import vercel from '@astrojs/vercel';

export default defineConfig({
  // ...
  adapter: vercel(),
  output: "server"
});
```

## With Astro on Netlify

If you've integrated Mastra with Astro [by following our guide](/docs/frameworks/web-frameworks/astro) and plan to deploy to Vercel, no additional setup is required.

The only thing to verify is that you've added the following to your `astro.config.mjs` and removed any usage of [LibSQLStore](/docs/deployment/deployment#libsqlstore), which is not supported in serverless environments:

```javascript {2,6,7} filename="astro.config.mjs" showLineNumbers copy
import { defineConfig } from 'astro/config';
import vercel from '@astrojs/netlify';

export default defineConfig({
  // ...
  adapter: netlify(),
  output: "server"
});
```
