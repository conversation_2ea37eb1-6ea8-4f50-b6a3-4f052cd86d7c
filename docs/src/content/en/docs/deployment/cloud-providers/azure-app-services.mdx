---
title: "Azure App Services"
description: "Deploy your Mastra applications to Azure App Services."
---

import { Callout, Steps } from "nextra/components";

## Azure App Services

Deploy your Mastra applications to Azure App Services.

<Callout>
  This guide assumes your Mastra application has been created using the default
  `npx create-mastra@latest` command.
  For more information on how to create a new Mastra application,
  refer to our [getting started guide](/docs/getting-started/installation)
</Callout>

### Prerequisites

- An Azure account with an active subscription
- A GitHub repository containing your Mastra application
- Your Mastra application should be created using `npx create-mastra@latest`

### Deployment Steps

<Steps>

#### Create a new App Service

- Log in to the [Azure Portal](https://portal.azure.com)
- Navigate to **App Services** or search for it in the top search bar
- Click **Create** to create a new App Service
- In the drop-down, select **Web App**

#### Configure App Service settings

- **Subscription**: Select your Azure subscription
- **Resource Group**: Create a new resource group or select an existing one
- **Instance name**: Enter a unique name for your app (this will be part of your URL)
- **Publish**: Select **Code**
- **Runtime stack**: Select **Node 22 LTS**
- **Operating System**: Select **Linux**
- **Region**: Choose a region close to your users
- **Linux Plan**: You may have the option of choosing a plan depending on the region you chose, pick an appropriate one for your needs.
- Click **Review + Create**
- Wait for validation to complete, then click **Create**

#### Wait for deployment

- Wait for the deployment to complete
- Once finished, click **Go to resource** under the next steps section

#### Configure environment variables

Before setting up deployment, configure your environment variables:

- Navigate to **Settings** > **Environment variables** in the left sidebar
- Add your required environment variables such as:
  - Model provider API keys (e.g., `OPENAI_API_KEY`)
  - Database connection strings
  - Any other configuration values your Mastra application requires
- Click **Apply** to save the changes

#### Set up GitHub deployment

- Navigate to **Deployment Center** in the left sidebar
- Select **GitHub** as your source
- Sign in to GitHub if you're not already authenticated with Azure
- In this example, we will keep GitHub Actions as our provider.
- Select your organization, repository, and branch
- Azure will generate a GitHub workflow file and you can preview it before proceeding
- Click **Save** (the save button is located at the top of the page)

#### Modify the GitHub workflow

<Callout type="warning">
The default workflow generated by Azure will fail for Mastra applications and needs to be modified.
</Callout>

After Azure creates the workflow, it will trigger a GitHub Actions run and merge the workflow file into your branch. **Cancel this initial run** as it will fail without the necessary modifications.

Pull the latest changes to your local repository and modify the generated workflow file (`.github/workflows/main_<your-app-name>.yml`):

1. **Update the build step**: Find the step named "npm install, build, and test" and:
   - Change the step name to "npm install and build"
   - Remove the `npm test` command from the run section

2. **Update the zip artifact step**: Find the "Zip artifact for deployment" step and replace the zip command with:

   ```yaml
   run: (cd .mastra/output && zip ../../release.zip -r .)
   ```

   This ensures only the build outputs from `.mastra/output` are included in the deployment package.

#### Deploy your changes

- Commit and push your workflow modifications
- The build will be automatically triggered in the **Deployment Center** in your Azure dashboard
- Monitor the deployment progress until it completes successfully

#### Access your application

- Once the build is successful, wait a few moments for the application to start
- Access your deployed application using the default URL provided in the **Overview** tab in the Azure portal
- Your application will be available at `https://<your-app-name>.azurewebsites.net`

</Steps>

### Connect to your Mastra server

You can now connect to your Mastra server from your client application using a `MastraClient` from the `@mastra/client-js` package.

Refer to the [`MastraClient` documentation](/docs/client-js/overview) for more information.

```typescript copy showLineNumbers
import { MastraClient } from "@mastra/client-js";

const mastraClient = new MastraClient({
  baseUrl: "https://<your-app-name>.azurewebsites.net",
});
```

<Callout>
Azure App Services uses an ephemeral file system for some pricing tiers.
For production applications, avoid using Mastra storage providers that rely on the local file system,
such as `LibSQLStore` with a file URL. Consider using cloud-based storage solutions instead.
</Callout>

## Next steps

- [Mastra Client SDK](/docs/client-js/overview)
- [Configure custom domains](https://docs.microsoft.com/en-us/azure/app-service/app-service-web-tutorial-custom-domain)
- [Enable HTTPS](https://docs.microsoft.com/en-us/azure/app-service/configure-ssl-bindings)

