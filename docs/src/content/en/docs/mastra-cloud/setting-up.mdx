---
title: Setting Up a Project
description: Configuration steps for Mastra Cloud projects
---

import { MastraCloudCallout } from '@/components/mastra-cloud-callout'
import { Steps } from "nextra/components";

# Setting Up and Deploying

This page explains how to set up a project on [Mastra Cloud](https://mastra.ai/cloud) with automatic deployments using our GitHub integration.

<MastraCloudCallout />

## Prerequisites

- A [Mastra Cloud](https://mastra.ai/cloud) account
- A GitHub account / repository containing a Mastra application

> See our [Getting started](/docs/getting-started/installation) guide to scaffold out a new Mastra project with sensible defaults.

## Setup and Deploy process

<Steps>

### Sign in to Mastra Cloud

Head over to [https://cloud.mastra.ai/](https://cloud.mastra.ai) and sign in with either:

- **GitHub**
- **Google**

### Install the Mastra GitHub app

When prompted, install the Mastra GitHub app.

![Install GitHub](/image/mastra-cloud/mastra-cloud-install-github.jpg)

### Create a new project

Click the **Create new project** button to create a new project.

![Create new project](/image/mastra-cloud/mastra-cloud-create-new-project.jpg)

### Import a Git repository

Search for a repository, then click **Import**.

![Import Git repository](/image/mastra-cloud/mastra-cloud-import-git-repository.jpg)

### Configure the deployment

Mastra Cloud automatically detects the right build settings, but you can customize them using the options described below.

![Deployment details](/image/mastra-cloud/mastra-cloud-deployment-details.jpg)

- **Importing from GitHub**: The GitHub repository name
- **Project name**: Customize the project name
- **Branch**: The branch to deploy from
- **Project root**: The root directory of your project
- **Mastra directory**: Where Mastra files are located
- **Environment variables**: Add environment variables used by the application
- **Build and Store settings**:
   - **Install command**: Runs pre-build to install project dependencies
   - **Project setup command**: Runs pre-build to prepare any external dependencies
   - **Port**: The network port the server will use
   - **Store settings**: Use Mastra Cloud's built-in [LibSQLStore](/docs/storage/overview) storage
- **Deploy Project**: Starts the deployment process

### Deploy project

Click **Deploy Project** to create and deploy your application using the configuration you’ve set.

</Steps>

## Successful deployment

After a successful deployment you'll be shown the **Overview** screen where you can view your project's status, domains, latest deployments and connected agents and workflows.

![Successful deployment](/image/mastra-cloud/mastra-cloud-successful-deployment.jpg)

## Continuous integration

Your project is now configured with automatic deployments which occur whenever you push to the configured branch of your GitHub repository.

## Testing your application

After a successful deployment you can test your agents and workflows from the [Playground](/docs/mastra-cloud/dashboard#playground) in Mastra Cloud, or interact with them using our [Client SDK](/docs/client-js/overview).

## Next steps

- [Navigating the Dashboard](/docs/mastra-cloud/dashboard)
