---
title: "Getting Started with <PERSON>stra and Next.js | Mastra Guides"
description: A step-by-step guide to integrating Mastra with Next.js.
---

import { Callout, Steps, Tabs } from "nextra/components";

# Integrate <PERSON>stra in your Next.js project

Mastra integrates with Next.js, making it easy to:

- Build flexible APIs to serve AI-powered features
- Simplify deployment with a unified codebase for frontend and backend
- Take advantage of Next.js's built-in server actions (App Router) or API Routes (Pages Router) for efficient server-client workflows

Use this guide to scaffold and integrate Mastra with your Next.js project.

<Tabs items={["App Router", "Pages Router"]}>
  <Tabs.Tab>

<Callout type="warning">
  This guide assumes you're using the Next.js App Router at the root of your
  project, e.g., `app` rather than `src/app`.
</Callout>

  <Steps>
  ## Install Mastra

Install the required Mastra packages:
{/*
LLM CONTEXT: This Tabs component shows commands for integrating a new Mastra backend project using different package managers.
Each tab displays the command for that specific package manager to create an integrated Mastra backend service.
This is part of the "Integrated Backend Integration" approach for Next.js projects.
All commands create the same Mastra project but use different package manager syntax.
*/}

<Tabs items={["npm", "yarn", "pnpm", "bun"]}>
  <Tabs.Tab>
    ```bash copy
    npm install mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    yarn add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    pnpm add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    bun add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
</Tabs>

## Integrate Mastra

To integrate Mastra into your project, you have two options:

### 1. Use the One-Liner

Run the following command to quickly scaffold the default Weather agent with sensible defaults:

```bash copy
npx mastra@latest init --dir . --components agents,tools --example --llm openai
```

> See [mastra init](/reference/cli/init) for more information.

### 2. Use the Interactive CLI

If you prefer to customize the setup, run the `init` command and choose from the options when prompted:

```bash copy
npx mastra@latest init
```

<Callout type="warning">
By default, `mastra init` suggests `src` as the install location. If you're using the App Router at the root of your project (e.g., `app`, not `src/app`), enter `.` when prompted:
</Callout>

Add the `dev` and `build` scripts to `package.json`:

<Tabs items={["app", "src/app"]}>
  <Tabs.Tab>
    ```json filename="package.json"
    {
      "scripts": {
        ...
        "dev:mastra": "mastra dev --dir mastra",
        "build:mastra": "mastra build --dir mastra"
      }
    }
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```json filename="package.json"
    {
      "scripts": {
        ...
        "dev:mastra": "mastra dev --dir src/mastra",
        "build:mastra": "mastra build --dir src/mastra"
      }
    }
    ```
  </Tabs.Tab>
</Tabs>

## Configure TypeScript

Modify the `tsconfig.json` file in your project root:

```json filename="tsconfig.json"
{
  ...
  "exclude": ["dist", ".mastra"]
}
```

## Set Up API Key

```bash filename=".env" copy
OPENAI_API_KEY=<your-api-key>
```

> Each LLM provider uses a different env var. See [Model Capabilities](/docs/getting-started/model-capability) for more information.

## Configure Next.js

Add to your `next.config.ts`:

```typescript filename="next.config.ts" showLineNumbers copy
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ["@mastra/*"],
};

export default nextConfig;
```

## Update .gitignore

Add `.mastra` to your `.gitignore` file:

```bash filename=".gitignore" copy
.mastra
```

## Start the Mastra Dev Server

Start the Mastra Dev Server to expose your agents as REST endpoints:

<Tabs items={["npm", "CLI"]}>
  <Tabs.Tab>
    ```bash copy
    npm run dev:mastra
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    mastra dev:mastra
    ```
  </Tabs.Tab>
</Tabs>

> Once running, your agents are available locally. See [Local Development Environment](/docs/server-db/local-dev-playground) for more information.

## Start Next.js Dev Server

With the Mastra Dev Server running, you can start your Next.js app in the usual way.

## Create Test Directory

Create a new directory that will contain a Page, Action, and Form for testing purposes.

```bash copy
mkdir app/test
```

### Create Test Action

Create a new Action, and add the example code:

```bash copy
touch app/test/action.ts
```

```typescript filename="app/test/action.ts" showLineNumbers copy
"use server";

import { mastra } from "../../mastra";

export async function getWeatherInfo(formData: FormData) {
  const city = formData.get("city")?.toString();
  const agent = mastra.getAgent("weatherAgent");

  const result = await agent.generate(`What's the weather like in ${city}?`);

  return result.text;
}
```

### Create Test Form

Create a new Form component, and add the example code:

```bash copy
touch app/test/form.tsx
```

```typescript filename="app/test/form.tsx" showLineNumbers copy
"use client";

import { useState } from "react";
import { getWeatherInfo } from "./action";

export function Form() {
  const [result, setResult] = useState<string | null>(null);

  async function handleSubmit(formData: FormData) {
    const res = await getWeatherInfo(formData);
    setResult(res);
  }

  return (
    <>
      <form action={handleSubmit}>
        <input name="city" placeholder="Enter city" required />
        <button type="submit">Get Weather</button>
      </form>
      {result && <pre>{result}</pre>}
    </>
  );
}
```

### Create Test Page

Create a new Page, and add the example code:

```bash copy
touch app/test/page.tsx
```

```typescript filename="app/test/page.tsx" showLineNumbers copy
import { Form } from "./form";

export default async function Page() {
  return (
    <>
      <h1>Test</h1>
      <Form />
    </>
  );
}
```

> You can now navigate to `/test` in your browser to try it out.

Submitting **London** as the city would return a result similar to:

```plaintext
Agent response: The current weather in London is as follows:

- **Temperature:** 12.9°C (Feels like 9.7°C)
- **Humidity:** 63%
- **Wind Speed:** 14.7 km/h
- **Wind Gusts:** 32.4 km/h
- **Conditions:** Overcast

Let me know if you need more information!
```

  </Steps>
  </Tabs.Tab>

  <Tabs.Tab>

<Callout type="warning">
  This guide assumes you're using the Next.js Pages Router at the root of your
  project, e.g., `pages` rather than `src/pages`.
</Callout>

  <Steps>

## Install Mastra

Install the required Mastra packages:
{/*
LLM CONTEXT: This Tabs component shows commands for integrating a new Mastra backend project using different package managers.
Each tab displays the command for that specific package manager to create an integrated Mastra backend service.
This is part of the "Integrated Backend Integration" approach for Next.js projects.
All commands create the same Mastra project but use different package manager syntax.
*/}

<Tabs items={["npm", "yarn", "pnpm", "bun"]}>
  <Tabs.Tab>
    ```bash copy
    npm install mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    yarn add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    pnpm add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    bun add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
</Tabs>

## Integrate Mastra

To integrate Mastra into your project, you have two options:

### 1. Use the One-Liner

Run the following command to quickly scaffold the default Weather agent with sensible defaults:

```bash copy
npx mastra@latest init --dir . --components agents,tools --example --llm openai
```

> See [mastra init](/reference/cli/init) for more information.

### 2. Use the Interactive CLI

If you prefer to customize the setup, run the `init` command and choose from the options when prompted:

```bash copy
npx mastra@latest init
```

<Callout type="warning">
By default, `mastra init` suggests `src` as the install location. If you're using the Pages Router at the root of your project (e.g., `pages`, not `src/pages`), enter `.` when prompted:
</Callout>

Add the `dev` and `build` scripts to `package.json`:

<Tabs items={["pages", "src/pages"]}>
  <Tabs.Tab>
    ```json filename="package.json"
    {
      "scripts": {
        ...
        "dev:mastra": "mastra dev --dir mastra",
        "build:mastra": "mastra build --dir mastra"
      }
    }
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```json filename="package.json"
    {
      "scripts": {
        ...
        "dev:mastra": "mastra dev --dir src/mastra",
        "build:mastra": "mastra build --dir src/mastra"
      }
    }
    ```
  </Tabs.Tab>
</Tabs>

## Configure TypeScript

Modify the `tsconfig.json` file in your project root:

```json filename="tsconfig.json"
{
  ...
  "exclude": ["dist", ".mastra"]
}
```

## Set Up API Key

```bash filename=".env" copy
OPENAI_API_KEY=<your-api-key>
```

> Each LLM provider uses a different env var. See [Model Capabilities](/docs/getting-started/model-capability) for more information.

## Configure Next.js

Add to your `next.config.ts`:

```typescript filename="next.config.ts" showLineNumbers copy
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ["@mastra/*"],
};

export default nextConfig;
```

## Update .gitignore

Add `.mastra` to your `.gitignore` file:

```bash filename=".gitignore" copy
.mastra
```

## Start the Mastra Dev Server

Start the Mastra Dev Server to expose your agents as REST endpoints:

<Tabs items={["npm", "CLI"]}>
  <Tabs.Tab>
    ```bash copy
    npm run dev:mastra
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    mastra dev:mastra
    ```
  </Tabs.Tab>
</Tabs>

> Once running, your agents are available locally. See [Local Development Environment](/docs/local-dev/mastra-dev) for more information.

## Start Next.js Dev Server

With the Mastra Dev Server running, you can start your Next.js app in the usual way.

## Create Test API Route

Create a new API Route, and add the example code:

```bash copy
touch pages/api/test.ts
```

```typescript filename="pages/api/test.ts" showLineNumbers copy
import type { NextApiRequest, NextApiResponse } from "next";

import { mastra } from "../../mastra";

export default async function getWeatherInfo(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const city = req.body.city;
  const agent = mastra.getAgent("weatherAgent");

  const result = await agent.generate(`What's the weather like in ${city}?`);

  return res.status(200).json(result.text);
}
```

## Create Test Page

Create a new Page, and add the example code:

```bash copy
touch pages/test.tsx
```

```typescript filename="pages/test.tsx" showLineNumbers copy
import { useState } from "react";

export default function Test() {
  const [result, setResult] = useState<string | null>(null);

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();

    const formData = new FormData(event.currentTarget);
    const city = formData.get("city")?.toString();

    const response = await fetch("/api/test", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ city })
    });

    const text = await response.json();
    setResult(text);
  }

  return (
    <>
      <h1>Test</h1>
      <form onSubmit={handleSubmit}>
        <input name="city" placeholder="Enter city" required />
        <button type="submit">Get Weather</button>
      </form>
      {result && <pre>{result}</pre>}
    </>
  );
}
```

> You can now navigate to `/test` in your browser to try it out.

Submitting **London** as the city would return a result similar to:

```plaintext
Agent response: The current weather in London is as follows:

- **Temperature:** 12.9°C (Feels like 9.7°C)
- **Humidity:** 63%
- **Wind Speed:** 14.7 km/h
- **Wind Gusts:** 32.4 km/h
- **Conditions:** Overcast

Let me know if you need more information!
```

  </Steps>
  </Tabs.Tab>
</Tabs>

## Next Steps

- [Deployment | With Next.js on Vercel](/docs/deployment/web-framework#with-nextjs-on-vercel)
