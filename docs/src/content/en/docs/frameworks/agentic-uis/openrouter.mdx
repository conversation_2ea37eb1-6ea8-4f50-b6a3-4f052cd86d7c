---
title: "Using with OpenRouter"
description: "Learn how to integrate OpenRouter with <PERSON><PERSON>"
---

import { Steps } from 'nextra/components'

# Use OpenRouter with <PERSON>stra

Integrate OpenRouter with <PERSON><PERSON> to leverage the numerous models available on OpenRouter.

<Steps>
## Initialize a Mastra Project

The simplest way to get started with <PERSON><PERSON> is to use the `mastra` CLI to initialize a new project:

```bash copy
npx create-mastra@latest
```

You'll be guided through prompts to set up your project. For this example, select:
- Name your project: my-mastra-openrouter-app
- Components: Agents (recommended)
- For default provider, select OpenAI (recommended) - we'll configure OpenRouter manually later
- Optionally include example code

## Configure OpenRouter

After creating your project with `create-mastra`, you'll find a `.env` file in your project root.
Since we selected OpenAI during setup, we'll configure OpenRouter manually: 

```bash filename=".env" copy
OPENROUTER_API_KEY=
```

We remove the `@ai-sdk/openai` package from the project:

```bash copy
npm uninstall @ai-sdk/openai
```

Then, we install the `@openrouter/ai-sdk-provider` package:

```bash copy
npm install @openrouter/ai-sdk-provider
```

## Configure your Agent to use OpenRouter

We will now configure our agent to use OpenRouter.

```typescript filename="src/mastra/agents/assistant.ts" copy showLineNumbers {4-6,11}
import { Agent } from "@mastra/core/agent";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";

const openrouter = createOpenRouter({
    apiKey: process.env.OPENROUTER_API_KEY,
})

export const assistant = new Agent({
    name: "assistant",
    instructions: "You are a helpful assistant.",
    model: openrouter("anthropic/claude-sonnet-4"),
})
```

Make sure to register your agent to the Mastra instance:

```typescript filename="src/mastra/index.ts" copy showLineNumbers {4}
import { assistant } from "./agents/assistant";

export const mastra = new Mastra({
    agents: { assistant }
})
```

## Run and Test your Agent

```bash copy
npm run dev
```

This will start the Mastra development server.

You can now test your agent by visiting [http://localhost:4111](http://localhost:4111) for the playground or via the Mastra API at [http://localhost:4111/api/agents/assistant/stream](http://localhost:4111/api/agents/assistant/stream).

</Steps>

## Advanced Configuration

For more control over your OpenRouter requests, you can pass additional configuration options.

### Provider-wide options:

You can pass provider-wide options to the OpenRouter provider:

```typescript filename="src/mastra/agents/assistant.ts" {6-10} copy showLineNumbers
import { Agent } from "@mastra/core/agent";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";

const openrouter = createOpenRouter({
    apiKey: process.env.OPENROUTER_API_KEY,
    extraBody: {
        reasoning: {
            max_tokens: 10,
        }
    }
})

export const assistant = new Agent({
    name: "assistant",
    instructions: "You are a helpful assistant.",
    model: openrouter("anthropic/claude-sonnet-4"),
})
```

### Model-specific options:

You can pass model-specific options to the OpenRouter provider:

```typescript filename="src/mastra/agents/assistant.ts" {11-17} copy showLineNumbers
import { Agent } from "@mastra/core/agent";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";

const openrouter = createOpenRouter({
    apiKey: process.env.OPENROUTER_API_KEY,
})

export const assistant = new Agent({
    name: "assistant",
    instructions: "You are a helpful assistant.",
    model: openrouter("anthropic/claude-sonnet-4", {
        extraBody: {
            reasoning: {
                max_tokens: 10,
            }
        }
    }),
})
```

### Provider-specific options:

You can pass provider-specific options to the OpenRouter provider:

```typescript copy showLineNumbers {7-12}
// Get a response with provider-specific options
const response = await assistant.generate([
  {
    role: 'system',
    content:
      'You are Chef Michel, a culinary expert specializing in ketogenic (keto) diet...',
    providerOptions: {
      // Provider-specific options - key can be 'anthropic' or 'openrouter'
      anthropic: {
        cacheControl: { type: 'ephemeral' },
      },
    },
  },
  {
    role: 'user',
    content: 'Can you suggest a keto breakfast?',
  },
]);
```
