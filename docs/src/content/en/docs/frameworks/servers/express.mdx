---
title: "Getting started with Mastra and Express | Mastra Guides"
description: A step-by-step guide to integrating Mastra with an Express backend.
---

import { Callout, Steps, Tabs, FileTree } from "nextra/components";

# Integrate Mastra in your Express project

Mastra integrates with Express, making it easy to:

- Build flexible APIs to serve AI-powered features
- Maintain full control over your server logic and routing
- Scale your backend independently of your frontend

Use this guide to scaffold and integrate Mastra with your Express project.

<Callout type="warning">
This setup is compatible with the following package versions:
- `express`: 4.x
- `@types/express`: 4.x

Type compatibility in 5.x can be inconsistent while `express` and `@types/express` evolve toward alignment.

</Callout>

<Steps>
## Install Mastra

Install the required Mastra packages:

<Tabs items={["npm", "yarn", "pnpm", "bun"]}>
  <Tabs.Tab>
    ```bash copy
    npm install mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    yarn add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    pnpm add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    bun add mastra@latest @mastra/core@latest @mastra/libsql@latest
    ```
  </Tabs.Tab>
</Tabs>

## Integrate Mastra

To integrate Mastra in your project, you have two options:

### 1. Use the One-Liner

Run the following command to quickly scaffold the default Weather agent with sensible defaults:

```bash copy
npx mastra@latest init --default
```

> See [mastra init](/reference/cli/init) for more information.

### 2. Use the Interactive CLI

If you prefer to customize the setup, run the `init` command and choose from the options when prompted:

```bash copy
npx mastra@latest init
```

Add the `dev` and `build` scripts to `package.json`:

```json filename="package.json"
{
  "scripts": {
    ...
    "dev": "mastra dev",
    "build": "mastra build"
  }
}
```

> If your project already uses `dev` and `build` scripts, we recommend using: `dev:mastra` and `build:mastra`.

## Initialize TypeScript

Create a `tsconfig.json` file in your project root with the following configuration:

```json filename="tsconfig.json" copy
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "bundler",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "outDir": "dist"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", ".mastra"]
}
```

> This TypeScript configuration is optimized for Mastra projects, using modern module resolution and strict type checking.

## Set Up API Key

```bash filename=".env" copy
OPENAI_API_KEY=<your-api-key>
```

> Each llm provider uses a different env var. See [Model Capabilities](/docs/getting-started/model-capability) for more information.

## Start the Mastra Dev Server

Start the Mastra dev server to expose your agents as REST endpoints:

<Tabs items={["npm", "CLI"]}>
  <Tabs.Tab>
    ```bash copy
    npm run dev
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    mastra dev
    ```
  </Tabs.Tab>
</Tabs>

> Once running, your agents are available locally. See [Local Development Environment](/docs/server-db/local-dev-playground) for more information.

## Example Express App

This example creates an `/api/weather` endpoint that expects a `city` query parameter.

```typescript filename="src/server.ts" showLineNumbers copy
import "dotenv/config";
import express, { Request, Response } from "express";

import { mastra } from "./mastra";

const app = express();
const port = process.env.PORT ?? 3000;

app.get("/api/weather", async (req: Request, res: Response) => {
  const { city } = req.query as { city?: string };

  if (!city) {
    return res.status(400).send("Missing 'city' query parameter");
  }

  const agent = mastra.getAgent("weatherAgent");

  try {
    const result = await agent.generate(`What's the weather like in ${city}?`);
    res.send(result.text);
  } catch (error) {
    console.error("Agent error:", error);
    res.status(500).send("An error occurred while processing your request");
  }
});

app.listen(port, () => {
  console.log(`Server listening on port ${port}`);
});
```

With the Mastra dev server running, start your Express app separately. For example:

```bash copy
npx tsx --watch src/server.ts --watch-dir src
```

You can now make a request to the endpoint using one of the following:

<Tabs items={["http", "curl"]}>
  <Tabs.Tab>
    ```bash copy
    http://localhost:3000/api/weather?city=London
    ```
  </Tabs.Tab>
  <Tabs.Tab>
    ```bash copy
    curl "http://localhost:3000/api/weather?city=London"
    ```
  </Tabs.Tab>
</Tabs>

You should see output similar to the below:

```plaintext
The current weather in London is as follows:

- **Temperature:** 12.9°C (Feels like 9.7°C)
- **Humidity:** 63%
- **Wind Speed:** 14.7 km/h
- **Wind Gusts:** 32.4 km/h
- **Conditions:** Overcast

Let me know if you need more information!
```

</Steps>

## Next Steps

- [Mastra Client SDK](/docs/deployment/client)
