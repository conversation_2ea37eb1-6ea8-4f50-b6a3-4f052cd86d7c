---
title: "Reference: createVectorQueryTool() | RAG | Mastra Tools Docs"
description: Documentation for the Vector Query Tool in Mastra, which facilitates semantic search over vector stores with filtering and reranking capabilities.
---

import { Callout } from "nextra/components";
import { Tabs } from "nextra/components";

# createVectorQueryTool()

The `createVectorQueryTool()` function creates a tool for semantic search over vector stores. It supports filtering, reranking, database-specific configurations, and integrates with various vector store backends.

## Basic Usage

```typescript
import { openai } from "@ai-sdk/openai";
import { createVectorQueryTool } from "@mastra/rag";

const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
});
```

## Parameters

<Callout>
  **Parameter Requirements:** Most fields can be set at creation as defaults.
  Some fields can be overridden at runtime via the runtime context or input. If
  a required field is missing from both creation and runtime, an error will be
  thrown. Note that `model`, `id`, and `description` can only be set at creation
  time.
</Callout>

<PropertiesTable
  content={[
    {
      name: "id",
      type: "string",
      description:
        "Custom ID for the tool. By default: 'VectorQuery {vectorStoreName} {indexName} Tool'. (Set at creation only.)",
      isOptional: true,
    },
    {
      name: "description",
      type: "string",
      description:
        "Custom description for the tool. By default: 'Access the knowledge base to find information needed to answer user questions' (Set at creation only.)",
      isOptional: true,
    },
    {
      name: "model",
      type: "EmbeddingModel",
      description:
        "Embedding model to use for vector search. (Set at creation only.)",
      isOptional: false,
    },
    {
      name: "vectorStoreName",
      type: "string",
      description:
        "Name of the vector store to query. (Can be set at creation or overridden at runtime.)",
      isOptional: false,
    },
    {
      name: "indexName",
      type: "string",
      description:
        "Name of the index within the vector store. (Can be set at creation or overridden at runtime.)",
      isOptional: false,
    },
    {
      name: "enableFilter",
      type: "boolean",
      description:
        "Enable filtering of results based on metadata. (Set at creation only, but will be automatically enabled if a filter is provided in the runtime context.)",
      isOptional: true,
      defaultValue: "false",
    },
    {
      name: "includeVectors",
      type: "boolean",
      description:
        "Include the embedding vectors in the results. (Can be set at creation or overridden at runtime.)",
      isOptional: true,
      defaultValue: "false",
    },
    {
      name: "includeSources",
      type: "boolean",
      description:
        "Include the full retrieval objects in the results. (Can be set at creation or overridden at runtime.)",
      isOptional: true,
      defaultValue: "true",
    },
    {
      name: "reranker",
      type: "RerankConfig",
      description:
        "Options for reranking results. (Can be set at creation or overridden at runtime.)",
      isOptional: true,
    },
    {
      name: "databaseConfig",
      type: "DatabaseConfig",
      description:
        "Database-specific configuration options for optimizing queries. (Can be set at creation or overridden at runtime.)",
      isOptional: true,
    },
  ]}
/>

### DatabaseConfig

The `DatabaseConfig` type allows you to specify database-specific configurations that are automatically applied to query operations. This enables you to take advantage of unique features and optimizations offered by different vector stores.

<PropertiesTable
  content={[
    {
      name: "pinecone",
      type: "PineconeConfig",
      description: "Configuration specific to Pinecone vector store",
      isOptional: true,
      properties: [
        {
          type: "object",
          parameters: [
            {
              name: "namespace",
              description: "Pinecone namespace for organizing vectors",
              isOptional: true,
              type: "string",
            },
            {
              name: "sparseVector",
              description: "Sparse vector for hybrid search",
              isOptional: true,
              type: "{ indices: number[]; values: number[]; }",
            },
          ],
        },
      ],
    },
    {
      name: "pgvector",
      type: "PgVectorConfig",
      description: "Configuration specific to PostgreSQL with pgvector extension",
      isOptional: true,
      properties: [
        {
          type: "object",
          parameters: [
            {
              name: "minScore",
              description: "Minimum similarity score threshold for results",
              isOptional: true,
              type: "number",
            },
            {
              name: "ef",
              description: "HNSW search parameter - controls accuracy vs speed tradeoff",
              isOptional: true,
              type: "number",
            },
            {
              name: "probes",
              description: "IVFFlat probe parameter - number of cells to visit during search",
              isOptional: true,
              type: "number",
            },
          ],
        },
      ],
    },
    {
      name: "chroma",
      type: "ChromaConfig",
      description: "Configuration specific to Chroma vector store",
      isOptional: true,
      properties: [
        {
          type: "object",
          parameters: [
            {
              name: "where",
              description: "Metadata filtering conditions",
              isOptional: true,
              type: "Record<string, any>",
            },
            {
              name: "whereDocument",
              description: "Document content filtering conditions",
              isOptional: true,
              type: "Record<string, any>",
            },
          ],
        },
      ],
    },
  ]}
/>

### RerankConfig

<PropertiesTable
  content={[
    {
      name: "model",
      type: "MastraLanguageModel",
      description: "Language model to use for reranking",
      isOptional: false,
    },
    {
      name: "options",
      type: "RerankerOptions",
      description: "Options for the reranking process",
      isOptional: true,
      properties: [
        {
          type: "object",
          parameters: [
            {
              name: "weights",
              description:
                "Weights for scoring components (semantic: 0.4, vector: 0.4, position: 0.2)",
              isOptional: true,
              type: "WeightConfig",
            },
            {
              name: "topK",
              description: "Number of top results to return",
              isOptional: true,
              type: "number",
              defaultValue: "3",
            },
          ],
        },
      ],
    },
  ]}
/>

## Returns

The tool returns an object with:

<PropertiesTable
  content={[
    {
      name: "relevantContext",
      type: "string",
      description: "Combined text from the most relevant document chunks",
    },
    {
      name: "sources",
      type: "QueryResult[]",
      description:
        "Array of full retrieval result objects. Each object contains all information needed to reference the original document, chunk, and similarity score.",
    },
  ]}
/>

### QueryResult object structure

```typescript
{
  id: string;         // Unique chunk/document identifier
  metadata: any;      // All metadata fields (document ID, etc.)
  vector: number[];   // Embedding vector (if available)
  score: number;      // Similarity score for this retrieval
  document: string;   // Full chunk/document text (if available)
}
```

## Default Tool Description

The default description focuses on:

- Finding relevant information in stored knowledge
- Answering user questions
- Retrieving factual content

## Result Handling

The tool determines the number of results to return based on the user's query, with a default of 10 results. This can be adjusted based on the query requirements.

## Example with Filters

```typescript
const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  enableFilter: true,
});
```

With filtering enabled, the tool processes queries to construct metadata filters that combine with semantic search. The process works as follows:

1. A user makes a query with specific filter requirements like "Find content where the 'version' field is greater than 2.0"
2. The agent analyzes the query and constructs the appropriate filters:
   ```typescript
   {
      "version": { "$gt": 2.0 }
   }
   ```

This agent-driven approach:

- Processes natural language queries into filter specifications
- Implements vector store-specific filter syntax
- Translates query terms to filter operators

For detailed filter syntax and store-specific capabilities, see the [Metadata Filters](../rag/metadata-filters) documentation.

For an example of how agent-driven filtering works, see the [Agent-Driven Metadata Filtering](../../../examples/rag/usage/filter-rag) example.

## Example with Reranking

```typescript
const queryTool = createVectorQueryTool({
  vectorStoreName: "milvus",
  indexName: "documentation",
  model: openai.embedding("text-embedding-3-small"),
  reranker: {
    model: openai("gpt-4o-mini"),
    options: {
      weights: {
        semantic: 0.5, // Semantic relevance weight
        vector: 0.3, // Vector similarity weight
        position: 0.2, // Original position weight
      },
      topK: 5,
    },
  },
});
```

Reranking improves result quality by combining:

- Semantic relevance: Using LLM-based scoring of text similarity
- Vector similarity: Original vector distance scores
- Position bias: Consideration of original result ordering
- Query analysis: Adjustments based on query characteristics

The reranker processes the initial vector search results and returns a reordered list optimized for relevance.

## Example with Custom Description

```typescript
const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  description:
    "Search through document archives to find relevant information for answering questions about company policies and procedures",
});
```

This example shows how to customize the tool description for a specific use case while maintaining its core purpose of information retrieval.

## Database-Specific Configuration Examples

The `databaseConfig` parameter allows you to leverage unique features and optimizations specific to each vector database. These configurations are automatically applied during query execution.

<Tabs items={['Pinecone', 'pgVector', 'Chroma', 'Multiple Configs']}>
  <Tabs.Tab>
    ### Pinecone Configuration

    ```typescript
    const pineconeQueryTool = createVectorQueryTool({
      vectorStoreName: "pinecone",
      indexName: "docs",
      model: openai.embedding("text-embedding-3-small"),
      databaseConfig: {
        pinecone: {
          namespace: "production",  // Organize vectors by environment
          sparseVector: {          // Enable hybrid search
            indices: [0, 1, 2, 3],
            values: [0.1, 0.2, 0.15, 0.05]
          }
        }
      }
    });
    ```

    **Pinecone Features:**
    - **Namespace**: Isolate different data sets within the same index
    - **Sparse Vector**: Combine dense and sparse embeddings for improved search quality
    - **Use Cases**: Multi-tenant applications, hybrid semantic search
  </Tabs.Tab>

  <Tabs.Tab>
    ### pgVector Configuration

    ```typescript
    const pgVectorQueryTool = createVectorQueryTool({
      vectorStoreName: "postgres",
      indexName: "embeddings",
      model: openai.embedding("text-embedding-3-small"),
      databaseConfig: {
        pgvector: {
          minScore: 0.7,    // Only return results above 70% similarity
          ef: 200,          // Higher value = better accuracy, slower search
          probes: 10        // For IVFFlat: more probes = better recall
        }
      }
    });
    ```

    **pgVector Features:**
    - **minScore**: Filter out low-quality matches
    - **ef (HNSW)**: Control accuracy vs speed for HNSW indexes
    - **probes (IVFFlat)**: Control recall vs speed for IVFFlat indexes
    - **Use Cases**: Performance tuning, quality filtering
  </Tabs.Tab>

  <Tabs.Tab>
    ### Chroma Configuration

    ```typescript
    const chromaQueryTool = createVectorQueryTool({
      vectorStoreName: "chroma",
      indexName: "documents",
      model: openai.embedding("text-embedding-3-small"),
      databaseConfig: {
        chroma: {
          where: {                    // Metadata filtering
            "category": "technical",
            "status": "published"
          },
          whereDocument: {            // Document content filtering
            "$contains": "API"
          }
        }
      }
    });
    ```

    **Chroma Features:**
    - **where**: Filter by metadata fields
    - **whereDocument**: Filter by document content
    - **Use Cases**: Advanced filtering, content-based search
  </Tabs.Tab>

  <Tabs.Tab>
    ### Multiple Database Configurations

    ```typescript
    // Configure for multiple databases (useful for dynamic stores)
    const multiDbQueryTool = createVectorQueryTool({
      vectorStoreName: "dynamic-store", // Will be set at runtime
      indexName: "docs",
      model: openai.embedding("text-embedding-3-small"),
      databaseConfig: {
        pinecone: {
          namespace: "default"
        },
        pgvector: {
          minScore: 0.8,
          ef: 150
        },
        chroma: {
          where: { "type": "documentation" }
        }
      }
    });
    ```

    **Multi-Config Benefits:**
    - Support multiple vector stores with one tool
    - Database-specific optimizations are automatically applied
    - Flexible deployment scenarios
  </Tabs.Tab>
</Tabs>

### Runtime Configuration Override

You can override database configurations at runtime to adapt to different scenarios:

```typescript
import { RuntimeContext } from '@mastra/core/runtime-context';

const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  databaseConfig: {
    pinecone: {
      namespace: "development"
    }
  }
});

// Override at runtime
const runtimeContext = new RuntimeContext();
runtimeContext.set('databaseConfig', {
  pinecone: {
    namespace: 'production'  // Switch to production namespace
  }
});

const response = await agent.generate(
  "Find information about deployment",
  { runtimeContext }
);
```

This approach allows you to:
- Switch between environments (dev/staging/prod)
- Adjust performance parameters based on load
- Apply different filtering strategies per request

## Example: Using Runtime Context

```typescript
const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
});
```

When using runtime context, provide required parameters at execution time via the runtime context:

```typescript
const runtimeContext = new RuntimeContext<{
  vectorStoreName: string;
  indexName: string;
  topK: number;
  filter: VectorFilter;
  databaseConfig: DatabaseConfig;
}>();
runtimeContext.set("vectorStoreName", "my-store");
runtimeContext.set("indexName", "my-index");
runtimeContext.set("topK", 5);
runtimeContext.set("filter", { category: "docs" });
runtimeContext.set("databaseConfig", {
  pinecone: { namespace: "runtime-namespace" }
});
runtimeContext.set("model", openai.embedding("text-embedding-3-small"));

const response = await agent.generate(
  "Find documentation from the knowledge base.",
  {
    runtimeContext,
  },
);
```

For more information on runtime context, please see:

- [Runtime Variables](../../docs/agents/runtime-variables)
- [Dynamic Context](../../docs/tools-mcp/dynamic-context)

## Tool Details

The tool is created with:

- **ID**: `VectorQuery {vectorStoreName} {indexName} Tool`
- **Input Schema**: Requires queryText and filter objects
- **Output Schema**: Returns relevantContext string

## Related

- [rerank()](../rag/rerank)
- [createGraphRAGTool](./graph-rag-tool)
