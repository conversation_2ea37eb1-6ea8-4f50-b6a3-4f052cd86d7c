---
title: "Reference: voice.answer() | Voice Providers | Mastra Docs"
description: "Documentation for the answer() method available in real-time voice providers, which triggers the voice provider to generate a response."
---

# voice.answer()

The `answer()` method is used in real-time voice providers to trigger the AI to generate a response. This method is particularly useful in speech-to-speech conversations where you need to explicitly signal the AI to respond after receiving user input.

## Usage Example

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { getMicrophoneStream } from "@mastra/node-audio";
import Speaker from "@mastra/node-speaker";

const speaker = new Speaker({
  sampleRate: 24100, // Audio sample rate in Hz - standard for high-quality audio on MacBook Pro
  channels: 1, // Mono audio output (as opposed to stereo which would be 2)
  bitDepth: 16, // Bit depth for audio quality - CD quality standard (16-bit resolution)
});

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o",
    apiKey: process.env.OPENAI_API_KEY,
  },
  speaker: "alloy", // Default voice
});
// Connect to the real-time service
await voice.connect();
// Register event listener for responses
voice.on("speaker", (stream) => {
  // Handle audio response
  stream.pipe(speaker);
});
// Send user audio input
const microphoneStream = getMicrophoneStream();
await voice.send(microphoneStream);
// Trigger the AI to respond
await voice.answer();
```

## Parameters

<br />
<PropertiesTable
  content={[
    {
      name: "options",
      type: "Record<string, unknown>",
      description: "Provider-specific options for the response",
      isOptional: true,
    },
  ]}
/>

## Return Value

Returns a `Promise<void>` that resolves when the response has been triggered.

## Notes

- This method is only implemented by real-time voice providers that support speech-to-speech capabilities
- If called on a voice provider that doesn't support this functionality, it will log a warning and resolve immediately
- The response audio will typically be emitted through the 'speaking' event rather than returned directly
- For providers that support it, you can use this method to send a specific response instead of having the AI generate one
- This method is commonly used in conjunction with `send()` to create a conversational flow
