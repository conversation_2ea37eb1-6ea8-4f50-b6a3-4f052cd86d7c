---
title: "Reference: Murf Voice | Voice Providers | Mastra Docs"
description: "Documentation for the Murf voice implementation, providing text-to-speech capabilities."
---

# Murf

The Murf voice implementation in Mastra provides text-to-speech (TTS) capabilities using Murf's AI voice service. It supports multiple voices across different languages.

## Usage Example

```typescript
import { MurfVoice } from "@mastra/voice-murf";

// Initialize with default configuration (uses MURF_API_KEY environment variable)
const voice = new MurfVoice();

// Initialize with custom configuration
const voice = new MurfVoice({
  speechModel: {
    name: "GEN2",
    apiKey: "your-api-key",
    properties: {
      format: "MP3",
      rate: 1.0,
      pitch: 1.0,
      sampleRate: 48000,
      channelType: "STEREO",
    },
  },
  speaker: "en-US-cooper",
});

// Text-to-Speech with default settings
const audioStream = await voice.speak("Hello, world!");

// Text-to-Speech with custom properties
const audioStream = await voice.speak("Hello, world!", {
  speaker: "en-UK-hazel",
  properties: {
    format: "WAV",
    rate: 1.2,
    style: "casual",
  },
});

// Get available voices
const voices = await voice.getSpeakers();
```

## Constructor Parameters

<PropertiesTable
  content={[
    {
      name: "speechModel",
      type: "MurfConfig",
      description: "Configuration for text-to-speech functionality",
      isOptional: true,
      defaultValue: "{ name: 'GEN2' }",
    },
    {
      name: "speaker",
      type: "string",
      description: "Default voice ID to use for text-to-speech",
      isOptional: true,
      defaultValue: "'en-UK-hazel'",
    },
  ]}
/>

### MurfConfig

<PropertiesTable
  content={[
    {
      name: "name",
      type: "'GEN1' | 'GEN2'",
      description: "The Murf model generation to use",
      isOptional: false,
      defaultValue: "'GEN2'",
    },
    {
      name: "apiKey",
      type: "string",
      description:
        "Murf API key. Falls back to MURF_API_KEY environment variable",
      isOptional: true,
    },
    {
      name: "properties",
      type: "object",
      description: "Default properties for all speech synthesis requests",
      isOptional: true,
    },
  ]}
/>

### Speech Properties

<PropertiesTable
  content={[
    {
      name: "style",
      type: "string",
      description: "Speaking style for the voice",
      isOptional: true,
    },
    {
      name: "rate",
      type: "number",
      description: "Speech rate multiplier",
      isOptional: true,
    },
    {
      name: "pitch",
      type: "number",
      description: "Voice pitch adjustment",
      isOptional: true,
    },
    {
      name: "sampleRate",
      type: "8000 | 24000 | 44100 | 48000",
      description: "Audio sample rate in Hz",
      isOptional: true,
    },
    {
      name: "format",
      type: "'MP3' | 'WAV' | 'FLAC' | 'ALAW' | 'ULAW'",
      description: "Output audio format",
      isOptional: true,
    },
    {
      name: "channelType",
      type: "'STEREO' | 'MONO'",
      description: "Audio channel configuration",
      isOptional: true,
    },
    {
      name: "pronunciationDictionary",
      type: "Record<string, string>",
      description: "Custom pronunciation mappings",
      isOptional: true,
    },
    {
      name: "encodeAsBase64",
      type: "boolean",
      description: "Whether to encode the audio as base64",
      isOptional: true,
    },
    {
      name: "variation",
      type: "number",
      description: "Voice variation parameter",
      isOptional: true,
    },
    {
      name: "audioDuration",
      type: "number",
      description: "Target audio duration in seconds",
      isOptional: true,
    },
    {
      name: "multiNativeLocale",
      type: "string",
      description: "Locale for multilingual support",
      isOptional: true,
    },
  ]}
/>

## Methods

### speak()

Converts text to speech using Murf's API.

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string | NodeJS.ReadableStream",
      description:
        "Text to convert to speech. If a stream is provided, it will be converted to text first.",
      isOptional: false,
    },
    {
      name: "options",
      type: "object",
      description: "Speech synthesis options",
      isOptional: true,
    },
    {
      name: "options.speaker",
      type: "string",
      description: "Override the default speaker for this request",
      isOptional: true,
    },
    {
      name: "options.properties",
      type: "object",
      description: "Override default speech properties for this request",
      isOptional: true,
    },
  ]}
/>

Returns: `Promise<NodeJS.ReadableStream>`

### getSpeakers()

Returns an array of available voice options, where each node contains:

<PropertiesTable
  content={[
    {
      name: "voiceId",
      type: "string",
      description: "Unique identifier for the voice",
      isOptional: false,
    },
    {
      name: "name",
      type: "string",
      description: "Display name of the voice",
      isOptional: false,
    },
    {
      name: "language",
      type: "string",
      description: "Language code for the voice",
      isOptional: false,
    },
    {
      name: "gender",
      type: "string",
      description: "Gender of the voice",
      isOptional: false,
    },
  ]}
/>

### listen()

This method is not supported by Murf and will throw an error. Murf does not provide speech-to-text functionality.

## Important Notes

1. A Murf API key is required. Set it via the `MURF_API_KEY` environment variable or pass it in the constructor.
2. The service uses GEN2 as the default model version.
3. Speech properties can be set at the constructor level and overridden per request.
4. The service supports extensive audio customization through properties like format, sample rate, and channel type.
5. Speech-to-text functionality is not supported.
