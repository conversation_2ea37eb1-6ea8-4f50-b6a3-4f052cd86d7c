---
title: "Reference: voice.addTools() | Voice Providers | Mastra Docs"
description: "Documentation for the addTools() method available in voice providers, which equips voice models with function calling capabilities."
---

# voice.addTools()

The `addTools()` method equips a voice provider with tools (functions) that can be called by the model during real-time interactions. This enables voice assistants to perform actions like searching for information, making calculations, or interacting with external systems.

## Usage Example

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// Define tools
const weatherTool = createTool({
  id: "getWeather",
  description: "Get the current weather for a location",
  inputSchema: z.object({
    location: z.string().describe("The city and state, e.g. San Francisco, CA"),
  }),
  outputSchema: z.object({
    message: z.string(),
  }),
  execute: async ({ context }) => {
    // Fetch weather data from an API
    const response = await fetch(
      `https://api.weather.com?location=${encodeURIComponent(context.location)}`,
    );
    const data = await response.json();
    return {
      message: `The current temperature in ${context.location} is ${data.temperature}°F with ${data.conditions}.`,
    };
  },
});

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o-mini-realtime",
    apiKey: process.env.OPENAI_API_KEY,
  },
});

// Add tools to the voice provider
voice.addTools({
  getWeather: weatherTool,
});

// Connect to the real-time service
await voice.connect();
```

## Parameters

<br />
<PropertiesTable
  content={[
    {
      name: "tools",
      type: "ToolsInput",
      description:
        "Object containing tool definitions that can be called by the voice model",
      isOptional: false,
    },
  ]}
/>

## Return Value

This method does not return a value.

## Notes

- Tools must follow the Mastra tool format with name, description, input schema, and execute function
- This method is primarily used with real-time voice providers that support function calling
- If called on a voice provider that doesn't support tools, it will log a warning and do nothing
- Tools added with this method are typically combined with any tools provided by an associated Agent
- For best results, add tools before starting a conversation (before calling `connect()`)
- The voice provider will automatically handle the invocation of tool handlers when the model decides to use them
- Multiple calls to `addTools()` may either replace or merge with existing tools, depending on the provider implementation
