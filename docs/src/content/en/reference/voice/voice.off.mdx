---
title: "Reference: voice.off() | Voice Providers | Mastra Docs"
description: "Documentation for the off() method available in voice providers, which removes event listeners for voice events."
---

# voice.off()

The `off()` method removes event listeners previously registered with the `on()` method. This is particularly useful for cleaning up resources and preventing memory leaks in long-running applications with real-time voice capabilities.

## Usage Example

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import chalk from "chalk";

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o-mini-realtime",
    apiKey: process.env.OPENAI_API_KEY,
  },
});

// Connect to the real-time service
await voice.connect();

// Define the callback function
const writingCallback = ({ text, role }) => {
  if (role === "user") {
    process.stdout.write(chalk.green(text));
  } else {
    process.stdout.write(chalk.blue(text));
  }
};

// Register event listener
voice.on("writing", writingCallback);

// Later, when you want to remove the listener
voice.off("writing", writingCallback);
```

## Parameters

<br />
<PropertiesTable
  content={[
    {
      name: "event",
      type: "string",
      description:
        "Name of the event to stop listening for (e.g., 'speaking', 'writing', 'error')",
      isOptional: false,
    },
    {
      name: "callback",
      type: "function",
      description: "The same callback function that was passed to on()",
      isOptional: false,
    },
  ]}
/>

## Return Value

This method does not return a value.

## Notes

- The callback passed to `off()` must be the same function reference that was passed to `on()`
- If the callback is not found, the method will have no effect
- This method is primarily used with real-time voice providers that support event-based communication
- If called on a voice provider that doesn't support events, it will log a warning and do nothing
- Removing event listeners is important for preventing memory leaks in long-running applications
