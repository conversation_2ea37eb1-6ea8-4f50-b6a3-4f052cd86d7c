---
title: "Reference: Play<PERSON><PERSON> Voice | Voice Providers | Mastra Docs"
description: "Documentation for the PlayAI voice implementation, providing text-to-speech capabilities."
---

# PlayAI

The PlayAI voice implementation in Mastra provides text-to-speech capabilities using PlayAI's API.

## Usage Example

```typescript
import { PlayAIVoice } from "@mastra/voice-playai";

// Initialize with default configuration (uses PLAYAI_API_KEY environment variable and PLAYAI_USER_ID environment variable)
const voice = new PlayAIVoice();

// Initialize with default configuration
const voice = new PlayAIVoice({
  speechModel: {
    name: "PlayDialog",
    apiKey: process.env.PLAYAI_API_KEY,
    userId: process.env.PLAYAI_USER_ID,
  },
  speaker: "<PERSON>", // Default voice
});

// Convert text to speech with a specific voice
const audioStream = await voice.speak("Hello, world!", {
  speaker:
    "s3://voice-cloning-zero-shot/b27bc13e-996f-4841-b584-4d35801aea98/original/manifest.json", // <PERSON> voice
});
```

## Constructor Parameters

<PropertiesTable
  content={[
    {
      name: "speechModel",
      type: "PlayAIConfig",
      description: "Configuration for text-to-speech functionality",
      isOptional: true,
      defaultValue: "{ name: 'PlayDialog' }",
    },
    {
      name: "speaker",
      type: "string",
      description: "Default voice ID to use for speech synthesis",
      isOptional: true,
      defaultValue: "First available voice ID",
    },
  ]}
/>

### PlayAIConfig

<PropertiesTable
  content={[
    {
      name: "name",
      type: "'PlayDialog' | 'Play3.0-mini'",
      description: "The PlayAI model to use",
      isOptional: true,
      defaultValue: "'PlayDialog'",
    },
    {
      name: "apiKey",
      type: "string",
      description:
        "PlayAI API key. Falls back to PLAYAI_API_KEY environment variable",
      isOptional: true,
    },
    {
      name: "userId",
      type: "string",
      description:
        "PlayAI user ID. Falls back to PLAYAI_USER_ID environment variable",
      isOptional: true,
    },
  ]}
/>

## Methods

### speak()

Converts text to speech using the configured speech model and voice.

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string | NodeJS.ReadableStream",
      description:
        "Text to convert to speech. If a stream is provided, it will be converted to text first.",
      isOptional: false,
    },
    {
      name: "options.speaker",
      type: "string",
      description: "Override the default speaker for this request",
      isOptional: true,
      defaultValue: "Constructor's speaker value",
    },
  ]}
/>

Returns: `Promise<NodeJS.ReadableStream>`.

### getSpeakers()

Returns an array of available voice options, where each node contains:

<PropertiesTable
  content={[
    {
      name: "name",
      type: "string",
      description: "Name of the voice",
      isOptional: false,
    },
    {
      name: "accent",
      type: "string",
      description: "Accent of the voice (e.g., 'US', 'British', 'Australian')",
      isOptional: false,
    },
    {
      name: "gender",
      type: "'M' | 'F'",
      description: "Gender of the voice",
      isOptional: false,
    },
    {
      name: "age",
      type: "'Young' | 'Middle' | 'Old'",
      description: "Age category of the voice",
      isOptional: false,
    },
    {
      name: "style",
      type: "'Conversational' | 'Narrative'",
      description: "Speaking style of the voice",
      isOptional: false,
    },
    {
      name: "voiceId",
      type: "string",
      description: "Unique identifier for the voice",
      isOptional: false,
    },
  ]}
/>
### listen()

This method is not supported by PlayAI and will throw an error. PlayAI does not provide speech-to-text functionality.

## Notes

- PlayAI requires both an API key and a user ID for authentication
- The service offers two models: 'PlayDialog' and 'Play3.0-mini'
- Each voice has a unique S3 manifest ID that must be used when making API calls
