---
title: "Reference: voice.connect() | Voice Providers | Mastra Docs"
description: "Documentation for the connect() method available in real-time voice providers, which establishes a connection for speech-to-speech communication."
---

# voice.connect()

The `connect()` method establishes a WebSocket or WebRTC connection for real-time speech-to-speech communication. This method must be called before using other real-time features like `send()` or `answer()`.

## Usage Example

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import Speaker from "@mastra/node-speaker";

const speaker = new Speaker({
  sampleRate: 24100, // Audio sample rate in Hz - standard for high-quality audio on MacBook Pro
  channels: 1, // Mono audio output (as opposed to stereo which would be 2)
  bitDepth: 16, // Bit depth for audio quality - CD quality standard (16-bit resolution)
});

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o-mini-realtime",
    apiKey: process.env.OPENAI_API_KEY,
    options: {
      sessionConfig: {
        turn_detection: {
          type: "server_vad",
          threshold: 0.6,
          silence_duration_ms: 1200,
        },
      },
    },
  },
  speaker: "alloy", // Default voice
});
// Connect to the real-time service
await voice.connect();
// Now you can use real-time features
voice.on("speaker", (stream) => {
  stream.pipe(speaker);
});
// With connection options
await voice.connect({
  timeout: 10000, // 10 seconds timeout
  reconnect: true,
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "options",
      type: "Record<string, unknown>",
      description: "Provider-specific connection options",
      isOptional: true,
    },
  ]}
/>

## Return Value

Returns a `Promise<void>` that resolves when the connection is successfully established.

## Provider-Specific Options

Each real-time voice provider may support different options for the `connect()` method:

### OpenAI Realtime

<PropertiesTable
  content={[
    {
      name: "options.timeout",
      type: "number",
      description: "Connection timeout in milliseconds",
      isOptional: true,
      defaultValue: "30000",
    },
    {
      name: "options.reconnect",
      type: "boolean",
      description: "Whether to automatically reconnect on connection loss",
      isOptional: true,
      defaultValue: "false",
    },
  ]}
/>

## Using with CompositeVoice

When using `CompositeVoice`, the `connect()` method delegates to the configured real-time provider:

```typescript
import { CompositeVoice } from "@mastra/core/voice";
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
const realtimeVoice = new OpenAIRealtimeVoice();
const voice = new CompositeVoice({
  realtimeProvider: realtimeVoice,
});
// This will use the OpenAIRealtimeVoice provider
await voice.connect();
```

## Notes

- This method is only implemented by real-time voice providers that support speech-to-speech capabilities
- If called on a voice provider that doesn't support this functionality, it will log a warning and resolve immediately
- The connection must be established before using other real-time methods like `send()` or `answer()`
- When you're done with the voice instance, call `close()` to properly clean up resources
- Some providers may automatically reconnect on connection loss, depending on their implementation
- Connection errors will typically be thrown as exceptions that should be caught and handled

## Related Methods

- [voice.send()](./voice.send) - Sends audio data to the voice provider
- [voice.answer()](./voice.answer) - Triggers the voice provider to respond
- [voice.close()](./voice.close) - Disconnects from the real-time service
- [voice.on()](./voice.on) - Registers an event listener for voice events
