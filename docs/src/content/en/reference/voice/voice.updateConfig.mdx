---
title: "Reference: voice.updateConfig() | Voice Providers | Mastra Docs"
description: "Documentation for the updateConfig() method available in voice providers, which updates the configuration of a voice provider at runtime."
---

# voice.updateConfig()

The `updateConfig()` method allows you to update the configuration of a voice provider at runtime. This is useful for changing voice settings, API keys, or other provider-specific options without creating a new instance.

## Usage Example

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o-mini-realtime",
    apiKey: process.env.OPENAI_API_KEY,
  },
  speaker: "alloy",
});

// Connect to the real-time service
await voice.connect();

// Later, update the configuration
voice.updateConfig({
  voice: "nova", // Change the default voice
  turn_detection: {
    type: "server_vad",
    threshold: 0.5,
    silence_duration_ms: 1000,
  },
});

// The next speak() call will use the new configuration
await voice.speak("Hello with my new voice!");
```

## Parameters

<br />
<PropertiesTable
  content={[
    {
      name: "options",
      type: "Record<string, unknown>",
      description:
        "Configuration options to update. The specific properties depend on the voice provider.",
      isOptional: false,
    },
  ]}
/>

## Return Value

This method does not return a value.

## Configuration Options

Different voice providers support different configuration options:

### OpenAI Realtime

<br />
<PropertiesTable
  content={[
    {
      name: "voice",
      type: "string",
      description:
        "Voice ID to use for speech synthesis (e.g., 'alloy', 'echo', 'nova')",
      isOptional: true,
    },
    {
      name: "turn_detection",
      type: "{ type: string, threshold?: number, silence_duration_ms?: number }",
      description:
        "Configuration for detecting when a user has finished speaking",
      isOptional: true,
    },
  ]}
/>

## Notes

- The default implementation logs a warning if the provider doesn't support this method
- Configuration updates are typically applied to subsequent operations, not ongoing ones
- Not all properties that can be set in the constructor can be updated at runtime
- The specific behavior depends on the voice provider implementation
- For real-time voice providers, some configuration changes may require reconnecting to the service
