---
title: "Reference: OtelConfig | Mastra Observability Docs"
description: Documentation for the OtelConfig object, which configures OpenTelemetry instrumentation, tracing, and exporting behavior.
---

# `OtelConfig`

The `OtelConfig` object is used to configure OpenTelemetry instrumentation, tracing, and exporting behavior within your application. By adjusting its properties, you can control how telemetry data (such as traces) is collected, sampled, and exported.

To use the `OtelConfig` within Mastra, pass it as the value of the `telemetry` key when initializing Mastra. This will configure <PERSON><PERSON> to use your custom OpenTelemetry settings for tracing and instrumentation.

```typescript showLineNumbers copy
import { Ma<PERSON> } from "mastra";

const otelConfig: OtelConfig = {
  serviceName: "my-awesome-service",
  enabled: true,
  sampling: {
    type: "ratio",
    probability: 0.5,
  },
  export: {
    type: "otlp",
    endpoint: "https://otel-collector.example.com/v1/traces",
    headers: {
      Authorization: "Bearer YOUR_TOKEN_HERE",
    },
  },
};
```

### Properties

<PropertiesTable
  content={[
    {
      name: "serviceName",
      type: "string",
      isOptional: true,
      default: "default-service",
      description:
        "Human-readable name used to identify your service in telemetry backends.",
    },
    {
      name: "enabled",
      type: "boolean",
      isOptional: true,
      default: "true",
      description: "Whether telemetry collection and export are enabled.",
    },
    {
      name: "sampling",
      type: "SamplingStrategy",
      isOptional: true,
      description:
        "Defines the sampling strategy for traces, controlling how much data is collected.",
      properties: [
        {
          name: "type",
          type: `'ratio' | 'always_on' | 'always_off' | 'parent_based'`,
          description: "Specifies the sampling strategy type.",
        },
        {
          name: "probability",
          type: "number (0.0 to 1.0)",
          isOptional: true,
          description:
            "For `ratio` or `parent_based` strategies, defines the sampling probability.",
        },
        {
          name: "root",
          type: "object",
          isOptional: true,
          description:
            "For `parent_based` strategy, configures root-level probability sampling.",
          properties: [
            {
              name: "probability",
              type: "number (0.0 to 1.0)",
              isOptional: true,
              description:
                "Sampling probability for root traces in `parent_based` strategy.",
            },
          ],
        },
      ],
    },
    {
      name: "export",
      type: "object",
      isOptional: true,
      description: "Configuration for exporting collected telemetry data.",
      properties: [
        {
          name: "type",
          type: `'otlp' | 'console'`,
          description:
            "Specifies the exporter type. Use `otlp` for external exporters or `console` for development.",
        },
        {
          name: "endpoint",
          type: "string",
          isOptional: true,
          description:
            "For `otlp` type, the OTLP endpoint URL to send traces to.",
        },
        {
          name: "headers",
          type: "Record<string, string>",
          isOptional: true,
          description:
            "Additional headers to send with OTLP requests, useful for authentication or routing.",
        },
      ],
    },
  ]}
/>
