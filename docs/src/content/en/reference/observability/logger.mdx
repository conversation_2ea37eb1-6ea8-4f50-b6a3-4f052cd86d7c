---
title: "Reference: Logger Instance | Mastra Observability Docs"
description: Documentation for Logger instances, which provide methods to record events at various severity levels.
---

# Logger Instance

A Logger instance is created by `new PinoLogger()` and provides methods to record events at various severity levels. Depending on the logger type, messages may be written to the console, file, or an external service.

## Example

```typescript showLineNumbers copy
// Using a console logger
const logger = new PinoLogger({ name: "<PERSON><PERSON>", level: "info" });

logger.debug("Debug message"); // Won't be logged because level is INFO
logger.info({
  message: "User action occurred",
  destinationPath: "user-actions",
  type: "AGENT",
}); // Logged
logger.error("An error occurred"); // Logged as ERROR
```

## Methods

<PropertiesTable
  content={[
    {
      name: "debug",
      type: "(message: BaseLogMessage | string, ...args: any[]) => void | Promise<void>",
      description: "Write a DEBUG-level log. Only recorded if level ≤ DEBUG.",
    },
    {
      name: "info",
      type: "(message: BaseLogMessage | string, ...args: any[]) => void | Promise<void>",
      description: "Write an INFO-level log. Only recorded if level ≤ INFO.",
    },
    {
      name: "warn",
      type: "(message: BaseLogMessage | string, ...args: any[]) => void | Promise<void>",
      description: "Write a WARN-level log. Only recorded if level ≤ WARN.",
    },
    {
      name: "error",
      type: "(message: BaseLogMessage | string, ...args: any[]) => void | Promise<void>",
      description: "Write an ERROR-level log. Only recorded if level ≤ ERROR.",
    },
    {
      name: "cleanup",
      type: "() => Promise<void>",
      isOptional: true,
      description:
        "Cleanup resources held by the logger (e.g., network connections for Upstash). Not all loggers implement this.",
    },
  ]}
/>

**Note:** Some loggers require a `BaseLogMessage` object (with `message`, `destinationPath`, `type` fields). For instance, the `File` and `Upstash` loggers need structured messages.

## File Transport (Structured Logs)

```typescript showLineNumbers copy
import { FileTransport } from "@mastra/loggers/file";

const fileLogger = new PinoLogger({
  name: "Mastra",
  transports: { file: new FileTransport({ path: "test-dir/test.log" }) },
  level: "warn",
});

fileLogger.warn("Low disk space", {
  destinationPath: "system",
  type: "WORKFLOW",
});
```

## Upstash Logger (Remote Log Drain)

```typescript showLineNumbers copy
import { UpstashTransport } from "@mastra/loggers/upstash";

const logger = new PinoLogger({
  name: "Mastra",
  transports: {
    upstash: new UpstashTransport({
      listName: "production-logs",
      upstashUrl: process.env.UPSTASH_URL!,
      upstashToken: process.env.UPSTASH_TOKEN!,
    }),
  },
  level: "info",
});

logger.info({
  message: "User signed in",
  destinationPath: "auth",
  type: "AGENT",
  runId: "run_123",
});
```

## Custom Transport

You can create custom transports using the `createCustomTransport` utility to integrate with any logging service or stream.

### Example: Sentry Integration

```typescript showLineNumbers copy
import { createCustomTransport } from "@mastra/core/loggers";
import pinoSentry from 'pino-sentry-transport';

const sentryStream = await pinoSentry({
  sentry: {
    dsn: 'YOUR_SENTRY_DSN',
    _experiments: {
      enableLogs: true,
    },
  },
});

const customTransport = createCustomTransport(sentryStream);

const logger = new PinoLogger({
  name: "Mastra",
  transports: { sentry: customTransport },
  level: "info",
});
```