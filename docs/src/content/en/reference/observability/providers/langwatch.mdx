---
title: "Reference: LangWatch Integration | Mastra Observability Docs"
description: Documentation for integrating LangWatch with Mastra, a specialized observability platform for LLM applications.
---

# LangWatch

LangWatch is a specialized observability platform for LLM applications.

## Configuration

To use LangWatch with <PERSON><PERSON>, configure these environment variables:

```env
LANGWATCH_API_KEY=your_api_key
```

## Implementation

Here's how to configure <PERSON><PERSON> to use LangWatch:

```typescript
import { Mastra } from "@mastra/core";
import { LangWatchExporter } from "langwatch";

export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "ai", // this must be set to "ai" so that the LangWatchExporter thinks it's an AI SDK trace
    enabled: true,
    export: {
      type: "custom",
      exporter: new LangWatchExporter({
        apiKey: process.env.LANGWATCH_API_KEY
      }),
    },
  },
});
```

## Dashboard

Access your LangWatch dashboard at [app.langwatch.ai](https://app.langwatch.ai)
