---
title: "Reference: Traceloop Integration | Mastra Observability Docs"
description: Documentation for integrating Traceloop with Mastra, an OpenTelemetry-native observability platform for LLM applications.
---

# Traceloop

Traceloop is an OpenTelemetry-native observability platform specifically designed for LLM applications.

## Configuration

To use Traceloop with <PERSON><PERSON>, configure these environment variables:

```env
OTEL_EXPORTER_OTLP_ENDPOINT=https://api.traceloop.com
OTEL_EXPORTER_OTLP_HEADERS="Authorization=Bearer your_api_key, x-traceloop-destination-id=your_destination_id"
```

## Implementation

Here's how to configure <PERSON><PERSON> to use Traceloop:

```typescript
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "your-service-name",
    enabled: true,
    export: {
      type: "otlp",
    },
  },
});
```

## Dashboard

Access your traces and analytics in the Traceloop dashboard at [app.traceloop.com](https://app.traceloop.com)
