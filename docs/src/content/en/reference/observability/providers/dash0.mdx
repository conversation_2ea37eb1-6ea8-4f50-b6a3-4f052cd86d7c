---
title: "Reference: Dash0 Integration | Mastra Observability Docs"
description: Documentation for integrating Ma<PERSON> with Dash0, an Open Telementry native observability solution.
---

# Dash0

Dash0, an Open Telementry native observability solution that provides full-stack monitoring capabilities as well as integrations with other CNCF projects like Perses and Prometheus.

## Configuration

To use Dash0 with <PERSON><PERSON>, configure these environment variables:

```env
OTEL_EXPORTER_OTLP_ENDPOINT=https://ingress.<region>.dash0.com
OTEL_EXPORTER_OTLP_HEADERS=Authorization=Bearer <your-auth-token>, Dash0-Dataset=<optional-dataset>
```

## Implementation

Here's how to configure <PERSON><PERSON> to use Dash0:

```typescript
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "your-service-name",
    enabled: true,
    export: {
      type: "otlp",
    },
  },
});
```

## Dashboard

Access your Dash0 dashboards at [dash0.com](https://www.dash0.com/) and find out how to do more [Distributed Tracing](https://www.dash0.com/distributed-tracing) integrations in the [Dash0 Integration Hub](https://www.dash0.com/hub/integrations)
