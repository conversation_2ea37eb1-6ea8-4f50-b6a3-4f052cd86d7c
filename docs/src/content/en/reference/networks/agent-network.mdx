---
title: "AgentNetwork (Experimental)"
description: "Reference documentation for the AgentNetwork class"
---

# AgentNetwork (Experimental)

> **Note:** The AgentNetwork feature is experimental and may change in future releases.

The `AgentNetwork` class provides a way to create a network of specialized agents that can collaborate to solve complex tasks. Unlike Workflows, which require explicit control over execution paths, AgentNetwork uses an LLM-based router to dynamically determine which agent to call next.

## Key Concepts

- **LLM-based Routing**: AgentNetwork exclusively uses an LLM to figure out the best way to use your agents
- **Agent Collaboration**: Multiple specialized agents can work together to solve complex tasks
- **Dynamic Decision Making**: The router decides which agent to call based on the task requirements

## Usage

```typescript
import { AgentNetwork } from "@mastra/core/network";
import { openai } from "@mastra/openai";

// Create specialized agents
const webSearchAgent = new Agent({
  name: "Web Search Agent",
  instructions: "You search the web for information.",
  model: openai("gpt-4o"),
  tools: {
    /* web search tools */
  },
});

const dataAnalysisAgent = new Agent({
  name: "Data Analysis Agent",
  instructions: "You analyze data and provide insights.",
  model: openai("gpt-4o"),
  tools: {
    /* data analysis tools */
  },
});

// Create the network
const researchNetwork = new AgentNetwork({
  name: "Research Network",
  instructions: "Coordinate specialized agents to research topics thoroughly.",
  model: openai("gpt-4o"),
  agents: [webSearchAgent, dataAnalysisAgent],
});

// Use the network
const result = await researchNetwork.generate(
  "Research the impact of climate change on agriculture",
);
console.log(result.text);
```

## Constructor

```typescript
constructor(config: AgentNetworkConfig)
```

### Parameters

- `config`: Configuration object for the AgentNetwork
  - `name`: Name of the network
  - `instructions`: Instructions for the routing agent
  - `model`: Language model to use for routing
  - `agents`: Array of specialized agents in the network

## Methods

### generate()

Generates a response using the agent network. This method has replaced the deprecated `run()` method for consistency with the rest of the codebase.

```typescript
async generate(
  messages: string | string[] | CoreMessage[],
  args?: AgentGenerateOptions
): Promise<GenerateTextResult>
```

### stream()

Streams a response using the agent network.

```typescript
async stream(
  messages: string | string[] | CoreMessage[],
  args?: AgentStreamOptions
): Promise<StreamTextResult>
```

### getRoutingAgent()

Returns the routing agent used by the network.

```typescript
getRoutingAgent(): Agent
```

### getAgents()

Returns the array of specialized agents in the network.

```typescript
getAgents(): Agent[]
```

### getAgentHistory()

Returns the history of interactions for a specific agent.

```typescript
getAgentHistory(agentId: string): Array<{
  input: string;
  output: string;
  timestamp: string;
}>
```

### getAgentInteractionHistory()

Returns the history of all agent interactions that have occurred in the network.

```typescript
getAgentInteractionHistory(): Record<
  string,
  Array<{
    input: string;
    output: string;
    timestamp: string;
  }>
>
```

### getAgentInteractionSummary()

Returns a formatted summary of agent interactions in chronological order.

```typescript
getAgentInteractionSummary(): string
```

## When to Use AgentNetwork vs Workflows

- **Use AgentNetwork when:** You want the AI to figure out the best way to use your agents, with dynamic routing based on the task requirements.

- **Use Workflows when:** You need explicit control over execution paths, with predetermined sequences of agent calls and conditional logic.

## Internal Tools

The AgentNetwork uses a special `transmit` tool that allows the routing agent to call specialized agents. This tool handles:

- Single agent calls
- Multiple parallel agent calls
- Context sharing between agents

## Limitations

- The AgentNetwork approach may use more tokens than a well-designed Workflow for the same task
- Debugging can be more challenging as the routing decisions are made by the LLM
- Performance may vary based on the quality of the routing instructions and the capabilities of the specialized agents
