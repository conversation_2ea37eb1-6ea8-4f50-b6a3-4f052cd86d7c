---
title: "Cloudflare Deployer"
description: "Documentation for the CloudflareDeployer class, which deploys Mastra applications to Cloudflare Workers."
---

# CloudflareDeployer

The `CloudflareDeployer` class handles deployment of standalone Mastra applications to Cloudflare Workers. It manages configuration, deployment, and extends the base [Deployer](/reference/deployer/deployer) class with Cloudflare specific functionality.

## Usage example

```typescript filename="src/mastra/index.ts" showLineNumbers copy
import { Mastra } from "@mastra/core/mastra";
import { CloudflareDeployer } from "@mastra/deployer-cloudflare";

export const mastra = new Mastra({
  // ...
  deployer: new CloudflareDeployer({
    scope: "your-account-id",
    projectName: "your-project-name",
    routes: [
      {
        pattern: "example.com/*",
        zone_name: "example.com",
        custom_domain: true,
      },
    ],
    workerNamespace: "your-namespace",
    auth: {
      apiToken: "your-api-token",
      apiEmail: "your-email",
    },
    d1Databases: [
      {
        binding: "binding-name",
        database_name: "database-name",
        database_id: "database-id",
      },
    ],
    kvNamespaces: [
      {
        binding: "binding-name",
        id: "namespace-id",
      },
    ],
  }),
});
```

## Parameters

### Constructor Parameters

<PropertiesTable
  content={[
    {
      name: "scope",
      type: "string",
      description: "Your Cloudflare account ID.",
      isOptional: false,
    },
    {
      name: "projectName",
      type: "string",
      description: "Name of your worker project.",
      isOptional: true,
      defaultValue: "'mastra'",
    },
    {
      name: "routes",
      type: "CFRoute[]",
      description: "Array of route configurations for your worker.",
      isOptional: true,
    },
    {
      name: "workerNamespace",
      type: "string",
      description: "Namespace for your worker.",
      isOptional: true,
    },
    {
      name: "env",
      type: "Record<string, any>",
      description:
        "Environment variables to be included in the worker configuration.",
      isOptional: true,
    },
    {
      name: "auth",
      type: "object",
      description: "Cloudflare authentication details.",
      isOptional: false,
    },
    {
      name: "d1Databases",
      type: "D1DatabaseBinding[]",
      description: "Array of D1 database bindings for your worker.",
      isOptional: true,
    },
    {
      name: "kvNamespaces",
      type: "KVNamespaceBinding[]",
      description: "Array of KV namespace bindings for your worker.",
      isOptional: true,
    },
  ]}
/>

### auth Object

<PropertiesTable
  content={[
    {
      name: "apiToken",
      type: "string",
      description: "Your Cloudflare API token.",
      isOptional: false,
    },
    {
      name: "apiEmail",
      type: "string",
      description: "Your Cloudflare account email.",
      isOptional: true,
    },
  ]}
/>

### CFRoute Object

<PropertiesTable
  content={[
    {
      name: "pattern",
      type: "string",
      description: "URL pattern to match (e.g., 'example.com/*').",
      isOptional: false,
    },
    {
      name: "zone_name",
      type: "string",
      description: "Domain zone name.",
      isOptional: false,
    },
    {
      name: "custom_domain",
      type: "boolean",
      description: "Whether to use a custom domain.",
      isOptional: true,
      defaultValue: "false",
    },
  ]}
/>

### D1DatabaseBinding Object

<PropertiesTable
  content={[
    {
      name: "binding",
      type: "string",
      description: "Name used in Worker code (e.g., `env.testdb`).",
      isOptional: false,
    },
    {
      name: "database_name",
      type: "string",
      description: "Human-readable name (for dashboard).",
      isOptional: false,
    },
    {
      name: "database_id",
      type: "string",
      description: "Cloudflare D1 database ID.",
      isOptional: false,
    },
    {
      name: "preview_database_id",
      type: "string",
      description: "For preview deployments.",
      isOptional: true,
    },
  ]}
/>

### KVNamespaceBinding Object

<PropertiesTable
  content={[
    {
      name: "binding",
      type: "string",
      description: "Name used in Worker code (e.g., `env.test_namespace`).",
      isOptional: false,
    },
    {
      name: "id",
      type: "string",
      description: "Cloudflare KV namespace ID.",
      isOptional: false,
    },
  ]}
/>
