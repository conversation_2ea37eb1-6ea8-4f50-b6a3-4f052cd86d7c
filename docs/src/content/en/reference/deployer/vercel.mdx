---
title: "Vercel Deployer"
description: "Documentation for the VercelDeployer class, which deploys Mastra applications to Vercel."
---

# VercelDeployer

The `VercelDeployer` class handles deployment of standalone Mastra applications to Vercel. It manages configuration, deployment, and extends the base [Deployer](/reference/deployer/deployer) class with Vercel specific functionality.

## Usage example

```typescript filename="src/mastra/index.ts" showLineNumbers copy
import { <PERSON><PERSON> } from "@mastra/core/mastra";
import { VercelDeployer } from "@mastra/deployer-vercel";

export const mastra = new Mastra({
  // ...
  deployer: new VercelDeployer()
});
```
