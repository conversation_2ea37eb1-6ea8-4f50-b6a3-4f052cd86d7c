# getThreadById Reference

The `getThreadById` function retrieves a specific thread by its ID from storage.

## Usage Example

```typescript
import { Memory } from "@mastra/core/memory";

const memory = new Memory(config);

const thread = await memory.getThreadById({ threadId: "thread-123" });
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "threadId",
      type: "string",
      description: "The ID of the thread to be retrieved.",
      isOptional: false,
    },
  ]}
/>

## Returns

<PropertiesTable
  content={[
    {
      name: "StorageThreadType | null",
      type: "Promise",
      description:
        "A promise that resolves to the thread associated with the given ID, or null if not found.",
    },
  ]}
/>

### Related

- [Memory Class Reference](/reference/memory/Memory.mdx)
- [Getting Started with Memory](/docs/memory/overview.mdx) (Covers threads concept)
- [createThread](/reference/memory/createThread.mdx)
- [getThreadsByResourceId](/reference/memory/getThreadsByResourceId.mdx)
