# query

Retrieves messages from a specific thread, with support for pagination, filtering options, and semantic search.

## Usage Example

```typescript
import { Memory } from "@mastra/memory";

const memory = new Memory({
  /* config */
});

// Get last 50 messages
const { messages, uiMessages, messagesV2 } = await memory.query({
  threadId: "thread-123",
  selectBy: {
    last: 50,
  },
});

// Get messages with context around specific messages
const { messages: contextMessages } = await memory.query({
  threadId: "thread-123",
  selectBy: {
    include: [
      {
        id: "msg-123", // Get just this message (no context)
      },
      {
        id: "msg-456", // Get this message with custom context
        withPreviousMessages: 3, // 3 messages before
        withNextMessages: 1, // 1 message after
      },
    ],
  },
});

// Semantic search in messages
const { messages } = await memory.query({
  threadId: "thread-123",
  selectBy: {
    vectorSearchString: "What was discussed about deployment?",
  },
  threadConfig: {
    semanticRecall: true,
  },
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "threadId",
      type: "string",
      description:
        "The unique identifier of the thread to retrieve messages from",
      isOptional: false,
    },
    {
      name: "resourceId",
      type: "string",
      description:
        "Optional ID of the resource that owns the thread. If provided, validates thread ownership",
      isOptional: true,
    },
    {
      name: "selectBy",
      type: "object",
      description: "Options for filtering and selecting messages",
      isOptional: true,
    },
    {
      name: "threadConfig",
      type: "MemoryConfig",
      description: "Configuration options for message retrieval and semantic search",
      isOptional: true,
    },
  ]}
/>

### selectBy

<PropertiesTable
  content={[
    {
      name: "vectorSearchString",
      type: "string",
      description: "Search string for finding semantically similar messages. Requires semantic recall to be enabled in threadConfig.",
      isOptional: true,
    },
    {
      name: "last",
      type: "number | false",
      description:
        "Number of most recent messages to retrieve. Set to false to disable limit. Note: threadConfig.lastMessages (default: 40) will override this if smaller.",
      isOptional: true,
      defaultValue: "40",
    },
    {
      name: "include",
      type: "array",
      description: "Array of specific message IDs to include with optional context messages",
      isOptional: true,
    }
  ]}
/>

### include

<PropertiesTable
  content={[
    {
      name: "id",
      type: "string",
      description: "ID of the message to include",
      isOptional: false,
    },
    {
      name: "threadId",
      type: "string",
      description: "Optional thread ID (defaults to the main threadId parameter)",
      isOptional: true,
    },
    {
      name: "withPreviousMessages",
      type: "number",
      description:
        "Number of messages to include before this message. Defaults to 2 when using vector search, 0 otherwise.",
      isOptional: true,
    },
    {
      name: "withNextMessages",
      type: "number",
      description:
        "Number of messages to include after this message. Defaults to 2 when using vector search, 0 otherwise.",
      isOptional: true,
    },
  ]}
/>

## Returns

<PropertiesTable
  content={[
    {
      name: "messages",
      type: "CoreMessage[]",
      description: "Array of retrieved messages in their core format (v1 format for backwards compatibility)",
    },
    {
      name: "uiMessages",
      type: "UIMessage[]",
      description: "Array of messages formatted for UI display, including proper threading of tool calls and results",
    },
    {
      name: "messagesV2",
      type: "MastraMessageV2[]",
      description: "Array of messages in the v2 format, the current internal message format",
    },
  ]}
/>

## Additional Notes

The `query` function returns three different message formats:

- `messages`: Core message format (v1) used for backwards compatibility with older APIs
- `uiMessages`: Formatted messages suitable for UI display, including proper threading of tool calls and results  
- `messagesV2`: The current internal message format with enhanced structure and metadata

### Semantic Search

When using `vectorSearchString`, ensure that:
- Semantic recall is enabled in your `threadConfig` 
- A vector database is configured in your Memory instance
- An embedding model is provided

The function will automatically include context messages around semantically similar results based on the configured `messageRange`.

### Related

- [Memory Class Reference](/reference/memory/Memory.mdx)
- [Getting Started with Memory](/docs/memory/overview.mdx)
- [Semantic Recall](/docs/memory/semantic-recall.mdx)
- [createThread](/reference/memory/createThread.mdx)
