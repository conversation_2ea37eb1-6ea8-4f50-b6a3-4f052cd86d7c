---
title: "Reference: .after() | Building Workflows (Legacy) | Mastra Docs"
description: Documentation for the `after()` method in workflows (legacy), enabling branching and merging paths.
---

# .after()

The `.after()` method defines explicit dependencies between workflow steps, enabling branching and merging paths in your workflow execution.

## Usage

### Basic Branching

```typescript
workflow
  .step(stepA)
  .then(stepB)
  .after(stepA) // Create new branch after stepA completes
  .step(stepC);
```

### Merging Multiple Branches

```typescript
workflow
  .step(stepA)
  .then(stepB)
  .step(stepC)
  .then(stepD)
  .after([stepB, stepD]) // Create a step that depends on multiple steps
  .step(stepE);
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "steps",
      type: "Step | Step[]",
      description:
        "A single step or array of steps that must complete before continuing",
      isOptional: false,
    },
  ]}
/>

## Returns

<PropertiesTable
  content={[
    {
      name: "workflow",
      type: "LegacyWorkflow",
      description: "The workflow instance for method chaining",
    },
  ]}
/>

## Examples

### Single Dependency

```typescript
workflow
  .step(fetchData)
  .then(processData)
  .after(fetchData) // Branch after fetchData
  .step(logData);
```

### Multiple Dependencies (Merging Branches)

```typescript
workflow
  .step(fetchUserData)
  .then(validateUserData)
  .step(fetchProductData)
  .then(validateProductData)
  .after([validateUserData, validateProductData]) // Wait for both validations to complete
  .step(processOrder);
```

## Related

- [Branching Paths example](../../examples/workflows_legacy/branching-paths.mdx)
- [Workflow Class Reference](./workflow.mdx)
- [Step Reference](./step-class.mdx)
- [Control Flow Guide](../../docs/workflows-legacy/control-flow.mdx)
