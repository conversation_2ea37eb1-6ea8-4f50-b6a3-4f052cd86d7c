---
title: "Reference: Workflow.then() | Building Workflows (Legacy) | Mastra Docs"
description: Documentation for the `.then()` method in workflows, which creates sequential dependencies between steps.
---

# Workflow.then()

The `.then()` method creates a sequential dependency between workflow steps, ensuring steps execute in a specific order.

## Usage

```typescript
workflow.step(stepOne).then(stepTwo).then(stepThree);
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "step",
      type: "Step | string",
      description:
        "The step instance or step ID that should execute after the previous step completes",
      isOptional: false,
    },
  ]}
/>

## Returns

<PropertiesTable
  content={[
    {
      name: "workflow",
      type: "LegacyWorkflow",
      description: "The workflow instance for method chaining",
    },
  ]}
/>

## Validation

When using `then`:

- The previous step must exist in the workflow
- Steps cannot form circular dependencies
- Each step can only appear once in a sequential chain

## Error Handling

```typescript
try {
  workflow
    .step(stepA)
    .then(stepB)
    .then(stepA) // Will throw error - circular dependency
    .commit();
} catch (error) {
  if (error instanceof ValidationError) {
    console.log(error.type); // 'circular_dependency'
    console.log(error.details);
  }
}
```

## Related

- [step Reference](./step-class.mdx)
- [after Reference](./after.mdx)
- [Sequential Steps Example](../../examples/workflows_legacy/sequential-steps.mdx)
- [Control Flow Guide](../../docs/workflows-legacy/control-flow.mdx)
