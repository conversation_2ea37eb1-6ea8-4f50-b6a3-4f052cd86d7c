---
title: "Reference: Workflow.execute() | Workflows (Legacy) | Mastra Docs"
description: "Documentation for the `.execute()` method in Mastra workflows, which runs workflow steps and returns results."
---

# Workflow.execute()

Executes a workflow with the provided trigger data and returns the results. The workflow must be committed before execution.

## Usage Example

```typescript
const workflow = new LegacyWorkflow({
  name: "my-workflow",
  triggerSchema: z.object({
    inputValue: z.number(),
  }),
});

workflow.step(stepOne).then(stepTwo).commit();

const result = await workflow.execute({
  triggerData: { inputValue: 42 },
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "options",
      type: "ExecuteOptions",
      description: "Options for workflow execution",
      isOptional: true,
      properties: [
        {
          name: "triggerData",
          type: "TriggerSchema",
          description: "Input data matching the workflow's trigger schema",
          isOptional: false,
        },
        {
          name: "runId",
          type: "string",
          description: "Optional ID to track this execution run",
          isOptional: true,
        },
      ],
    },
  ]}
/>

## Returns

<PropertiesTable
  content={[
    {
      name: "WorkflowResult",
      type: "object",
      description: "Results from workflow execution",
      properties: [
        {
          name: "runId",
          type: "string",
          description: "Unique identifier for this execution run",
        },
        {
          name: "results",
          type: "Record<string, StepResult>",
          description: "Results from each completed step",
        },
        {
          name: "status",
          type: "WorkflowStatus",
          description: "Final status of the workflow run",
        },
      ],
    },
  ]}
/>

## Additional Examples

Execute with run ID:

```typescript
const result = await workflow.execute({
  runId: "custom-run-id",
  triggerData: { inputValue: 42 },
});
```

Handle execution results:

```typescript
const { runId, results, status } = await workflow.execute({
  triggerData: { inputValue: 42 },
});

if (status === "COMPLETED") {
  console.log("Step results:", results);
}
```

### Related

- [Workflow.createRun()](./createRun.mdx)
- [Workflow.commit()](./commit.mdx)
- [Workflow.start()](./start.mdx)
