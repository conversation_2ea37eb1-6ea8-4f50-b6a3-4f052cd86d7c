---
title: "Reference: run.resume() | Running Workflows (Legacy) | Mastra Docs"
description: Documentation for the `.resume()` method in workflows, which continues execution of a suspended workflow step.
---

# run.resume()

The `.resume()` method continues execution of a suspended workflow step, optionally providing new context data that can be accessed by the step on the inputData property.

## Usage

```typescript copy showLineNumbers
await run.resume({
  runId: "abc-123",
  stepId: "stepTwo",
  context: {
    secondValue: 100,
  },
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "config",
      type: "object",
      description: "Configuration for resuming the workflow",
      isOptional: false,
    },
  ]}
/>

### config

<PropertiesTable
  content={[
    {
      name: "runId",
      type: "string",
      description: "Unique identifier of the workflow run to resume",
      isOptional: false,
    },
    {
      name: "stepId",
      type: "string",
      description: "ID of the suspended step to resume",
      isOptional: false,
    },
    {
      name: "context",
      type: "Record<string, any>",
      description:
        "New context data to inject into the step's inputData property",
      isOptional: true,
    },
  ]}
/>

## Returns

<PropertiesTable
  content={[
    {
      name: "Promise<LegacyWorkflowResult>",
      type: "object",
      description: "Result of the resumed workflow execution",
    },
  ]}
/>

## Async/Await Flow

When a workflow is resumed, execution continues from the point immediately after the `suspend()` call in the step's execution function. This creates a natural flow in your code:

```typescript
// Step definition with suspend point
const reviewStep = new LegacyStep({
  id: "review",
  execute: async ({ context, suspend }) => {
    // First part of execution
    const initialAnalysis = analyzeData(context.inputData.data);

    if (initialAnalysis.needsReview) {
      // Suspend execution here
      await suspend({ analysis: initialAnalysis });

      // This code runs after resume() is called
      // context.inputData now contains any data provided during resume
      return {
        reviewedData: enhanceWithFeedback(
          initialAnalysis,
          context.inputData.feedback,
        ),
      };
    }

    return { reviewedData: initialAnalysis };
  },
});

const { runId, resume, start } = workflow.createRun();

await start({
  inputData: {
    data: "some data",
  },
});

// Later, resume the workflow
const result = await resume({
  runId: "workflow-123",
  stepId: "review",
  context: {
    // This data will be available in `context.inputData`
    feedback: "Looks good, but improve section 3",
  },
});
```

### Execution Flow

1. The workflow runs until it hits `await suspend()` in the `review` step
2. The workflow state is persisted and execution pauses
3. Later, `run.resume()` is called with new context data
4. Execution continues from the point after `suspend()` in the `review` step
5. The new context data (`feedback`) is available to the step on the `inputData` property
6. The step completes and returns its result
7. The workflow continues with subsequent steps

## Error Handling

The resume function may throw several types of errors:

```typescript
try {
  await run.resume({
    runId,
    stepId: "stepTwo",
    context: newData,
  });
} catch (error) {
  if (error.message === "No snapshot found for workflow run") {
    // Handle missing workflow state
  }
  if (error.message === "Failed to parse workflow snapshot") {
    // Handle corrupted workflow state
  }
}
```

## Related

- [Suspend and Resume](../../docs/workflows-legacy/suspend-and-resume.mdx)
- [`suspend` Reference](./suspend.mdx)
- [`watch` Reference](./watch.mdx)
- [Workflow Class Reference](./workflow.mdx)
