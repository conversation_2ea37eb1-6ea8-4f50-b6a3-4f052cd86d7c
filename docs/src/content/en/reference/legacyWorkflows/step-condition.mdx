---
title: "Reference: StepCondition | Building Workflows (Legacy) | Mastra"
description: Documentation for the step condition class in workflows, which determines whether a step should execute based on the output of previous steps or trigger data.
---

# StepCondition

Conditions determine whether a step should execute based on the output of previous steps or trigger data.

## Usage

There are three ways to specify conditions: function, query object, and simple path comparison.

### 1. Function Condition

```typescript copy showLineNumbers
workflow.step(processOrder, {
  when: async ({ context }) => {
    const auth = context?.getStepResult<{ status: string }>("auth");
    return auth?.status === "authenticated";
  },
});
```

### 2. Query Object

```typescript copy showLineNumbers
workflow.step(processOrder, {
  when: {
    ref: { step: "auth", path: "status" },
    query: { $eq: "authenticated" },
  },
});
```

### 3. Simple Path Comparison

```typescript copy showLineNumbers
workflow.step(processOrder, {
  when: {
    "auth.status": "authenticated",
  },
});
```

Based on the type of condition, the workflow runner will try to match the condition to one of these types.

1. Simple Path Condition (when there's a dot in the key)
2. Base/Query Condition (when there's a 'ref' property)
3. Function Condition (when it's an async function)

## StepCondition

<PropertiesTable
  content={[
    {
      name: "ref",
      type: "{ stepId: string | 'trigger'; path: string }",
      description:
        "Reference to step output value. stepId can be a step ID or 'trigger' for initial data. path specifies location of value in step result",
      isOptional: false,
    },
    {
      name: "query",
      type: "Query<any>",
      description: "MongoDB-style query using sift operators ($eq, $gt, etc)",
      isOptional: false,
    },
  ]}
/>

## Query

The Query object provides MongoDB-style query operators for comparing values from previous steps or trigger data. It supports basic comparison operators like `$eq`, `$gt`, `$lt` as well as array operators like `$in` and `$nin`, and can be combined with and/or operators for complex conditions.

This query syntax allows for readable conditional logic for determining whether a step should execute.

<PropertiesTable
  content={[
    {
      name: "$eq",
      type: "any",
      description: "Equal to value",
    },
    {
      name: "$ne",
      type: "any",
      description: "Not equal to value",
    },
    {
      name: "$gt",
      type: "number",
      description: "Greater than value",
    },
    {
      name: "$gte",
      type: "number",
      description: "Greater than or equal to value",
    },
    {
      name: "$lt",
      type: "number",
      description: "Less than value",
    },
    {
      name: "$lte",
      type: "number",
      description: "Less than or equal to value",
    },
    {
      name: "$in",
      type: "any[]",
      description: "Value exists in array",
    },
    {
      name: "$nin",
      type: "any[]",
      description: "Value does not exist in array",
    },
    {
      name: "and",
      type: "StepCondition[]",
      description: "Array of conditions that must all be true",
    },
    {
      name: "or",
      type: "StepCondition[]",
      description: "Array of conditions where at least one must be true",
    },
  ]}
/>

## Related

- [Step Options Reference](./step-options.mdx)
- [Step Function Reference](./step-function.mdx)
- [Control Flow Guide](../../docs/workflows-legacy/control-flow.mdx)
