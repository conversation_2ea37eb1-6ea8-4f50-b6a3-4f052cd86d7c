---
title: "Reference: StepOptions | Building Workflows (Legacy) | Mastra Docs"
description: Documentation for the step options in workflows, which control variable mapping, execution conditions, and other runtime behavior.
---

# StepOptions

Configuration options for workflow steps that control variable mapping, execution conditions, and other runtime behavior.

## Usage

```typescript
workflow.step(processOrder, {
  variables: {
    orderId: { step: "trigger", path: "id" },
    userId: { step: "auth", path: "user.id" },
  },
  when: {
    ref: { step: "auth", path: "status" },
    query: { $eq: "authenticated" },
  },
});
```

## Properties

<PropertiesTable
  content={[
    {
      name: "variables",
      type: "Record<string, VariableRef>",
      description: "Maps step input variables to values from other steps",
      isOptional: true,
    },
    {
      name: "when",
      type: "StepCondition",
      description: "Condition that must be met for step execution",
      isOptional: true,
    },
  ]}
/>

### VariableRef

<PropertiesTable
  content={[
    {
      name: "step",
      type: "string | Step | { id: string }",
      description: "Source step for the variable value",
      isOptional: false,
    },
    {
      name: "path",
      type: "string",
      description: "Path to the value in the step's output",
      isOptional: false,
    },
  ]}
/>

## Related

- [Path Comparison](../../docs/workflows-legacy/control-flow.mdx)
- [Step Function Reference](./step-function.mdx)
- [Step Class Reference](./step-class.mdx)
- [Workflow Class Reference](./workflow.mdx)
- [Control Flow Guide](../../docs/workflows-legacy/control-flow.mdx)
