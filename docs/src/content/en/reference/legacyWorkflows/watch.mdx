---
title: "Reference: run.watch() | Workflows (Legacy) | Mastra Docs"
description: Documentation for the `.watch()` method in workflows, which monitors the status of a workflow run.
---

# run.watch()

The `.watch()` function subscribes to state changes on a mastra run, allowing you to monitor execution progress and react to state updates.

## Usage Example

```typescript
import { LegacyWorkflow } from "@mastra/core/workflows/legacy";

const workflow = new LegacyWorkflow({
  name: "document-processor",
});

const run = workflow.createRun();

// Subscribe to state changes
const unsubscribe = run.watch(({ results, activePaths }) => {
  console.log("Results:", results);
  console.log("Active paths:", activePaths);
});

// Run the workflow
await run.start({
  input: { text: "Process this document" },
});

// Stop watching
unsubscribe();
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "callback",
      type: "(state: LegacyWorkflowState) => void",
      description: "Function called whenever the workflow state changes",
      isOptional: false,
    },
  ]}
/>

### LegacyWorkflowState Properties

<PropertiesTable
  content={[
    {
      name: "results",
      type: "Record<string, any>",
      description: "Outputs from completed workflow steps",
      isOptional: false,
    },
    {
      name: "activePaths",
      type: "Map<string, { status: string; suspendPayload?: any; stepPath: string[] }>",
      description: "Current status of each step",
      isOptional: false,
    },
    {
      name: "runId",
      type: "string",
      description: "ID of the workflow run",
      isOptional: false,
    },
    {
      name: "timestamp",
      type: "number",
      description: "Timestamp of the workflow run",
      isOptional: false,
    },
  ]}
/>

## Returns

<PropertiesTable
  content={[
    {
      name: "unsubscribe",
      type: "() => void",
      description: "Function to stop watching workflow state changes",
    },
  ]}
/>

## Additional Examples

Monitor specific step completion:

```typescript
run.watch(({ results, activePaths }) => {
  if (activePaths.get("processDocument")?.status === "completed") {
    console.log(
      "Document processing output:",
      results["processDocument"].output,
    );
  }
});
```

Error handling:

```typescript
run.watch(({ results, activePaths }) => {
  if (activePaths.get("processDocument")?.status === "failed") {
    console.error(
      "Document processing failed:",
      results["processDocument"].error,
    );
    // Implement error recovery logic
  }
});
```

### Related

- [Workflow Creation](./createRun.mdx)
- [Step Configuration](./step-class.mdx)
