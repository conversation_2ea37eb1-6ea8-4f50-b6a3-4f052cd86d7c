---
title: ".resumeWithEvent() Method | Mastra Docs"
description: "Reference for the resumeWithEvent method that resumes suspended workflows using event data."
---

# resumeWithEvent()

The `resumeWithEvent()` method resumes workflow execution by providing data for a specific event that the workflow is waiting for.

## Syntax

```typescript
const run = workflow.createRun();

// After the workflow has started and suspended at an event step
await run.resumeWithEvent(eventName: string, data: any): Promise<WorkflowRunResult>
```

## Parameters

| Parameter | Type   | Description                                                                                             |
| --------- | ------ | ------------------------------------------------------------------------------------------------------- |
| eventName | string | The name of the event to trigger. Must match an event defined in the workflow's `events` configuration. |
| data      | any    | The event data to provide. Must conform to the schema defined for that event.                           |

## Return Value

Returns a Promise that resolves to a `WorkflowRunResult` object, containing:

- `results`: The result status and output of each step in the workflow
- `activePaths`: A map of active workflow paths and their states
- `value`: The current state value of the workflow
- Other workflow execution metadata

## Description

The `resumeWithEvent()` method is used to resume a workflow that has been suspended at an event step created by the `afterEvent()` method. When called, this method:

1. Validates the provided event data against the schema defined for that event
2. Loads the workflow snapshot from storage
3. Updates the context with the event data in the `resumedEvent` field
4. Resumes execution from the event step
5. Continues workflow execution with the subsequent steps

This method is part of Mastra's event-driven workflow capabilities, allowing you to create workflows that can respond to external events or user interactions.

## Usage Notes

- The workflow must be in a suspended state, specifically at the event step created by `afterEvent(eventName)`
- The event data must conform to the schema defined for that event in the workflow configuration
- The workflow will continue execution from the point it was suspended
- If the workflow is not suspended or is suspended at a different step, this method may throw an error
- The event data is made available to subsequent steps via `context.inputData.resumedEvent`

## Examples

### Basic Usage

```typescript
// Define and start a workflow
const workflow = mastra.legacy_getWorkflow("approval-workflow");
const run = workflow.createRun();

// Start the workflow
await run.start({ triggerData: { requestId: "req-123" } });

// Later, when the approval event occurs:
const result = await run.resumeWithEvent("approval", {
  approved: true,
  approverName: "John Doe",
  comment: "Looks good to me!",
});

console.log(result.results);
```

### With Error Handling

```typescript
try {
  const result = await run.resumeWithEvent("paymentReceived", {
    amount: 100.5,
    transactionId: "tx-456",
    paymentMethod: "credit-card",
  });

  console.log("Workflow resumed successfully:", result.results);
} catch (error) {
  console.error("Failed to resume workflow with event:", error);
  // Handle error - could be invalid event data, workflow not suspended, etc.
}
```

### Monitoring and Auto-Resuming

```typescript
// Start a workflow
const { start, watch, resumeWithEvent } = workflow.createRun();

// Watch for suspended event steps
watch(async ({ activePaths }) => {
  const isApprovalEventSuspended =
    activePaths.get("__approval_event")?.status === "suspended";
  // Check if suspended at the approval event step
  if (isApprovalEventSuspended) {
    console.log("Workflow waiting for approval");

    // In a real scenario, you would wait for the actual event
    // Here we're simulating with a timeout
    setTimeout(async () => {
      try {
        await resumeWithEvent("approval", {
          approved: true,
          approverName: "Auto Approver",
        });
      } catch (error) {
        console.error("Failed to auto-resume workflow:", error);
      }
    }, 5000); // Wait 5 seconds before auto-approving
  }
});

// Start the workflow
await start({ triggerData: { requestId: "auto-123" } });
```

## Related

- [Event-Driven Workflows](./events.mdx)
- [afterEvent()](./afterEvent.mdx)
- [Suspend and Resume](../../docs/workflows-legacy/suspend-and-resume.mdx)
- [resume()](./resume.mdx)
- [watch()](./watch.mdx)
