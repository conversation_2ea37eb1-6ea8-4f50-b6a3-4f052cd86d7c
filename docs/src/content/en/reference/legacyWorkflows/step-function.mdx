---
title: "Reference: Workflow.step() | Workflows (Legacy) | Mastra Docs"
description: Documentation for the `.step()` method in workflows, which adds a new step to the workflow.
---

# Workflow.step()

The `.step()` method adds a new step to the workflow, optionally configuring its variables and execution conditions.

## Usage

```typescript
workflow.step({
  id: "stepTwo",
  outputSchema: z.object({
    result: z.number(),
  }),
  execute: async ({ context }) => {
    return { result: 42 };
  },
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "stepConfig",
      type: "Step | StepDefinition | string",
      description:
        "Step instance, configuration object, or step ID to add to workflow",
      isOptional: false,
    },
    {
      name: "options",
      type: "StepOptions",
      description: "Optional configuration for step execution",
      isOptional: true,
    },
  ]}
/>

### StepDefinition

<PropertiesTable
  content={[
    {
      name: "id",
      type: "string",
      description: "Unique identifier for the step",
      isOptional: false,
    },
    {
      name: "outputSchema",
      type: "z.ZodSchema",
      description: "Schema for validating step output",
      isOptional: true,
    },
    {
      name: "execute",
      type: "(params: ExecuteParams) => Promise<any>",
      description: "Function containing step logic",
      isOptional: false,
    },
  ]}
/>

### StepOptions

<PropertiesTable
  content={[
    {
      name: "variables",
      type: "Record<string, VariableRef>",
      description: "Map of variable names to their source references",
      isOptional: true,
    },
    {
      name: "when",
      type: "StepCondition",
      description: "Condition that must be met for step to execute",
      isOptional: true,
    },
  ]}
/>

## Related

- [Basic Usage with Step Instance](../../docs/workflows-legacy/steps.mdx)
- [Step Class Reference](./step-class.mdx)
- [Workflow Class Reference](./workflow.mdx)
- [Control Flow Guide](../../docs/workflows-legacy/control-flow.mdx)
