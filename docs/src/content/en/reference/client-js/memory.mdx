---
title: Mastra Client Memory API
description: Learn how to manage conversation threads and message history in Mastra using the client-js SDK.
---

# Memory API

The Memory API provides methods to manage conversation threads and message history in Mastra.

## Initialize Mastra Client

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## Memory Thread Operations

### Get All Threads

Retrieve all memory threads for a specific resource:

```typescript
const threads = await client.getMemoryThreads({
  resourceId: "resource-1",
  agentId: "agent-1",
});
```

### Create a New Thread

Create a new memory thread:

```typescript
const thread = await client.createMemoryThread({
  title: "New Conversation",
  metadata: { category: "support" },
  resourceid: "resource-1",
  agentId: "agent-1",
});
```

### Working with a Specific Thread

Get an instance of a specific memory thread:

```typescript
const thread = client.getMemoryThread("thread-id", "agent-id");
```

## Thread Methods

### Get Thread Details

Retrieve details about a specific thread:

```typescript
const details = await thread.get();
```

### Update Thread

Update thread properties:

```typescript
const updated = await thread.update({
  title: "Updated Title",
  metadata: { status: "resolved" },
  resourceid: "resource-1",
});
```

### Delete Thread

Delete a thread and its messages:

```typescript
await thread.delete();
```



## Message Operations

### Save Messages

Save messages to memory:

```typescript
const savedMessages = await client.saveMessageToMemory({
  messages: [
    {
      role: "user",
      content: "Hello!",
      id: "1",
      threadId: "thread-1",
      createdAt: new Date(),
      type: "text",
    },
  ],
  agentId: "agent-1",
});
```

### Retrieve Thread Messages

Get messages associated with a memory thread:

```typescript
// Get all messages in the thread
const { messages } = await thread.getMessages();

// Limit the number of messages retrieved
const { messages } = await thread.getMessages({ limit: 10 });
```

### Get Memory Status

Check the status of the memory system:

```typescript
const status = await client.getMemoryStatus("agent-id");
```
