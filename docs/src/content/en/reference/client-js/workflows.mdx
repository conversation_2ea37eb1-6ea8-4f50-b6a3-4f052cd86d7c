---
title: Mastra Client Workflows API
description: Learn how to interact with and execute automated workflows in Mastra using the client-js SDK.
---

# Workflows API

The Workflows API provides methods to interact with and execute automated workflows in Mastra.

## Initialize Mastra Client

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## Getting All Workflows

Retrieve a list of all available workflows:

```typescript
const workflows = await client.getWorkflows();
```

## Working with a Specific Workflow

Get an instance of a specific workflow as defined by the const name:

```typescript filename="src/mastra/workflows/test-workflow.ts"
export const testWorkflow = createWorkflow({
  id: 'city-workflow'
})
```

```typescript
const workflow = client.getWorkflow("testWorkflow");
```

## Workflow Methods

### Get Workflow Details

Retrieve detailed information about a workflow:

```typescript
const details = await workflow.details();
```

### Start workflow run asynchronously

Start a workflow run with inputData and await full run results:

```typescript
const run = await workflow.createRun();

const result = await workflow.startAsync({
  runId: run.runId,
  inputData: {
    city: "New York",
  },
});
```

### Resume Workflow run asynchronously

Resume a suspended workflow step and await full run result:

```typescript
const run = await workflow.createRun();

const result = await workflow.resumeAsync({
  runId: run.runId,
  step: "step-id",
  resumeData: { key: "value" },
});
```

### Watch Workflow

Watch workflow transitions:

```typescript
try {
  const workflow = client.getWorkflow("testWorkflow");

  const run = await workflow.createRun();

  workflow.watch({ runId: run.runId }, (record) => {
    console.log(record);
  });

  const result = await workflow.start({
    runId: run.runId,
    inputData: {
      city: "New York",
    },
  });
} catch (e) {
  console.error(e);
}
```

### Resume Workflow

Resume workflow run and watch workflow step transitions:

```typescript
try {
  const workflow = client.getWorkflow("testWorkflow");

  const run = await workflow.createRun({ runId: prevRunId });

  workflow.watch({ runId: run.runId }, (record) => {
    console.log(record);
  });

  workflow.resume({
    runId: run.runId,
    step: "step-id",
    resumeData: { key: "value" },
  });
} catch (e) {
  console.error(e);
}
```

### Get Workflow Run result

Get the result of a workflow run:

```typescript
try  {
  const workflow = client.getWorkflow("testWorkflow");

  const run = await workflow.createRun();

  // start the workflow run
  const startResult = await workflow.start({
    runId: run.runId,
    inputData: {
      city: "New York",
    },
  });

  const result = await workflow.runExecutionResult(run.runId);

  console.log(result);
} catch (e) {
  console.error(e);
}
```

This is useful when dealing with long running workflows. You can use this to poll the result of the workflow run.

### Workflow run result

A workflow run result yields the following:

| Field            | Type                                                                                                                                                                                                                                               | Description                                      |
| ---------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------ |
| `payload`        | `{currentStep?: {id: string, status: string, output?: Record<string, any>, payload?: Record<string, any>}, workflowState: {status: string, steps: Record<string, {status: string, output?: Record<string, any>, payload?: Record<string, any>}>}}` | The current step and workflow state of the run   |
| `eventTimestamp` | `Date`                                                                                                                                                                                                                                             | The timestamp of the event                       |
| `runId`          | `string`                                                                                                                                                                                                                                           | Unique identifier for this workflow run instance |
