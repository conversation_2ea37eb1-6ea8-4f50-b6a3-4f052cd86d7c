---
title: Mastra Client Error Handling
description: Learn about the built-in retry mechanism and error handling capabilities in the Mastra client-js SDK.
---

# Error Handling

The Mastra Client SDK includes built-in retry mechanism and error handling capabilities.

## Error Handling

All API methods can throw errors that you can catch and handle:

```typescript
try {
  const agent = client.getAgent("agent-id");
  const response = await agent.generate({
    messages: [{ role: "user", content: "Hello" }],
  });
} catch (error) {
  console.error("An error occurred:", error.message);
}
```

## Retry Mechanism

The client automatically retries failed requests with exponential backoff:

```typescript
const client = new MastraClient({
  baseUrl: "http://localhost:4111",
  retries: 3, // Number of retry attempts
  backoffMs: 300, // Initial backoff time
  maxBackoffMs: 5000, // Maximum backoff time
});
```

### How Retries Work

1. First attempt fails → Wait 300ms
2. Second attempt fails → Wait 600ms
3. Third attempt fails → Wait 1200ms
4. Final attempt fails → Throw error
