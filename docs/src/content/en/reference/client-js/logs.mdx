---
title: Mastra Client Logs API
description: Learn how to access and query system logs and debugging information in Mastra using the client-js SDK.
---

# Logs API

The Logs API provides methods to access and query system logs and debugging information in Mastra.

## Initialize Mastra Client

```typescript
import { Mastra<PERSON>lient } from "@mastra/client-js";

const client = new MastraClient();
```

## Getting Logs

Retrieve system logs with optional filtering:

```typescript
const logs = await client.getLogs({
  transportId: "transport-1",
});
```

## Getting Logs for a Specific Run

Retrieve logs for a specific execution run:

```typescript
const runLogs = await client.getLogForRun({
  runId: "run-1",
  transportId: "transport-1",
});
```
