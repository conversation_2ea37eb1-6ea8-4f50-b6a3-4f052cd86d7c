---
title: "Cloudflare Storage | Storage System | Mastra Core"
description: Documentation for the Cloudflare KV storage implementation in Mastra.
---

# Cloudflare Storage

The Cloudflare KV storage implementation provides a globally distributed, serverless key-value store solution using Cloudflare Workers KV.

## Installation

```bash copy
npm install @mastra/cloudflare@latest
```

## Usage

```typescript copy showLineNumbers
import { CloudflareStore } from "@mastra/cloudflare";

// --- Example 1: Using Workers Binding ---
const storageWorkers = new CloudflareStore({
  bindings: {
    threads: THREADS_KV, // KVNamespace binding for threads table
    messages: MESSAGES_KV, // KVNamespace binding for messages table
    // Add other tables as needed
  },
  keyPrefix: "dev_", // Optional: isolate keys per environment
});

// --- Example 2: Using REST API ---
const storageRest = new CloudflareStore({
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID!, // Cloudflare Account ID
  apiToken: process.env.CLOUDFLARE_API_TOKEN!, // Cloudflare API Token
  namespacePrefix: "dev_", // Optional: isolate namespaces per environment
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "bindings",
      type: "Record<string, KVNamespace>",
      description: "Cloudflare Workers KV bindings (for Workers runtime)",
      isOptional: true,
    },
    {
      name: "accountId",
      type: "string",
      description: "Cloudflare Account ID (for REST API)",
      isOptional: true,
    },
    {
      name: "apiToken",
      type: "string",
      description: "Cloudflare API Token (for REST API)",
      isOptional: true,
    },
    {
      name: "namespacePrefix",
      type: "string",
      description:
        "Optional prefix for all namespace names (useful for environment isolation)",
      isOptional: true,
    },
    {
      name: "keyPrefix",
      type: "string",
      description:
        "Optional prefix for all keys (useful for environment isolation)",
      isOptional: true,
    },
  ]}
/>

#### Additional Notes

### Schema Management

The storage implementation handles schema creation and updates automatically. It creates the following tables:

- `threads`: Stores conversation threads
- `messages`: Stores individual messages
- `metadata`: Stores additional metadata for threads and messages

### Consistency & Propagation

Cloudflare KV is an eventually consistent store, meaning that data may not be immediately available across all regions after a write.

### Key Structure & Namespacing

Keys in Cloudflare KV are structured as a combination of a configurable prefix and a table-specific format (e.g., `threads:threadId`).
For Workers deployments, `keyPrefix` is used to isolate data within a namespace; for REST API deployments, `namespacePrefix` is used to isolate entire namespaces between environments or applications.
