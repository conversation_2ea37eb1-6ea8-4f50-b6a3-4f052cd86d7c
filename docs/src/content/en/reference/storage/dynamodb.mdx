---
title: "DynamoDB Storage | Storage System | Mastra Core"
description: "Documentation for the DynamoDB storage implementation in Mastra, using a single-table design with ElectroDB."
---

# DynamoDB Storage

The DynamoDB storage implementation provides a scalable and performant NoSQL database solution for Mastra, leveraging a single-table design pattern with [ElectroDB](https://electrodb.dev/).

## Features

- Efficient single-table design for all Mastra storage needs
- Based on ElectroDB for type-safe DynamoDB access
- Support for AWS credentials, regions, and endpoints
- Compatible with AWS DynamoDB Local for development
- Stores Thread, Message, Trace, Eval, and Workflow data
- Optimized for serverless environments

## Installation

```bash copy
npm install @mastra/dynamodb@latest
# or
pnpm add @mastra/dynamodb@latest
# or
yarn add @mastra/dynamodb@latest
```

## Prerequisites

Before using this package, you **must** create a DynamoDB table with a specific structure, including primary keys and Global Secondary Indexes (GSIs). This adapter expects the DynamoDB table and its GSIs to be provisioned externally.

Detailed instructions for setting up the table using AWS CloudFormation or AWS CDK are available in [TABLE_SETUP.md](https://github.com/mastra-ai/mastra/blob/main/stores/dynamodb/TABLE_SETUP.md). Please ensure your table is configured according to those instructions before proceeding.

## Usage

### Basic Usage

```typescript copy showLineNumbers
import { Memory } from "@mastra/memory";
import { DynamoDBStore } from "@mastra/dynamodb";

// Initialize the DynamoDB storage
const storage = new DynamoDBStore({
  name: "dynamodb", // A name for this storage instance
  config: {
    tableName: "mastra-single-table", // Name of your DynamoDB table
    region: "us-east-1", // Optional: AWS region, defaults to 'us-east-1'
    // endpoint: "http://localhost:8000", // Optional: For local DynamoDB
    // credentials: { accessKeyId: "YOUR_ACCESS_KEY", secretAccessKey: "YOUR_SECRET_KEY" } // Optional
  },
});

// Example: Initialize Memory with DynamoDB storage
const memory = new Memory({
  storage,
  options: {
    lastMessages: 10,
  },
});
```

### Local Development with DynamoDB Local

For local development, you can use [DynamoDB Local](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DynamoDBLocal.html).

1.  **Run DynamoDB Local (e.g., using Docker):**

    ```bash
    docker run -p 8000:8000 amazon/dynamodb-local
    ```

2.  **Configure `DynamoDBStore` to use the local endpoint:**

    ```typescript copy showLineNumbers
    import { DynamoDBStore } from "@mastra/dynamodb";

    const storage = new DynamoDBStore({
      name: "dynamodb-local",
      config: {
        tableName: "mastra-single-table", // Ensure this table is created in your local DynamoDB
        region: "localhost", // Can be any string for local, 'localhost' is common
        endpoint: "http://localhost:8000",
        // For DynamoDB Local, credentials are not typically required unless configured.
        // If you've configured local credentials:
        // credentials: { accessKeyId: "fakeMyKeyId", secretAccessKey: "fakeSecretAccessKey" }
      },
    });
    ```

    You will still need to create the table and GSIs in your local DynamoDB instance, for example, using the AWS CLI pointed to your local endpoint.

## Parameters

<PropertiesTable
  content={[
    {
      name: "name",
      type: "string",
      description: "A name for the storage instance.",
      isOptional: false,
    },
    {
      name: "config.tableName",
      type: "string",
      description: "The name of your DynamoDB table.",
      isOptional: false,
    },
    {
      name: "config.region",
      type: "string",
      description:
        "AWS region. Defaults to 'us-east-1'. For local development, can be set to 'localhost' or similar.",
      isOptional: true,
    },
    {
      name: "config.endpoint",
      type: "string",
      description:
        "Custom endpoint for DynamoDB (e.g., 'http://localhost:8000' for local development).",
      isOptional: true,
    },
    {
      name: "config.credentials",
      type: "object",
      description:
        "AWS credentials object with `accessKeyId` and `secretAccessKey`. If not provided, the AWS SDK will attempt to source credentials from environment variables, IAM roles (e.g., for EC2/Lambda), or the shared AWS credentials file.",
      isOptional: true,
    },
  ]}
/>

## AWS IAM Permissions

The IAM role or user executing the code needs appropriate permissions to interact with the specified DynamoDB table and its indexes. Below is a sample policy. Replace `${YOUR_TABLE_NAME}` with your actual table name and `${YOUR_AWS_REGION}` and `${YOUR_AWS_ACCOUNT_ID}` with appropriate values.

```json copy
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "dynamodb:DescribeTable",
        "dynamodb:GetItem",
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:DeleteItem",
        "dynamodb:Query",
        "dynamodb:Scan",
        "dynamodb:BatchGetItem",
        "dynamodb:BatchWriteItem"
      ],
      "Resource": [
        "arn:aws:dynamodb:${YOUR_AWS_REGION}:${YOUR_AWS_ACCOUNT_ID}:table/${YOUR_TABLE_NAME}",
        "arn:aws:dynamodb:${YOUR_AWS_REGION}:${YOUR_AWS_ACCOUNT_ID}:table/${YOUR_TABLE_NAME}/index/*"
      ]
    }
  ]
}
```

## Key Considerations

Before diving into the architectural details, keep these key points in mind when working with the DynamoDB storage adapter:

- **External Table Provisioning:** This adapter _requires_ you to create and configure the DynamoDB table and its Global Secondary Indexes (GSIs) yourself, prior to using the adapter. Follow the guide in [TABLE_SETUP.md](https://github.com/mastra-ai/mastra/blob/main/stores/dynamodb/TABLE_SETUP.md).
- **Single-Table Design:** All Mastra data (threads, messages, etc.) is stored in one DynamoDB table. This is a deliberate design choice optimized for DynamoDB, differing from relational database approaches.
- **Understanding GSIs:** Familiarity with how the GSIs are structured (as per `TABLE_SETUP.md`) is important for understanding data retrieval and potential query patterns.
- **ElectroDB:** The adapter uses ElectroDB to manage interactions with DynamoDB, providing a layer of abstraction and type safety over raw DynamoDB operations.

## Architectural Approach

This storage adapter utilizes a **single-table design pattern** leveraging [ElectroDB](https://electrodb.dev/), a common and recommended approach for DynamoDB. This differs architecturally from relational database adapters (like `@mastra/pg` or `@mastra/libsql`) that typically use multiple tables, each dedicated to a specific entity (threads, messages, etc.).

Key aspects of this approach:

- **DynamoDB Native:** The single-table design is optimized for DynamoDB's key-value and query capabilities, often leading to better performance and scalability compared to mimicking relational models.
- **External Table Management:** Unlike some adapters that might offer helper functions to create tables via code, this adapter **expects the DynamoDB table and its associated Global Secondary Indexes (GSIs) to be provisioned externally** before use. Please refer to [TABLE_SETUP.md](https://github.com/mastra-ai/mastra/blob/main/stores/dynamodb/TABLE_SETUP.md) for detailed instructions using tools like AWS CloudFormation or CDK. The adapter focuses solely on interacting with the pre-existing table structure.
- **Consistency via Interface:** While the underlying storage model differs, this adapter adheres to the same `MastraStorage` interface as other adapters, ensuring it can be used interchangeably within the Mastra `Memory` component.

### Mastra Data in the Single Table

Within the single DynamoDB table, different Mastra data entities (such as Threads, Messages, Traces, Evals, and Workflows) are managed and distinguished using ElectroDB. ElectroDB defines specific models for each entity type, which include unique key structures and attributes. This allows the adapter to store and retrieve diverse data types efficiently within the same table.

For example, a `Thread` item might have a primary key like `THREAD#<threadId>`, while a `Message` item belonging to that thread might use `THREAD#<threadId>` as a partition key and `MESSAGE#<messageId>` as a sort key. The Global Secondary Indexes (GSIs), detailed in `TABLE_SETUP.md`, are strategically designed to support common access patterns across these different entities, such as fetching all messages for a thread or querying traces associated with a particular workflow.

### Advantages of Single-Table Design

This implementation uses a single-table design pattern with ElectroDB, which offers several advantages within the context of DynamoDB:

1.  **Lower cost (potentially):** Fewer tables can simplify Read/Write Capacity Unit (RCU/WCU) provisioning and management, especially with on-demand capacity.
2.  **Better performance:** Related data can be co-located or accessed efficiently through GSIs, enabling fast lookups for common access patterns.
3.  **Simplified administration:** Fewer distinct tables to monitor, back up, and manage.
4.  **Reduced complexity in access patterns:** ElectroDB helps manage the complexity of item types and access patterns on a single table.
5.  **Transaction support:** DynamoDB transactions can be used across different "entity" types stored within the same table if needed.

## License

This package is distributed under the MIT License. See [LICENSE.md](https://github.com/mastra-ai/mastra/blob/main/LICENSE.md) for more information.
