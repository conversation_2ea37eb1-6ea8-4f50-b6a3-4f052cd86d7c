---
title: "PostgreSQL Storage | Storage System | Mastra Core"
description: Documentation for the PostgreSQL storage implementation in Mastra.
---

# PostgreSQL Storage

The PostgreSQL storage implementation provides a production-ready storage solution using PostgreSQL databases.

## Installation

```bash copy
npm install @mastra/pg@latest
```

## Usage

```typescript copy showLineNumbers
import { PostgresStore } from "@mastra/pg";

const storage = new PostgresStore({
  connectionString: process.env.DATABASE_URL,
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "connectionString",
      type: "string",
      description:
        "PostgreSQL connection string (e.g., ********************************/dbname)",
      isOptional: false,
    },
    {
      name: "schemaName",
      type: "string",
      description:
        "The name of the schema you want the storage to use. Will use the default schema if not provided.",
      isOptional: true,
    },
  ]}
/>

## Constructor Examples

You can instantiate `PostgresStore` in the following ways:

```ts
import { PostgresStore } from "@mastra/pg";

// Using a connection string only
const store1 = new PostgresStore({
  connectionString: "postgresql://user:password@localhost:5432/mydb",
});

// Using a connection string with a custom schema name
const store2 = new PostgresStore({
  connectionString: "postgresql://user:password@localhost:5432/mydb",
  schemaName: "custom_schema", // optional
});

// Using individual connection parameters
const store4 = new PostgresStore({
  host: "localhost",
  port: 5432,
  database: "mydb",
  user: "user",
  password: "password",
});

// Individual parameters with schemaName
const store5 = new PostgresStore({
  host: "localhost",
  port: 5432,
  database: "mydb",
  user: "user",
  password: "password",
  schemaName: "custom_schema", // optional
});
```

## Additional Notes

### Schema Management

The storage implementation handles schema creation and updates automatically. It creates the following tables:

- `threads`: Stores conversation threads
- `messages`: Stores individual messages
- `metadata`: Stores additional metadata for threads and messages

### Direct Database and Pool Access

`PostgresStore` exposes both the underlying database object and the pg-promise instance as public fields:

```typescript
store.db  // pg-promise database instance
store.pgp // pg-promise main instance
```

This enables direct queries and custom transaction management. When using these fields:
- You are responsible for proper connection and transaction handling.
- Closing the store (`store.close()`) will destroy the associated connection pool.
- Direct access bypasses any additional logic or validation provided by PostgresStore methods.

This approach is intended for advanced scenarios where low-level access is required.

