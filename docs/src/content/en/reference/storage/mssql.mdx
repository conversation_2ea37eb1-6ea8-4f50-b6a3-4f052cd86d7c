---
title: "MSSQL Storage | Storage System | Mastra Core"
description: Documentation for the MSSQL storage implementation in Mastra.
---

# MSSQL Storage

The MSSQL storage implementation provides a production-ready storage solution using Microsoft SQL Server databases.

## Installation

```bash copy
npm install @mastra/mssql@latest
```

## Usage

```typescript copy showLineNumbers
import { MSSQLStore } from "@mastra/mssql";

const storage = new MSSQLStore({
  connectionString: process.env.DATABASE_URL,
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "connectionString",
      type: "string",
      description:
        "MSSQL connection string (e.g., mssql://user:pass@host:1433/dbname)",
      isOptional: false,
    },
    {
      name: "schemaName",
      type: "string",
      description:
        "The name of the schema you want the storage to use. Will use the default schema if not provided.",
      isOptional: true,
    },
  ]}
/>

## Constructor Examples

You can instantiate `MSSQLStore` in the following ways:

```ts
import { MSSQLStore } from "@mastra/mssql";

// Using a connection string only
const store1 = new MSSQLStore({
  connectionString: "mssql://user:password@localhost:1433/mydb",
});

// Using a connection string with a custom schema name
const store2 = new MSSQLStore({
  connectionString: "mssql://user:password@localhost:1433/mydb",
  schemaName: "custom_schema", // optional
});

// Using individual connection parameters
const store4 = new MSSQLStore({
  server: "localhost",
  port: 1433,
  database: "mydb",
  user: "user",
  password: "password",
});

// Individual parameters with schemaName
const store5 = new MSSQLStore({
  server: "localhost",
  port: 1433,
  database: "mydb",
  user: "user",
  password: "password",
  schemaName: "custom_schema", // optional
});
```

## Additional Notes

### Schema Management

The storage implementation handles schema creation and updates automatically. It creates the following tables:

- `threads`: Stores conversation threads
- `messages`: Stores individual messages
- `metadata`: Stores additional metadata for threads and messages

### Direct Database and Pool Access

`MSSQLStore` exposes the mssql connection pool as public fields:

```typescript
store.pool // mssql connection pool instance
```

This enables direct queries and custom transaction management. When using these fields:
- You are responsible for proper connection and transaction handling.
- Closing the store (`store.close()`) will destroy the associated connection pool.
- Direct access bypasses any additional logic or validation provided by MSSQLStore methods.

This approach is intended for advanced scenarios where low-level access is required. 