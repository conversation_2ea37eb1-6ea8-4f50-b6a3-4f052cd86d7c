---
title: "LibSQL Storage | Storage System | Mastra Core"
description: Documentation for the LibSQL storage implementation in Mastra.
---

# LibSQL Storage

The LibSQL storage implementation provides a SQLite-compatible storage solution that can run both in-memory and as a persistent database.

## Installation

```bash copy
npm install @mastra/libsql@latest
```

## Usage

```typescript copy showLineNumbers
import { LibSQLStore } from "@mastra/libsql";

// File database (development)
const storage = new LibSQLStore({
  url: "file:./storage.db",
});

// Persistent database (production)
const storage = new LibSQLStore({
  url: process.env.DATABASE_URL,
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "url",
      type: "string",
      description:
        "Database URL. Use ':memory:' for in-memory database, 'file:filename.db' for a file database, or any LibSQL-compatible connection string for persistent storage.",
      isOptional: false,
    },
    {
      name: "authT<PERSON>",
      type: "string",
      description: "Authentication token for remote LibSQL databases.",
      isOptional: true,
    },
  ]}
/>

## Additional Notes

### In-Memory vs Persistent Storage

The file configuration (`file:storage.db`) is useful for:

- Development and testing
- Temporary storage
- Quick prototyping

For production use cases, use a persistent database URL: `libsql://your-database.turso.io`

### Schema Management

The storage implementation handles schema creation and updates automatically. It creates the following tables:

- `threads`: Stores conversation threads
- `messages`: Stores individual messages
- `resources`: Stores user-specific data for resource-scoped working memory
- `metadata`: Stores additional metadata for threads and messages
