---
title: 'mastra start'
description: 'Start your built Mastra application'
---

# mastra start

Start your built Mastra application. This command is used to run your built Mastra application in production mode.
Telemetry is enabled by default.

## Usage
After building your project with `mastra build` run:

```bash
mastra start [options]
```

## Options

| Option | Description |
|--------|-------------|
| `-d, --dir <path>` | Path to your built Mastra output directory (default: .mastra/output) |
| `-nt, --no-telemetry` | Enable OpenTelemetry instrumentation for observability |

## Examples

Start the application with default settings:

```bash
mastra start
```

Start from a custom output directory:

```bash
mastra start --dir ./my-output
```

Start with telemetry disabled:

```bash
mastra start -nt
```