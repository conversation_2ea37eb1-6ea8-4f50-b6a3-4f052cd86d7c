---
title: "mastra lint | Validate Project | Mastra CLI"
description: "Lint your Mastra project"
---

# mastra lint

The `mastra lint` command validates the structure and code of your Mastra project to ensure it follows best practices and is error-free.

## Usage

```bash
mastra lint [options]
```

## Options

<PropertiesTable
  content={[
    {
      name: "--dir",
      type: "string",
      description: "Path to your Mastra folder",
      isOptional: true,
    },
    {
      name: "--root",
      type: "string",
      description: "Path to your root folder",
      isOptional: true,
    },
    {
      name: "--tools",
      type: "string",
      description: "Comma-separated list of paths to tool files to include",
      isOptional: true,
    },
    {
      name: "--help",
      type: "boolean",
      description: "display help for command",
      isOptional: true,
    },
  ]}
/>

## Advanced usage

### Disable telemetry

To disable CLI analytics while running linting (and other commands) set
`MASTRA_TELEMETRY_DISABLED=1`:

```bash copy
MASTRA_TELEMETRY_DISABLED=1 mastra lint
```
