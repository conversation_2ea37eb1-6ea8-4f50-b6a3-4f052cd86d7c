---
title: "create-mastra | Create Project | Mastra CLI"
description: Documentation for the create-mastra command, which creates a new Mastra project with interactive setup options.
---

# create-mastra

The `create-mastra` command **creates** a new standalone Mastra project. Use this command to scaffold a complete Mastra setup in a dedicated directory.

## Usage

```bash
create-mastra [options]
```

## Options

<PropertiesTable
  content={[
    {
      name: "--version",
      type: "boolean",
      description: "Output the version number",
      isOptional: true,
    },
    {
      name: "--project-name",
      type: "string",
      description:
        "Project name that will be used in package.json and as the project directory name",
      isOptional: true,
    },
    {
      name: "--default",
      type: "boolean",
      description: "Quick start with defaults(src, OpenAI, no examples)",
      isOptional: true,
    },
    {
      name: "--components",
      type: "string",
      description:
        "Comma-separated list of components (agents, tools, workflows)",
      isOptional: true,
    },
    {
      name: "--llm",
      type: "string",
      description:
        "Default model provider (openai, anthropic, groq, google, or cerebras)",
      isOptional: true,
    },
    {
      name: "--llm-api-key",
      type: "string",
      description: "API key for the model provider",
      isOptional: true,
    },
    {
      name: "--example",
      type: "boolean",
      description: "Include example code",
      isOptional: true,
    },
    {
      name: "--no-example",
      type: "boolean",
      description: "Do not include example code",
      isOptional: true,
    },
    {
      name: "--template",
      type: "string",
      description:
        "Create project from a template (use template name or leave blank to select from list)",
      isOptional: true,
    },
    {
      name: "--timeout",
      type: "number",
      description:
        "Configurable timeout for package installation, defaults to 60000 ms",
      isOptional: true,
    },
    {
      name: "--dir",
      type: "string",
      description: "Target directory for Mastra source code (default: src/)",
      isOptional: true,
    },
    {
      name: "--mcp",
      type: "string",
      description:
        "MCP Server for code editor (cursor, cursor-global, windsurf, vscode)",
      isOptional: true,
    },
    {
      name: "--help",
      type: "boolean",
      description: "Display help for command",
      isOptional: true,
    },
  ]}
/>
