---
title: "mastra dev | Development Server | Mastra CLI"
description: Documentation for the mastra dev command, which starts a development server for agents, tools, and workflows.
---

# mastra dev

The `mastra dev` command starts a development server that exposes REST endpoints for your agents, tools, and workflows.

## Usage

```bash
mastra dev [options]
```

## Options

<PropertiesTable
  content={[
    {
      name: "--dir",
      type: "string",
      description: "Path to your mastra folder",
      isOptional: true,
    },
    {
      name: "--root",
      type: "string",
      description: "Path to your root folder",
      isOptional: true,
    },
    {
      name: "--tools",
      type: "string",
      description: "Comma-separated list of paths to tool files to include",
      isOptional: true,
    },
    {
      name: "--port",
      type: "number",
      description:
        "deprecated: Port number for the development server (defaults to 4111)",
      isOptional: true,
    },
    {
      name: "--env",
      type: "string",
      description: "Path to custom environment file",
      isOptional: true,
    },
    {
      name: "--inspect",
      type: "boolean",
      description: "Start the dev server in inspect mode for debugging (cannot be used with --inspect-brk)",
      isOptional: true,
    },
    {
      name: "--inspect-brk",
      type: "boolean",
      description: "Start the dev server in inspect mode and break at the beginning of the script (cannot be used with --inspect)",
      isOptional: true,
    },
    {
      name: "--help",
      type: "boolean",
      description: "display help for command",
      isOptional: true,
    },
  ]}
/>

## Routes

Starting the server with `mastra dev` exposes a set of REST routes by default:

### System Routes

- **GET `/api`**: Get API status.

### Agent Routes

Agents are expected to be exported from `src/mastra/agents`.

- **GET `/api/agents`**: Lists the registered agents found in your Mastra folder.
- **GET `/api/agents/:agentId`**: Get agent by ID.
- **GET `/api/agents/:agentId/evals/ci`**: Get CI evals by agent ID.
- **GET `/api/agents/:agentId/evals/live`**: Get live evals by agent ID.
- **POST `/api/agents/:agentId/generate`**: Sends a text-based prompt to the specified agent, returning the agent's response.
- **POST `/api/agents/:agentId/stream`**: Stream a response from an agent.
- **POST `/api/agents/:agentId/instructions`**: Update an agent's instructions.
- **POST `/api/agents/:agentId/instructions/enhance`**: Generate an improved system prompt from instructions.
- **GET `/api/agents/:agentId/speakers`**: Get available speakers for an agent.
- **POST `/api/agents/:agentId/speak`**: Convert text to speech using the agent's voice provider.
- **POST `/api/agents/:agentId/listen`**: Convert speech to text using the agent's voice provider.
- **POST `/api/agents/:agentId/tools/:toolId/execute`**: Execute a tool through an agent.

### Tool Routes

Tools are expected to be exported from `src/mastra/tools` (or the configured tools directory).

- **GET `/api/tools`**: Get all tools.
- **GET `/api/tools/:toolId`**: Get tool by ID.
- **POST `/api/tools/:toolId/execute`**: Invokes a specific tool by name, passing input data in the request body.

### Workflow Routes

Workflows are expected to be exported from `src/mastra/workflows` (or the configured workflows directory).

- **GET `/api/workflows`**: Get all workflows.
- **GET `/api/workflows/:workflowId`**: Get workflow by ID.
- **POST `/api/workflows/:workflowName/start`**: Starts the specified workflow.
- **POST `/api/workflows/:workflowName/:instanceId/event`**: Sends an event or trigger signal to an existing workflow instance.
- **GET `/api/workflows/:workflowName/:instanceId/status`**: Returns status info for a running workflow instance.
- **POST `/api/workflows/:workflowId/resume`**: Resume a suspended workflow step.
- **POST `/api/workflows/:workflowId/resume-async`**: Resume a suspended workflow step asynchronously.
- **POST `/api/workflows/:workflowId/createRun`**: Create a new workflow run.
- **POST `/api/workflows/:workflowId/start-async`**: Execute/Start a workflow asynchronously.
- **GET `/api/workflows/:workflowId/watch`**: Watch workflow transitions in real-time.

### Memory Routes

- **GET `/api/memory/status`**: Get memory status.
- **GET `/api/memory/threads`**: Get all threads.
- **GET `/api/memory/threads/:threadId`**: Get thread by ID.
- **GET `/api/memory/threads/:threadId/messages`**: Get messages for a thread.
- **POST `/api/memory/threads`**: Create a new thread.
- **PATCH `/api/memory/threads/:threadId`**: Update a thread.
- **DELETE `/api/memory/threads/:threadId`**: Delete a thread.
- **POST `/api/memory/save-messages`**: Save messages.

### Telemetry Routes

- **GET `/api/telemetry`**: Get all traces.

### Log Routes

- **GET `/api/logs`**: Get all logs.
- **GET `/api/logs/transports`**: List of all log transports.
- **GET `/api/logs/:runId`**: Get logs by run ID.

### Vector Routes

- **POST `/api/vector/:vectorName/upsert`**: Upsert vectors into an index.
- **POST `/api/vector/:vectorName/create-index`**: Create a new vector index.
- **POST `/api/vector/:vectorName/query`**: Query vectors from an index.
- **GET `/api/vector/:vectorName/indexes`**: List all indexes for a vector store.
- **GET `/api/vector/:vectorName/indexes/:indexName`**: Get details about a specific index.
- **DELETE `/api/vector/:vectorName/indexes/:indexName`**: Delete a specific index.

### OpenAPI Specification

- **GET `/openapi.json`**: Returns an auto-generated OpenAPI specification for your project's routes.
- **GET `/swagger-ui`**: Access Swagger UI for API documentation.

## Additional Notes

The port defaults to 4111. Both the port and hostname can be configured via the mastra server config. See [Launch Development Server](/docs/server-db/local-dev-playground) for configuration details.

Make sure you have your environment variables set up in your `.env.development` or `.env` file for any providers you use (e.g., `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, etc.).

Make sure the `index.ts` file in your Mastra folder exports the Mastra instance for the dev server to read.

### Example request

To test an agent after running `mastra dev`:

```bash copy
curl -X POST http://localhost:4111/api/agents/myAgent/generate \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      { "role": "user", "content": "Hello, how can you assist me today?" }
    ]
  }'
```

## Advanced usage

The `mastra dev` server obeys a few extra environment variables that can
be handy during development:

### Disable build caching

Set `MASTRA_DEV_NO_CACHE=1` to force a full rebuild rather than using
the cached assets under `.mastra/`:

```bash copy
MASTRA_DEV_NO_CACHE=1 mastra dev
```

This helps when you are debugging bundler plugins or suspect stale
output.

### Limit parallelism

`MASTRA_CONCURRENCY` caps how many expensive operations run in parallel
(primarily build and evaluation steps). For example:

```bash copy
MASTRA_CONCURRENCY=4 mastra dev
```

Leave it unset to let the CLI pick a sensible default for the machine.

### Custom provider endpoints

When using providers supported by the Vercel AI SDK you can redirect
requests through proxies or internal gateways by setting a base URL. For
OpenAI:

```bash copy
OPENAI_API_KEY=<your-api-key> \
OPENAI_BASE_URL=https://openrouter.example/v1 \
mastra dev
```

and for Anthropic:

```bash copy
OPENAI_API_KEY=<your-api-key> \
ANTHROPIC_BASE_URL=https://anthropic.internal \
mastra dev
```

These are forwarded by the AI SDK and work with any `openai()` or
`anthropic()` calls.

### Disable telemetry

To opt out of anonymous CLI analytics set
`MASTRA_TELEMETRY_DISABLED=1`. This also prevents tracking within the
local playground.

```bash copy
MASTRA_TELEMETRY_DISABLED=1 mastra dev
```
