---
title: "@mastra/mcp-docs-server"
description: "Serve <PERSON> docs, examples and blog posts over MCP"
---

The `@mastra/mcp-docs-server` package runs a small [Model Context
Protocol](https://github.com/modelcontextprotocol/mcp) server that makes
<PERSON><PERSON> documentation, code examples, blog posts and changelogs
queryable by an LLM agent. It can be invoked manually from the command
line or configured inside an MCP-aware IDE such as Cursor or Windsurf.

## Running from the CLI

```bash
npx -y @mastra/mcp-docs-server
```

The command above runs a stdio based MCP server. The process will keep
reading requests from `stdin` and returning responses on `stdout`. This
is the same command that IDE integrations use. When run manually it can
be pointed at the `@wong2/mcp-cli` package for exploration.

### Examples

Rebuild the docs before serving (useful if you've modified docs locally):

```bash
REBUILD_DOCS_ON_START=true npx -y @mastra/mcp-docs-server
```

Enable verbose logging while experimenting:

```bash
DEBUG=1 npx -y @mastra/mcp-docs-server
```

Serve blog posts from a custom domain:

```bash
BLOG_URL=https://my-blog.example npx -y @mastra/mcp-docs-server
```

## Environment variables

`@mastra/mcp-docs-server` honours a few environment variables that tweak
its behaviour:

- **`REBUILD_DOCS_ON_START`** - when set to `true` the server rebuilds
  the `.docs` directory before binding to stdio. This can be helpful
  after editing or adding documentation locally.
- **`PREPARE`** - the docs build step (`pnpm mcp-docs-server
prepare-docs`) looks for `PREPARE=true` to copy Markdown sources from
  the repository into `.docs`.
- **`BLOG_URL`** - base URL used for fetching blog posts.
  Defaults to `https://mastra.ai`.
- **`DEBUG`** or **`NODE_ENV=development`** - increase logging written
  to `stderr`.

No other variables are required for a basic run; the server ships with a
pre-built docs directory.

## Rebuilding with custom docs

The package includes a precompiled copy of the documentation. If you
want to experiment with additional content you can rebuild the
`.docs` directory locally:

```bash
pnpm mcp-docs-server prepare-docs
```

The script will copy documentation from
`mastra/docs/src/content/en/docs` and
`mastra/docs/src/content/en/reference` into the package. Once rebuilt,
start the server with `REBUILD_DOCS_ON_START=true` so the fresh content
is served.

A rebuild is only necessary if you need to serve customised docs. For
regular use you can rely on the published package contents.

For IDE configuration details see the
[Getting started guide](/docs/getting-started/mcp-docs-server).
