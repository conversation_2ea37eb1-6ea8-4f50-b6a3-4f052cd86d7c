---
title: "Reference: Chroma Vector Store | Vector Databases | RAG | Mastra Docs"
description: Documentation for the ChromaVector class in Mastra, which provides vector search using ChromaDB.
---

# Chroma Vector Store

The ChromaVector class provides vector search using [ChromaDB](https://www.trychroma.com/), an open-source embedding database.
It offers efficient vector search with metadata filtering and hybrid search capabilities.

## Constructor Options

<PropertiesTable
  content={[
    {
      name: "path",
      type: "string",
      description: "URL path to ChromaDB instance",
    },
    {
      name: "auth",
      type: "object",
      isOptional: true,
      description: "Authentication configuration",
    },
  ]}
/>

### auth

<PropertiesTable
  content={[
    {
      name: "provider",
      type: "string",
      description: "Authentication provider",
    },
    {
      name: "credentials",
      type: "string",
      description: "Authentication credentials",
    },
  ]}
/>

## Methods

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to create",
    },
    {
      name: "dimension",
      type: "number",
      description: "Vector dimension (must match your embedding model)",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description: "Distance metric for similarity search",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to upsert into",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "Array of embedding vectors",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "Metadata for each vector",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description: "Optional vector IDs (auto-generated if not provided)",
    },
    {
      name: "documents",
      type: "string[]",
      isOptional: true,
      description:
        "Chroma-specific: Original text documents associated with the vectors",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to query",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "Query vector to find similar vectors",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "Number of results to return",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "Metadata filters for the query",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "Whether to include vectors in the results",
    },
    {
      name: "documentFilter",
      type: "Record<string, any>",
      isOptional: true,
      description: "Chroma-specific: Filter to apply on the document content",
    },
  ]}
/>

### listIndexes()

Returns an array of index names as strings.

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to describe",
    },
  ]}
/>

Returns:

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to delete",
    },
  ]}
/>

### updateVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the vector to update",
    },
    {
      name: "id",
      type: "string",
      description: "ID of the vector to update",
    },
    {
      name: "update",
      type: "object",
      description: "Update parameters",
    },
  ]}
/>

The `update` object can contain:

<PropertiesTable
  content={[
    {
      name: "vector",
      type: "number[]",
      isOptional: true,
      description: "New vector to replace the existing one",
    },
    {
      name: "metadata",
      type: "Record<string, any>",
      isOptional: true,
      description: "New metadata to replace the existing metadata",
    },
  ]}
/>

### deleteVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the vector to delete",
    },
    {
      name: "id",
      type: "string",
      description: "ID of the vector to delete",
    },
  ]}
/>

## Response Types

Query results are returned in this format:

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  document?: string; // Chroma-specific: Original document if it was stored
  vector?: number[]; // Only included if includeVector is true
}
```

## Error Handling

The store throws typed errors that can be caught:

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

## Related

- [Metadata Filters](./metadata-filters)
