---
title: "Reference: Pinecone Vector Store | Vector DBs | RAG | Mastra Docs"
description: Documentation for the PineconeVector class in Mastra, which provides an interface to Pinecone's vector database.
---

# Pinecone Vector Store

The PineconeVector class provides an interface to [Pinecone](https://www.pinecone.io/)'s vector database.
It provides real-time vector search, with features like hybrid search, metadata filtering, and namespace management.

## Constructor Options

<PropertiesTable
  content={[
    {
      name: "api<PERSON>ey",
      type: "string",
      description: "Pinecone API key",
    },
    {
      name: "environment",
      type: "string",
      description: 'Pinecone environment (e.g., "us-west1-gcp")',
    },
  ]}
/>

## Methods

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to create",
    },
    {
      name: "dimension",
      type: "number",
      description: "Vector dimension (must match your embedding model)",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description:
        "Distance metric for similarity search. Use 'dotproduct' if you plan to use hybrid search.",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of your Pinecone index",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "Array of dense embedding vectors",
    },
    {
      name: "sparseVectors",
      type: "{ indices: number[], values: number[] }[]",
      isOptional: true,
      description:
        "Array of sparse vectors for hybrid search. Each vector must have matching indices and values arrays.",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "Metadata for each vector",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description: "Optional vector IDs (auto-generated if not provided)",
    },
    {
      name: "namespace",
      type: "string",
      isOptional: true,
      description:
        "Optional namespace to store vectors in. Vectors in different namespaces are isolated from each other.",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to query",
    },
    {
      name: "vector",
      type: "number[]",
      description: "Dense query vector to find similar vectors",
    },
    {
      name: "sparseVector",
      type: "{ indices: number[], values: number[] }",
      isOptional: true,
      description:
        "Optional sparse vector for hybrid search. Must have matching indices and values arrays.",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "Number of results to return",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "Metadata filters for the query",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "Whether to include the vector in the result",
    },
    {
      name: "namespace",
      type: "string",
      isOptional: true,
      description:
        "Optional namespace to query vectors from. Only returns results from the specified namespace.",
    },
  ]}
/>

### listIndexes()

Returns an array of index names as strings.

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to describe",
    },
  ]}
/>

Returns:

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to delete",
    },
  ]}
/>

### updateVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the vector",
    },
    {
      name: "id",
      type: "string",
      description: "ID of the vector to update",
    },
    {
      name: "update",
      type: "object",
      description: "Update parameters",
    },
    {
      name: "update.vector",
      type: "number[]",
      isOptional: true,
      description: "New vector values to update",
    },
    {
      name: "update.metadata",
      type: "Record<string, any>",
      isOptional: true,
      description: "New metadata to update",
    },
  ]}
/>

### deleteVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the vector",
    },
    {
      name: "id",
      type: "string",
      description: "ID of the vector to delete",
    },
  ]}
/>

## Response Types

Query results are returned in this format:

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[]; // Only included if includeVector is true
}
```

## Error Handling

The store throws typed errors that can be caught:

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

### Environment Variables

Required environment variables:

- `PINECONE_API_KEY`: Your Pinecone API key
- `PINECONE_ENVIRONMENT`: Pinecone environment (e.g., 'us-west1-gcp')

## Hybrid Search

Pinecone supports hybrid search by combining dense and sparse vectors. To use hybrid search:

1. Create an index with `metric: 'dotproduct'`
2. During upsert, provide sparse vectors using the `sparseVectors` parameter
3. During query, provide a sparse vector using the `sparseVector` parameter

## Related

- [Metadata Filters](./metadata-filters)
