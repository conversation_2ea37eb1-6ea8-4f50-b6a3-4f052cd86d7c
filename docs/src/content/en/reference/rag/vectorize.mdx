---
title: "Reference: Cloudflare Vector Store | Vector Databases | RAG | Mastra Docs"
description: Documentation for the CloudflareVector class in Mastra, which provides vector search using Cloudflare Vectorize.
---

# Cloudflare Vector Store

The CloudflareVector class provides vector search using [Cloudflare Vectorize](https://developers.cloudflare.com/vectorize/), a vector database service integrated with Cloudflare's edge network.

## Constructor Options

<PropertiesTable
  content={[
    {
      name: "accountId",
      type: "string",
      description: "Cloudflare account ID",
    },
    {
      name: "apiToken",
      type: "string",
      description: "Cloudflare API token with Vectorize permissions",
    },
  ]}
/>

## Methods

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to create",
    },
    {
      name: "dimension",
      type: "number",
      description: "Vector dimension (must match your embedding model)",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description:
        "Distance metric for similarity search (dotproduct maps to dot-product)",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to upsert into",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "Array of embedding vectors",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "Metadata for each vector",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description: "Optional vector IDs (auto-generated if not provided)",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to query",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "Query vector to find similar vectors",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "Number of results to return",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "Metadata filters for the query",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "Whether to include vectors in the results",
    },
  ]}
/>

### listIndexes()

Returns an array of index names as strings.

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to describe",
    },
  ]}
/>

Returns:

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to delete",
    },
  ]}
/>

### createMetadataIndex()

Creates an index on a metadata field to enable filtering.

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the metadata field",
    },
    {
      name: "propertyName",
      type: "string",
      description: "Name of the metadata field to index",
    },
    {
      name: "indexType",
      type: "'string' | 'number' | 'boolean'",
      description: "Type of the metadata field",
    },
  ]}
/>

### deleteMetadataIndex()

Removes an index from a metadata field.

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the metadata field",
    },
    {
      name: "propertyName",
      type: "string",
      description: "Name of the metadata field to remove indexing from",
    },
  ]}
/>

### listMetadataIndexes()

Lists all metadata field indexes for an index.

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to list metadata indexes for",
    },
  ]}
/>

### updateVector()

Updates a vector or metadata for a specific ID within an index.

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the ID to update",
    },
    {
      name: "id",
      type: "string",
      description: "Unique identifier of the vector or metadata to update",
    },
    {
      name: "update",
      type: "{ vector?: number[]; metadata?: Record<string, any>; }",
      description: "Object containing the vector and/or metadata to update",
    },
  ]}
/>

### deleteVector()

Deletes a vector and its associated metadata for a specific ID within an index.

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the ID to delete",
    },
    {
      name: "id",
      type: "string",
      description: "Unique identifier of the vector and metadata to delete",
    },
  ]}
/>

## Response Types

Query results are returned in this format:

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[];
}
```

## Error Handling

The store throws typed errors that can be caught:

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

## Environment Variables

Required environment variables:

- `CLOUDFLARE_ACCOUNT_ID`: Your Cloudflare account ID
- `CLOUDFLARE_API_TOKEN`: Your Cloudflare API token with Vectorize permissions

## Related

- [Metadata Filters](./metadata-filters)
