---
title: "Reference: Astra Vector Store | Vector Databases | RAG | Mastra Docs"
description: Documentation for the AstraVector class in Mastra, which provides vector search using DataStax Astra DB.
---

# Astra Vector Store

The AstraVector class provides vector search using [DataStax Astra DB](https://www.datastax.com/products/datastax-astra), a cloud-native, serverless database built on Apache Cassandra.
It provides vector search capabilities with enterprise-grade scalability and high availability.

## Constructor Options

<PropertiesTable
  content={[
    {
      name: "token",
      type: "string",
      description: "Astra DB API token",
    },
    {
      name: "endpoint",
      type: "string",
      description: "Astra DB API endpoint",
    },
    {
      name: "keyspace",
      type: "string",
      isOptional: true,
      description: "Optional keyspace name",
    },
  ]}
/>

## Methods

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to create",
    },
    {
      name: "dimension",
      type: "number",
      description: "Vector dimension (must match your embedding model)",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description:
        "Distance metric for similarity search (maps to dot_product for dotproduct)",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to upsert into",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "Array of embedding vectors",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "Metadata for each vector",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description: "Optional vector IDs (auto-generated if not provided)",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to query",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "Query vector to find similar vectors",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "Number of results to return",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "Metadata filters for the query",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "Whether to include vectors in the results",
    },
  ]}
/>

### listIndexes()

Returns an array of index names as strings.

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to describe",
    },
  ]}
/>

Returns:

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index to delete",
    },
  ]}
/>

### updateVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the vector",
    },
    {
      name: "id",
      type: "string",
      description: "ID of the vector to update",
    },
    {
      name: "update",
      type: "object",
      description: "Update object containing vector and/or metadata changes",
      properties: [
        {
          name: "vector",
          type: "number[]",
          isOptional: true,
          description: "New vector values",
        },
        {
          name: "metadata",
          type: "Record<string, any>",
          isOptional: true,
          description: "New metadata values",
        },
      ],
    },
  ]}
/>

### deleteVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "Name of the index containing the vector",
    },
    {
      name: "id",
      type: "string",
      description: "ID of the vector to delete",
    },
  ]}
/>

## Response Types

Query results are returned in this format:

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[]; // Only included if includeVector is true
}
```

## Error Handling

The store throws typed errors that can be caught:

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

## Environment Variables

Required environment variables:

- `ASTRA_DB_TOKEN`: Your Astra DB API token
- `ASTRA_DB_ENDPOINT`: Your Astra DB API endpoint

## Related

- [Metadata Filters](./metadata-filters)
