---
title: "Reference: Rerank | Document Retrieval | RAG | Mastra Docs"
description: Documentation for the rerank function in Mastra, which provides advanced reranking capabilities for vector search results.
---

# rerankWithScorer()

The `rerankWithScorer()` function provides advanced reranking capabilities for vector search results by combining semantic relevance, vector similarity, and position-based scoring.

```typescript
function rerankWithScorer({
  results: QueryResult[],
  query: string,
  scorer: RelevanceScoreProvider,
  options?: RerankerFunctionOptions,
}): Promise<RerankResult[]>;
```

## Usage Example

```typescript
import { openai } from "@ai-sdk/openai";
import { rerankWithScorer as rerank, CohereRelevanceScorer } from "@mastra/rag";

const scorer = new CohereRelevanceScorer('rerank-v3.5');

const rerankedResults = await rerank({
  results: vectorSearchResults,
  query: "How do I deploy to production?",
  scorer,
  options: {
    weights: {
      semantic: 0.5,
      vector: 0.3,
      position: 0.2,
    },
    topK: 3,
  },
});
```

## Parameters

<PropertiesTable
  content={[
    {
      name: "results",
      type: "QueryResult[]",
      description: "The vector search results to rerank",
      isOptional: false,
    },
    {
      name: "query",
      type: "string",
      description: "The search query text used to evaluate relevance",
      isOptional: false,
    },
    {
      name: "scorer",
      type: "RelevanceScoreProvider",
      description: "The relevance scorer to use for reranking",
      isOptional: false,
    },
    {
      name: "options",
      type: "RerankerFunctionOptions",
      description: "Options for the reranking model",
      isOptional: true,
    },
  ]}
/>

The `rerankWithScorer` function accepts any `RelevanceScoreProvider` from @mastra/rag.

> **Note:** For semantic scoring to work properly during re-ranking, each result must include the text content in its `metadata.text` field.

### RerankerFunctionOptions

<PropertiesTable
  content={[
    {
      name: "weights",
      type: "WeightConfig",
      description:
        "Weights for different scoring components (must add up to 1)",
      isOptional: true,
      properties: [
        {
          type: "number",
          parameters: [
            {
              name: "semantic",
              description: "Weight for semantic relevance",
              isOptional: true,
              type: "number (default: 0.4)",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "vector",
              description: "Weight for vector similarity",
              isOptional: true,
              type: "number (default: 0.4)",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "position",
              description: "Weight for position-based scoring",
              isOptional: true,
              type: "number (default: 0.2)",
            },
          ],
        },
      ],
    },
    {
      name: "queryEmbedding",
      type: "number[]",
      description: "Embedding of the query",
      isOptional: true,
    },
    {
      name: "topK",
      type: "number",
      description: "Number of top results to return",
      isOptional: true,
      defaultValue: "3",
    },
  ]}
/>

## Returns

The function returns an array of `RerankResult` objects:

<PropertiesTable
  content={[
    {
      name: "result",
      type: "QueryResult",
      description: "The original query result",
    },
    {
      name: "score",
      type: "number",
      description: "Combined reranking score (0-1)",
    },
    {
      name: "details",
      type: "ScoringDetails",
      description: "Detailed scoring information",
    },
  ]}
/>

### ScoringDetails

<PropertiesTable
  content={[
    {
      name: "semantic",
      type: "number",
      description: "Semantic relevance score (0-1)",
    },
    {
      name: "vector",
      type: "number",
      description: "Vector similarity score (0-1)",
    },
    {
      name: "position",
      type: "number",
      description: "Position-based score (0-1)",
    },
    {
      name: "queryAnalysis",
      type: "object",
      description: "Query analysis details",
      isOptional: true,
      properties: [
        {
          type: "number",
          parameters: [
            {
              name: "magnitude",
              description: "Magnitude of the query",
            },
          ],
        },
        {
          type: "number[]",
          parameters: [
            {
              name: "dominantFeatures",
              description: "Dominant features of the query",
            },
          ],
        },
      ],
    },
  ]}
/>

## Related

- [createVectorQueryTool](../tools/vector-query-tool)
