const meta = {
  workflow: "Workflow",
  step: "createStep()",
  then: "then()",
  parallel: "parallel()",
  branch: "branch()",
  dowhile: "dowhile()",
  dountil: "dountil()",
  foreach: "foreach()",
  map: "map()",
  sleep: "sleep()",
  sleepUntil: "sleepUntil()",
  waitForEvent: "waitForEvent()",
  sendEvent: "sendEvent()",
  commit: "commit()",
  "create-run": "createRunAsync()",
  snapshots: "Snapshots",
  watch: "watch()",
  stream: "stream()",
  execute: "execute()",
  resume: "resume()",
  start: "start()",
};

export default meta;
