---
title: "Mastra Core"
description: Documentation for the Mastra Class, the core entry point for managing agents, workflows, MCP servers, and server endpoints.
---

# The Mastra Class

The `Mastra` class is the central orchestrator in any Mastra application, managing agents, workflows, storage, logging, telemetry, and more. Typically, you create a single instance of `Mastra` to coordinate your application.

Think of `Mastra` as a top-level registry:

- Registering **integrations** makes them accessible to **agents**, **workflows**, and **tools** alike.
- **tools** aren’t registered on `Mastra` directly but are associated with agents and discovered automatically.


## Importing

```typescript
import { Mastra } from "@mastra/core";
```

## Constructor

Creates a new `Mastra` instance with the specified configuration.

```typescript
constructor(config?: Config);
```

## Initialization

The Mastra class is typically initialized in your `src/mastra/index.ts` file:

```typescript filename="src/mastra/index.ts" showLineNumbers copy
import { Mastra } from "@mastra/core";
import { LibSQLStore } from "@mastra/libsql";
import { weatherAgent } from "./agents/weather-agent";

export const mastra = new Mastra({
  agents: { weatherAgent },
  storage: new LibSQLStore({
    url: ":memory:",
  }),
});
```

## Configuration Object

The constructor accepts an optional `Config` object to customize its behavior and integrate various Mastra components. All properties on the `Config` object are optional.

### Properties

<PropertiesTable
  content={[
    {
      name: "agents",
      type: "Agent[]",
      description: "Array of Agent instances to register",
      isOptional: true,
      defaultValue: "[]",
    },
    {
      name: "tools",
      type: "Record<string, ToolApi>",
      description:
        "Custom tools to register. Structured as a key-value pair, with keys being the tool name and values being the tool function.",
      isOptional: true,
      defaultValue: "{}",
    },
    {
      name: "storage",
      type: "MastraStorage",
      description: "Storage engine instance for persisting data",
      isOptional: true,
    },
    {
      name: "vectors",
      type: "Record<string, MastraVector>",
      description:
        "Vector store instance, used for semantic search and vector-based tools (eg Pinecone, PgVector or Qdrant)",
      isOptional: true,
    },
    {
      name: "logger",
      type: "Logger",
      description: "Logger instance created with new PinoLogger()",
      isOptional: true,
      defaultValue: "Console logger with INFO level",
    },
    {
      name: "workflows",
      type: "Record<string, Workflow>",
      description:
        "Workflows to register. Structured as a key-value pair, with keys being the workflow name and values being the workflow instance.",
      isOptional: true,
      defaultValue: "{}",
    },
    {
      name: "tts",
      type: "Record<string, MastraTTS>",
      isOptional: true,
      description: "An object for registering Text-To-Speech services.",
    },
    {
      name: "telemetry",
      type: "OtelConfig",
      isOptional: true,
      description: "Configuration for OpenTelemetry integration.",
    },
    {
      name: "deployer",
      type: "MastraDeployer",
      isOptional: true,
      description: "An instance of a MastraDeployer for managing deployments.",
    },
    {
      name: "server",
      type: "ServerConfig",
      description:
        "Server configuration including port, host, timeout, API routes, middleware, CORS settings, and build options for Swagger UI, API request logging, and OpenAPI docs.",
      isOptional: true,
      defaultValue:
        "{ port: 4111, host: localhost,  cors: { origin: '*', allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], allowHeaders: ['Content-Type', 'Authorization', 'x-mastra-client-type'], exposeHeaders: ['Content-Length', 'X-Requested-With'], credentials: false } }",
    },
    {
      name: "mcpServers",
      type: "Record<string, MCPServerBase>",
      isOptional: true,
      description:
        "An object where keys are unique server identifiers and values are instances of MCPServer or classes extending MCPServerBase. This allows Mastra to be aware of and potentially manage these MCP servers.",
    },
    {
      name: "bundler",
      type: "BundlerConfig",
      description: "Configuration for the asset bundler.",
    },
  ]}
/>

## Usage

Any of the below methods can be used with the Mastra class, for example:

```typescript {3} filename="example.ts" showLineNumbers
import { mastra } from "./mastra";

const agent = mastra.getAgent("weatherAgent");
const result = await agent.generate("What's the weather like in London?");
```

### Methods

<PropertiesTable
  content={[
    {
      name: "getAgent(name)",
      type: "Agent",
      description:
        "Returns an agent instance by id. Throws if agent not found.",
      example: 'const agent = mastra.getAgent("agentOne");',
    },
    {
      name: "getAgents()",
      type: "Record<string, Agent>",
      description: "Returns all registered agents as a key-value object.",
      example: "const agents = mastra.getAgents();",
    },
    {
      name: "getWorkflow(id, { serialized })",
      type: "Workflow",
      description:
        "Returns a workflow instance by id. The serialized option (default: false) returns a simplified representation with just the name.",
      example: 'const workflow = mastra.getWorkflow("myWorkflow");',
    },
    {
      name: "getWorkflows({ serialized })",
      type: "Record<string, Workflow>",
      description:
        "Returns all registered workflows. The serialized option (default: false) returns simplified representations.",
      example: "const workflows = mastra.getWorkflows();",
    },
    {
      name: "getVector(name)",
      type: "MastraVector",
      description:
        "Returns a vector store instance by name. Throws if not found.",
      example: 'const vectorStore = mastra.getVector("myVectorStore");',
    },
    {
      name: "getVectors()",
      type: "Record<string, MastraVector>",
      description:
        "Returns all registered vector stores as a key-value object.",
      example: "const vectorStores = mastra.getVectors();",
    },
    {
      name: "getDeployer()",
      type: "MastraDeployer | undefined",
      description: "Returns the configured deployer instance, if any.",
      example: "const deployer = mastra.getDeployer();",
    },
    {
      name: "getStorage()",
      type: "MastraStorage | undefined",
      description: "Returns the configured storage instance.",
      example: "const storage = mastra.getStorage();",
    },
    {
      name: "getMemory()",
      type: "MastraMemory | undefined",
      description:
        "Returns the configured memory instance. Note: This is deprecated, memory should be added to agents directly.",
      example: "const memory = mastra.getMemory();",
    },
    {
      name: "getServer()",
      type: "ServerConfig | undefined",
      description:
        "Returns the server configuration including port, timeout, API routes, middleware, CORS settings, and build options.",
      example: "const serverConfig = mastra.getServer();",
    },
    {
      name: "setStorage(storage)",
      type: "void",
      description: "Sets the storage instance for the Mastra instance.",
      example: "mastra.setStorage(new DefaultStorage());",
    },
    {
      name: "setLogger({ logger })",
      type: "void",
      description:
        "Sets the logger for all components (agents, workflows, etc.).",
      example:
        'mastra.setLogger({ logger: new PinoLogger({ name: "MyLogger" }) });',
    },
    {
      name: "setTelemetry(telemetry)",
      type: "void",
      description: "Sets the telemetry configuration for all components.",
      example: 'mastra.setTelemetry({ export: { type: "console" } });',
    },
    {
      name: "getLogger()",
      type: "Logger",
      description: "Gets the configured logger instance.",
      example: "const logger = mastra.getLogger();",
    },
    {
      name: "getTelemetry()",
      type: "Telemetry | undefined",
      description: "Gets the configured telemetry instance.",
      example: "const telemetry = mastra.getTelemetry();",
    },
    {
      name: "getLogsByRunId({ runId, transportId })",
      type: "Promise<any>",
      description: "Retrieves logs for a specific run ID and transport ID.",
      example:
        'const logs = await mastra.getLogsByRunId({ runId: "123", transportId: "456" });',
    },
    {
      name: "getLogs(transportId)",
      type: "Promise<any>",
      description: "Retrieves all logs for a specific transport ID.",
      example: 'const logs = await mastra.getLogs("transportId");',
    },
    {
      name: "getMCPServers()",
      type: "Record<string, MCPServerBase> | undefined",
      description: "Retrieves all registered MCP server instances.",
      example: "const mcpServers = mastra.getMCPServers();",
    },
  ]}
/>

## Error Handling

The Mastra class methods throw typed errors that can be caught:

```typescript {8} filename="example.ts" showLineNumbers
import { mastra } from "./mastra";

try {
  const agent = mastra.getAgent("weatherAgent");
  const result = await agent.generate("What's the weather like in London?");

} catch (error) {
  if (error instanceof Error) {
    console.log(error.message);
  }
}
```
