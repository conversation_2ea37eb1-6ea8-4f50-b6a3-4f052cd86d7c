---
title: "Example: Reranking with Cohere | RAG | Mastra Docs"
description: Example of using Mastra to improve document retrieval relevance with Cohere's reranking service.
---

# Reranking with Cohere

When retrieving documents for RAG, initial vector similarity search may miss important semantic matches.

Cohere's reranking service helps improve result relevance by reordering documents using multiple scoring factors.

```typescript
import { 
  rerankWithScorer as rerank, 
  CohereRelevanceScorer 
} from "@mastra/rag";

const results = rerank({
  results: searchResults,
  query: "deployment configuration",
  scorer: new CohereRelevanceScorer('rerank-v3.5'),
  {
    topK: 5,
    weights: {
      semantic: 0.4,
      vector: 0.4,
      position: 0.2,
    },
  },
);
```

## Links

- [rerank() reference](/reference/rag/rerankWithScorer.mdx)
- [Retrieval docs](/reference/rag/retrieval.mdx)
