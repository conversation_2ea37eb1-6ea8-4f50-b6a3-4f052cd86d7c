---
title: "Example: Semantically Chunking Markdown | RAG | Mastra Docs"
description: Example of using <PERSON><PERSON> to chunk markdown documents for search or retrieval purposes.
---

import { GithubLink } from "@/components/github-link";

# Chunk Markdown

Markdown is more information-dense than raw HTML, making it easier to work with for RAG pipelines. When working with markdown, you need to split it into smaller pieces while preserving headers and formatting. The `chunk` method handles Markdown-specific elements like headers, lists, and code blocks intelligently. This example shows how to chunk markdown documents for search or retrieval purposes.

```tsx copy
import { MDocument } from "@mastra/rag";

const doc = MDocument.fromMarkdown("# Your markdown content...");

const chunks = await doc.chunk();
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/chunk-markdown"
  }
/>
