---
title: "Example: Adjusting Chunk Delimiters | RAG | Mastra Docs"
description: Adjust chunk delimiters in Mastra to better match your content structure.
---

import { GithubLink } from "@/components/github-link";

# Adjust Chunk Delimiters

When processing large documents, you may want to control how the text is split into smaller chunks. By default, documents are split on newlines, but you can customize this behavior to better match your content structure. This example shows how to specify a custom delimiter for chunking documents.

```tsx copy
import { MDocument } from "@mastra/rag";

const doc = MDocument.fromText("Your plain text content...");

const chunks = await doc.chunk({
  separator: "\n",
});
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/adjust-chunk-delimiters"
  }
/>
