---
title: "Example: Semantically Chunking HTML | RAG | Mastra Docs"
description: Chunk HTML content in Mastra to semantically chunk the document.
---

import { GithubLink } from "@/components/github-link";

# Semantically Chunking HTML

When working with HTML content, you often need to break it down into smaller, manageable pieces while preserving the document structure. The chunk method splits HTML content intelligently, maintaining the integrity of HTML tags and elements. This example shows how to chunk HTML documents for search or retrieval purposes.

```tsx copy
import { MDocument } from "@mastra/rag";

const html = `
<div>
    <h1>h1 content...</h1>
    <p>p content...</p>
</div>
`;

const doc = MDocument.fromHTML(html);

const chunks = await doc.chunk({
  headers: [
    ["h1", "Header 1"],
    ["p", "Paragraph"],
  ],
});

console.log(chunks);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/chunk-html"
  }
/>
