---
title: "Example: Semantically Chunking Text | RAG | Mastra Docs"
description: Example of using <PERSON>stra to split large text documents into smaller chunks for processing.
---

import { GithubLink } from "@/components/github-link";

# Chunk Text

When working with large text documents, you need to break them down into smaller, manageable pieces for processing. The chunk method splits text content into segments that can be used for search, analysis, or retrieval. This example shows how to split plain text into chunks using default settings.

```tsx copy
import { MDocument } from "@mastra/rag";

const doc = MDocument.fromText("Your plain text content...");

const chunks = await doc.chunk();
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/chunk-text"
  }
/>
