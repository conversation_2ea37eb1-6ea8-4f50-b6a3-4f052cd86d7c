---
title: "Example: Semantically Chunking JSON | RAG | Mastra Docs"
description: Chunk JSON data in Mastra to semantically chunk the document.
---

import { GithubLink } from "@/components/github-link";

# Semantically Chunking JSON

When working with JSON data, you need to split it into smaller pieces while preserving the object structure. The chunk method breaks down JSON content intelligently, maintaining the relationships between keys and values. This example shows how to chunk JSON documents for search or retrieval purposes.

```tsx copy
import { MDocument } from "@mastra/rag";

const testJson = {
  name: "<PERSON>",
  age: 30,
  email: "<EMAIL>",
};

const doc = MDocument.fromJSON(JSON.stringify(testJson));

const chunks = await doc.chunk({
  maxSize: 100,
});

console.log(chunks);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/chunk-json"
  }
/>
