---
title: "Example: Embedding Chunk Arrays | RAG | Mastra Docs"
description: Example of using <PERSON><PERSON> to generate embeddings for an array of text chunks for similarity search.
---

import { GithubLink } from "@/components/github-link";

# Embed Chunk Array

After chunking documents, you need to convert the text chunks into numerical vectors that can be used for similarity search. The `embed` method transforms text chunks into embeddings using your chosen provider and model. This example shows how to generate embeddings for an array of text chunks.

```tsx copy
import { openai } from "@ai-sdk/openai";
import { MDocument } from "@mastra/rag";
import { embed } from "ai";

const doc = MDocument.fromText("Your text content...");

const chunks = await doc.chunk();

const { embeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: chunks.map((chunk) => chunk.text),
});
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/embed-chunk-array"
  }
/>
