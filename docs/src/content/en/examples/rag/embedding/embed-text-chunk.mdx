---
title: "Example: Embedding Text Chunks | RAG | Mastra Docs"
description: Example of using <PERSON><PERSON> to generate an embedding for a single text chunk for similarity search.
---

import { GithubLink } from "@/components/github-link";

# Embed Text Chunk

When working with individual text chunks, you need to convert them into numerical vectors for similarity search. The `embed` method transforms a single text chunk into an embedding using your chosen provider and model.

```tsx copy
import { openai } from "@ai-sdk/openai";
import { MDocument } from "@mastra/rag";
import { embed } from "ai";

const doc = MDocument.fromText("Your text content...");

const chunks = await doc.chunk();

const { embedding } = await embed({
  model: openai.embedding("text-embedding-3-small"),
  value: chunks[0].text,
});
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/embed-text-chunk"
  }
/>
