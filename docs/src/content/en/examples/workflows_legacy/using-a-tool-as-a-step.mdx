---
title: "Example: Using a Tool as a Step | Workflows (Legacy) | Mastra Docs"
description: Example of using <PERSON><PERSON> to integrate a custom tool as a step in a legacy workflow.
---

import { GithubLink } from "@/components/github-link";

# Tool as a Workflow step (Legacy)

This example demonstrates how to create and integrate a custom tool as a workflow step, showing how to define input/output schemas and implement the tool's execution logic.

```ts showLineNumbers copy
import { createTool } from "@mastra/core/tools";
import { LegacyWorkflow } from "@mastra/core/workflows/legacy";
import { z } from "zod";

const crawlWebpage = createTool({
  id: "Crawl Webpage",
  description: "Crawls a webpage and extracts the text content",
  inputSchema: z.object({
    url: z.string().url(),
  }),
  outputSchema: z.object({
    rawText: z.string(),
  }),
  execute: async ({ context }) => {
    const response = await fetch(context.triggerData.url);
    const text = await response.text();
    return { rawText: "This is the text content of the webpage: " + text };
  },
});

const contentWorkflow = new LegacyWorkflow({ name: "content-review" });

contentWorkflow.step(crawlWebpage).commit();

const { start } = contentWorkflow.createRun();

const res = await start({ triggerData: { url: "https://example.com" } });

console.log(res.results);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/workflows-legacy/tool-as-workflow-step"
  }
/>

## Workflows (Legacy)

The following links provide example documentation for legacy workflows:

- [Creating a Simple Workflow (Legacy)](/examples/workflows_legacy/creating-a-workflow)
- [Workflow (Legacy) with Sequential Steps](/examples/workflows_legacy/sequential-steps)
- [Parallel Execution with Steps](/examples/workflows_legacy/parallel-steps)
- [Branching Paths](/examples/workflows_legacy/branching-paths)
- [Workflow (Legacy) with Conditional Branching (experimental)](/examples/workflows_legacy/conditional-branching)
- [Calling an Agent From a Workflow (Legacy)](/examples/workflows_legacy/calling-agent)
- [Workflow (Legacy) with Cyclical dependencies](/examples/workflows_legacy/cyclical-dependencies)
- [Data Mapping with Workflow Variables (Legacy)](/examples/workflows_legacy/workflow-variables)
- [Human in the Loop Workflow (Legacy)](/examples/workflows_legacy/human-in-the-loop)
- [Workflow (Legacy) with Suspend and Resume](/examples/workflows_legacy/suspend-and-resume)
