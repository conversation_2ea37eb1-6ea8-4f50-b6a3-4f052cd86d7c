---
title: "Example: Calling an Agent from a Workflow (Legacy) | Mastra Docs"
description: Example of using <PERSON><PERSON> to call an AI agent from within a legacy workflow step.
---

import { GithubLink } from "@/components/github-link";

# Calling an Agent From a Workflow (Legacy)

This example demonstrates how to create a legacy workflow that calls an AI agent to process messages and generate responses, and execute it within a legacy workflow step.

```ts showLineNumbers copy
import { openai } from "@ai-sdk/openai";
import { <PERSON><PERSON> } from "@mastra/core";
import { Agent } from "@mastra/core/agent";
import { LegacyStep, LegacyWorkflow } from "@mastra/core/workflows/legacy";
import { z } from "zod";

const penguin = new Agent({
  name: "agent skipper",
  instructions: `You are skipper from penguin of madagascar, reply as that`,
  model: openai("gpt-4o-mini"),
});

const newWorkflow = new LegacyWorkflow({
  name: "pass message to the workflow",
  triggerSchema: z.object({
    message: z.string(),
  }),
});

const replyAsSkipper = new LegacyStep({
  id: "reply",
  outputSchema: z.object({
    reply: z.string(),
  }),
  execute: async ({ context, mastra }) => {
    const skipper = mastra?.getAgent("penguin");

    const res = await skipper?.generate(context?.triggerData?.message);
    return { reply: res?.text || "" };
  },
});

newWorkflow.step(replyAsSkipper);
newWorkflow.commit();

const mastra = new Mastra({
  agents: { penguin },
  legacy_workflows: { newWorkflow },
});

const { runId, start } = await mastra
  .legacy_getWorkflow("newWorkflow")
  .createRun();

const runResult = await start({
  triggerData: { message: "Give me a run down of the mission to save private" },
});

console.log(runResult.results);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/workflows-legacy/calling-agent-from-workflow"
  }
/>

## Workflows (Legacy)

The following links provide example documentation for legacy workflows:

- [Creating a Simple Workflow (Legacy)](/examples/workflows_legacy/creating-a-workflow)
- [Workflow (Legacy) with Sequential Steps](/examples/workflows_legacy/sequential-steps)
- [Parallel Execution with Steps](/examples/workflows_legacy/parallel-steps)
- [Branching Paths](/examples/workflows_legacy/branching-paths)
- [Workflow (Legacy) with Conditional Branching (experimental)](/examples/workflows_legacy/conditional-branching)
- [Tool as a Workflow step (Legacy)](/examples/workflows_legacy/using-a-tool-as-a-step)
- [Workflow (Legacy) with Cyclical dependencies](/examples/workflows_legacy/cyclical-dependencies)
- [Data Mapping with Workflow Variables (Legacy)](/examples/workflows_legacy/workflow-variables)
- [Human in the Loop Workflow (Legacy)](/examples/workflows_legacy/human-in-the-loop)
- [Workflow (Legacy) with Suspend and Resume](/examples/workflows_legacy/suspend-and-resume)
