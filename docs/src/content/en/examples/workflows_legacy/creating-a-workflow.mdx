---
title: "Example: Creating a Workflow | Workflows (Legacy) | Mastra Docs"
description: Example of using <PERSON><PERSON> to define and execute a simple workflow with a single step.
---

import { GithubLink } from "@/components/github-link";

# Creating a Simple Workflow (Legacy)

A workflow allows you to define and execute sequences of operations in a structured path. This example shows a legacy workflow with a single step.

```ts showLineNumbers copy
import { LegacyStep, LegacyWorkflow } from "@mastra/core/workflows/legacy";
import { z } from "zod";

const myWorkflow = new LegacyWorkflow({
  name: "my-workflow",
  triggerSchema: z.object({
    input: z.number(),
  }),
});

const stepOne = new LegacyStep({
  id: "stepOne",
  inputSchema: z.object({
    value: z.number(),
  }),
  outputSchema: z.object({
    doubledValue: z.number(),
  }),
  execute: async ({ context }) => {
    const doubledValue = context?.triggerData?.input * 2;
    return { doubledValue };
  },
});

myWorkflow.step(stepOne).commit();

const { runId, start } = myWorkflow.createRun();

const res = await start({
  triggerData: { input: 90 },
});

console.log(res.results);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/workflows-legacy/create-workflow"
  }
/>

## Workflows (Legacy)

The following links provide example documentation for legacy workflows:

- [Workflow (Legacy) with Sequential Steps](/examples/workflows_legacy/sequential-steps)
- [Parallel Execution with Steps](/examples/workflows_legacy/parallel-steps)
- [Branching Paths](/examples/workflows_legacy/branching-paths)
- [Workflow (Legacy) with Conditional Branching (experimental)](/examples/workflows_legacy/conditional-branching)
- [Calling an Agent From a Workflow (Legacy)](/examples/workflows_legacy/calling-agent)
- [Tool as a Workflow step (Legacy)](/examples/workflows_legacy/using-a-tool-as-a-step)
- [Workflow (Legacy) with Cyclical dependencies](/examples/workflows_legacy/cyclical-dependencies)
- [Data Mapping with Workflow Variables (Legacy)](/examples/workflows_legacy/workflow-variables)
- [Human in the Loop Workflow (Legacy)](/examples/workflows_legacy/human-in-the-loop)
- [Workflow (Legacy) with Suspend and Resume](/examples/workflows_legacy/suspend-and-resume)
