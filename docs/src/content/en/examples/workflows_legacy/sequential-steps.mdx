---
title: "Example: Sequential Steps | Workflows (Legacy) | Mastra Docs"
description: Example of using <PERSON><PERSON> to chain legacy workflow steps in a specific sequence, passing data between them.
---

import { GithubLink } from "@/components/github-link";

# Workflow (Legacy) with Sequential Steps

Workflow can be chained to run one after another in a specific sequence.

## Control Flow Diagram

This example shows how to chain workflow steps by using the `then` method demonstrating how to pass data between sequential steps and execute them in order.

Here's the control flow diagram:

<img
  src="/sequential-chains.png"
  alt="Diagram showing workflow with sequential steps"
  width={600}
/>

## Creating the Steps

Let's start by creating the steps and initializing the workflow.

```ts showLineNumbers copy
import { LegacyStep, LegacyWorkflow } from "@mastra/core/workflows/legacy";
import { z } from "zod";

const stepOne = new LegacyStep({
  id: "stepOne",
  execute: async ({ context }) => ({
    doubledValue: context.triggerData.inputValue * 2,
  }),
});

const stepTwo = new LegacyStep({
  id: "stepTwo",
  execute: async ({ context }) => {
    if (context.steps.stepOne.status !== "success") {
      return { incrementedValue: 0 };
    }

    return { incrementedValue: context.steps.stepOne.output.doubledValue + 1 };
  },
});

const stepThree = new LegacyStep({
  id: "stepThree",
  execute: async ({ context }) => {
    if (context.steps.stepTwo.status !== "success") {
      return { tripledValue: 0 };
    }

    return { tripledValue: context.steps.stepTwo.output.incrementedValue * 3 };
  },
});

// Build the workflow
const myWorkflow = new LegacyWorkflow({
  name: "my-workflow",
  triggerSchema: z.object({
    inputValue: z.number(),
  }),
});
```

## Chaining the Steps and Executing the Workflow

Now let's chain the steps together.

```ts showLineNumbers copy
// sequential steps
myWorkflow.step(stepOne).then(stepTwo).then(stepThree);

myWorkflow.commit();

const { start } = myWorkflow.createRun();

const res = await start({ triggerData: { inputValue: 90 } });
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/workflows-legacy/workflow-with-sequential-steps"
  }
/>

## Workflows (Legacy)

The following links provide example documentation for legacy workflows:

- [Creating a Simple Workflow (Legacy)](/examples/workflows_legacy/creating-a-workflow)
- [Parallel Execution with Steps](/examples/workflows_legacy/parallel-steps)
- [Branching Paths](/examples/workflows_legacy/branching-paths)
- [Workflow (Legacy) with Conditional Branching (experimental)](/examples/workflows_legacy/conditional-branching)
- [Calling an Agent From a Workflow (Legacy)](/examples/workflows_legacy/calling-agent)
- [Tool as a Workflow step (Legacy)](/examples/workflows_legacy/using-a-tool-as-a-step)
- [Workflow (Legacy) with Cyclical dependencies](/examples/workflows_legacy/cyclical-dependencies)
- [Data Mapping with Workflow Variables (Legacy)](/examples/workflows_legacy/workflow-variables)
- [Human in the Loop Workflow (Legacy)](/examples/workflows_legacy/human-in-the-loop)
- [Workflow (Legacy) with Suspend and Resume](/examples/workflows_legacy/suspend-and-resume)
