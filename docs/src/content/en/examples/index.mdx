---
title: "Examples List: Workflows, Agents, RAG | Mastra Docs"
description: "Explore practical examples of AI development with Mastra, including text generation, RAG implementations, structured outputs, and multi-modal interactions. Learn how to build AI applications using OpenAI, Anthropic, and Google Gemini."
---

import { CardItems } from "@/components/cards/card-items";
import { Tabs } from "nextra/components";

# Examples

The Examples section is a short list of example projects demonstrating basic AI engineering with Mastra, including text generation, structured output, streaming responses, retrieval‐augmented generation (RAG), and voice.

<CardItems
  titles={["Agent", "Workflow", "legacyWorkflow", "Memory", "RAG", "Evals", "Voice"]}
  items={{
    Agent: [
      { title: "Giving a System Prompt", href: "/examples/agents/system-prompt" },
      { title: "Workflow as Tools", href: "/examples/agents/workflow-as-tools" },
      { title: "Using a Tool", href: "/examples/agents/using-a-tool" },
      { title: "Hierarchical Multi-Agent System", href: "/examples/agents/hierarchical-multi-agent" },
      { title: "Multi-Agent Workflow", href: "/examples/agents/multi-agent-workflow" },
      { title: "Bird Checker", href: "/examples/agents/bird-checker" },
      { title: "Give your Agent a voice", href: "/examples/agents/adding-voice-capabilities" },
      { title: "Deploying an MCPServer", href: "/examples/agents/deploying-mcp-server" },
      { title: "Dynamic Agents", href: "/examples/agents/dynamic-agents" },
      { title: "AI SDK v5 Integration", href: "/examples/agents/ai-sdk-v5-integration" }
    ],
    Workflow: [
      { title: "Conditional Branching", href: "/examples/workflows/conditional-branching" },
      { title: "Parallel Steps", href: "/examples/workflows/parallel-steps" },
      { title: "Calling an Agent", href: "/examples/workflows/calling-agent" },
      { title: "Tool & Agent as a Step", href: "/examples/workflows/agent-and-tool-interop" },
      { title: "Human in the Loop", href: "/examples/workflows/human-in-the-loop" },
      { title: "Control Flow", href: "/examples/workflows/control-flow" },
      { title: "Array as Input", href: "/examples/workflows/array-as-input" },
      { title: "Inngest Workflow", href: "/examples/workflows/inngest-workflow" }
    ],
    legacyWorkflow: [
      { title: "Creating a Workflow", href: "/examples/workflows_legacy/creating-a-workflow" },
      { title: "Sequential Steps", href: "/examples/workflows_legacy/sequential-steps" },
      { title: "Parallel Steps", href: "/examples/workflows_legacy/parallel-steps" },
      { title: "Branching Paths", href: "/examples/workflows_legacy/branching-paths" },
      { title: "Conditional Branching", href: "/examples/workflows_legacy/conditional-branching" },
      { title: "Calling an Agent", href: "/examples/workflows_legacy/calling-agent" },
      { title: "Using a Tool as a Step", href: "/examples/workflows_legacy/using-a-tool-as-a-step" },
      { title: "Cyclical Dependencies", href: "/examples/workflows_legacy/cyclical-dependencies" },
      { title: "Workflow Variables", href: "/examples/workflows_legacy/workflow-variables" }
    ],
    Memory: [
      { title: "Memory with LibSQL", href: "/examples/memory/memory-with-libsql" },
      { title: "Memory with PostgreSQL", href: "/examples/memory/memory-with-pg" },
      { title: "Memory with Upstash", href: "/examples/memory/memory-with-upstash" },
      { title: "Memory with Mem0", href: "/examples/memory/memory-with-mem0" },
      { title: "Streaming Working Memory", href: "/examples/memory/streaming-working-memory" },
      { title: "Streaming Working Memory (Advanced)", href: "/examples/memory/streaming-working-memory-advanced" },
      { title: "Streaming Structured Working Memory", href: "/examples/memory/streaming-working-memory-structured" },
      { title: "Memory Processors", href: "/examples/memory/memory-processors" },
      { title: "AI SDK useChat Hook", href: "/examples/memory/use-chat" }
    ],
    RAG: [
      { title: "Chunk Text", href: "/examples/rag/chunking/chunk-text" },
      { title: "Chunk Markdown", href: "/examples/rag/chunking/chunk-markdown" },
      { title: "Chunk HTML", href: "/examples/rag/chunking/chunk-html" },
      { title: "Chunk JSON", href: "/examples/rag/chunking/chunk-json" },
      { title: "Adjust Chunk Size", href: "/examples/rag/chunking/adjust-chunk-size" },
      { title: "Adjust Chunk Delimiters", href: "/examples/rag/chunking/adjust-chunk-delimiters" },
      { title: "Embed Text Chunk", href: "/examples/rag/embedding/embed-text-chunk" },
      { title: "Embed Chunk Array", href: "/examples/rag/embedding/embed-chunk-array" },
      { title: "Embed Text with Cohere", href: "/examples/rag/embedding/embed-text-with-cohere" },
      { title: "Metadata Extraction", href: "/examples/rag/embedding/metadata-extraction" },
      { title: "Upsert Embeddings", href: "/examples/rag/upsert/upsert-embeddings" },
      { title: "Using the Vector Query Tool", href: "/examples/rag/usage/basic-rag" },
      { title: "Optimizing Information Density", href: "/examples/rag/usage/cleanup-rag" },
      { title: "Metadata Filtering", href: "/examples/rag/usage/filter-rag" },
      { title: "Chain of Thought Prompting", href: "/examples/rag/usage/cot-rag" },
      { title: "Structured Reasoning with Workflows", href: "/examples/rag/usage/cot-workflow-rag" },
      { title: "Graph RAG", href: "/examples/rag/usage/graph-rag" }
    ],
    Evals: [
      { title: "Answer Relevancy", href: "/examples/evals/answer-relevancy" },
      { title: "Bias", href: "/examples/evals/bias" },
      { title: "Completeness", href: "/examples/evals/completeness" },
      { title: "Content Similarity", href: "/examples/evals/content-similarity" },
      { title: "Context Position", href: "/examples/evals/context-position" },
      { title: "Context Precision", href: "/examples/evals/context-precision" },
      { title: "Context Relevancy", href: "/examples/evals/context-relevancy" },
      { title: "Contextual Recall", href: "/examples/evals/contextual-recall" },
      { title: "Custom Eval", href: "/examples/evals/custom-eval" },
      { title: "Faithfulness", href: "/examples/evals/faithfulness" },
      { title: "Hallucination", href: "/examples/evals/hallucination" },
      { title: "Keyword Coverage", href: "/examples/evals/keyword-coverage" },
      { title: "Prompt Alignment", href: "/examples/evals/prompt-alignment" },
      { title: "Summarization", href: "/examples/evals/summarization" },
      { title: "Textual Difference", href: "/examples/evals/textual-difference" },
      { title: "Tone Consistency", href: "/examples/evals/tone-consistency" },
      { title: "Toxicity", href: "/examples/evals/toxicity" },
      { title: "Word Inclusion", href: "/examples/evals/word-inclusion" }
    ],
    Voice: [
      { title: "Text to Speech", href: "/examples/voice/text-to-speech" },
      { title: "Speech to Text", href: "/examples/voice/speech-to-text" },
      { title: "Turn Taking", href: "/examples/voice/turn-taking" },
      { title: "Speech to Speech", href: "/examples/voice/speech-to-speech" }
    ]
  }}
/>
