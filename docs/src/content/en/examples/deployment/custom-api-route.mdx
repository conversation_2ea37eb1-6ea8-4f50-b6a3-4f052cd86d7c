---
title: Custom API Route
---

```typescript showLineNumbers
import { <PERSON><PERSON> } from "@mastra/core";
import { registerApiRoute } from "@mastra/core/server";

export const mastra = new Mastra({
  server: {
    apiRoutes: [
      registerApiRoute("/my-custom-route", {
        method: "GET",
        handler: async (c) => {
          const mastra = c.get("mastra");
          const agents = await mastra.getAgent("my-agent");
          return c.json({ message: "Hello, world!" });
        },
      }),
    ],
  },
});
```
