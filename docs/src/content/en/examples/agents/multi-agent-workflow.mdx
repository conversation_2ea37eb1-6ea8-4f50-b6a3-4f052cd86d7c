---
title: "Example: Multi-Agent Workflow | Agents | Mastra Docs"
description: Example of creating an agentic workflow in Mastra, where work product is passed between multiple agents.
---

import { GithubLink } from "@/components/github-link";

# Multi-Agent Workflow

This example demonstrates how to create an agentic workflow with work product being passed between multiple agents with a worker agent and a supervisor agent.

In this example, we create a sequential workflow that calls two agents in order:

1. A Copywriter agent that writes the initial blog post
2. An Editor agent that refines the content

First, import the required dependencies:

```typescript
import { openai } from "@ai-sdk/openai";
import { anthropic } from "@ai-sdk/anthropic";
import { Agent } from "@mastra/core/agent";
import { createStep, createWorkflow } from "@mastra/core/workflows";
import { z } from "zod";
```

Create the copywriter agent that will generate the initial blog post:

```typescript
const copywriterAgent = new Agent({
  name: "Copy<PERSON>",
  instructions: "You are a copywriter agent that writes blog post copy.",
  model: anthropic("claude-3-5-sonnet-20241022"),
});
```

Define the copywriter step that executes the agent and handles the response:

```typescript
const copywriterStep = createStep({
  id: "copywriterStep",
  inputSchema: z.object({
    topic: z.string(),
  }),
  outputSchema: z.object({
    copy: z.string(),
  }),
  execute: async ({ inputData }) => {
    if (!inputData?.topic) {
      throw new Error("Topic not found in trigger data");
    }
    const result = await copywriterAgent.generate(
      `Create a blog post about ${inputData.topic}`,
    );
    console.log("copywriter result", result.text);
    return {
      copy: result.text,
    };
  },
});
```

Set up the editor agent to refine the copywriter's content:

```typescript
const editorAgent = new Agent({
  name: "Editor",
  instructions: "You are an editor agent that edits blog post copy.",
  model: openai("gpt-4o-mini"),
});
```

Create the editor step that processes the copywriter's output:

```typescript
const editorStep = createStep({
  id: "editorStep",
  inputSchema: z.object({
    copy: z.string(),
  }),
  outputSchema: z.object({
    finalCopy: z.string(),
  }),
  execute: async ({ inputData }) => {
    const copy = inputData?.copy;

    const result = await editorAgent.generate(
      `Edit the following blog post only returning the edited copy: ${copy}`,
    );
    console.log("editor result", result.text);
    return {
      finalCopy: result.text,
    };
  },
});
```

Configure the workflow and execute the steps:

```typescript
const myWorkflow = createWorkflow({
  id: "my-workflow",
  inputSchema: z.object({
    topic: z.string(),
  }),
  outputSchema: z.object({
    finalCopy: z.string(),
  }),
});

// Run steps sequentially.
myWorkflow.then(copywriterStep).then(editorStep).commit();

const run = await myWorkflow.createRunAsync();

const res = await run.start({
  inputData: { topic: "React JavaScript frameworks" },
});
console.log("Response: ", res);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />

<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/agents/multi-agent-workflow"
  }
/>
