---
title: "Example: Deploying an MCPServer | Agents | Mastra Docs"
description: Example of setting up, building, and deploying a Mastra MCPServer using the stdio transport and publishing it to NPM.
---

import { GithubLink } from "@/components/github-link";

# Example: Deploying an MCPServer

This example guides you through setting up a basic Mastra MCPServer using the stdio transport, building it, and preparing it for deployment, such as publishing to NPM.

## Install Dependencies

Install the necessary packages:

```bash
pnpm add @mastra/mcp @mastra/core tsup
```

## Set up MCP Server

1.  Create a file for your stdio server, for example, `/src/mastra/stdio.ts`.

2.  Add the following code to the file. Remember to import your actual Mastra tools and name the server appropriately.

    ```typescript filename="src/mastra/stdio.ts" copy
    #!/usr/bin/env node
    import { MCPServer } from "@mastra/mcp";
    import { weatherTool } from "./tools";

    const server = new MCPServer({
      name: "my-mcp-server",
      version: "1.0.0",
      tools: { weatherTool },
    });

    server.startStdio().catch((error) => {
      console.error("Error running MCP server:", error);
      process.exit(1);
    });
    ```

3.  Update your `package.json` to include the `bin` entry pointing to your built server file and a script to build the server.

```json filename="package.json" copy
{
  "bin": "dist/stdio.js",
  "scripts": {
    "build:mcp": "tsup src/mastra/stdio.ts --format esm --no-splitting --dts && chmod +x dist/stdio.js"
  }
}
```

4.  Run the build command:

    ```bash
    pnpm run build:mcp
    ```

    This will compile your server code and make the output file executable.

## Deploying to NPM

To make your MCP server available for others (or yourself) to use via `npx` or as a dependency, you can publish it to NPM.

1.  Ensure you have an NPM account and are logged in (`npm login`).
2.  Make sure your package name in `package.json` is unique and available.
3.  Run the publish command from your project root after building:

    ```bash
    npm publish --access public
    ```

    For more details on publishing packages, refer to the [NPM documentation](https://docs.npmjs.com/creating-and-publishing-scoped-public-packages).

## Use the Deployed MCP Server

Once published, your MCP server can be used by an `MCPClient` by specifying the command to run your package. You can also use any other MCP client like Claude desktop, Cursor, or Windsurf.

```typescript
import { MCPClient } from "@mastra/mcp";

const mcp = new MCPClient({
  servers: {
    // Give this MCP server instance a name
    yourServerName: {
      command: "npx",
      args: ["-y", "@your-org-name/your-package-name@latest"], // Replace with your package name
    },
  },
});

// You can then get tools or toolsets from this configuration to use in your agent
const tools = await mcp.getTools();
const toolsets = await mcp.getToolsets();
```

Note: If you published without an organization scope, the `args` might just be `["-y", "your-package-name@latest"]`.

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
