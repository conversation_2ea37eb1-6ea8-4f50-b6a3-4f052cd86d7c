---
title: "概要"
description: "Mastraを使った構築ガイド"
---

# ガイド

例では迅速な実装を示し、ドキュメントでは特定の機能を説明していますが、これらのガイドはやや長めで、Mastraのコアコンセプトを実証するように設計されています：

## [AI リクルーター](/guides/guide/ai-recruiter)

候補者の履歴書を処理し、面接を実施するワークフローを作成し、Mastraワークフローにおける分岐ロジックとLLM統合を実証します。

## [シェフアシスタント](/guides/guide/chef-michel)

利用可能な食材で料理を作るのを手伝うAIシェフエージェントを構築し、カスタムツールを使用してインタラクティブなエージェントを作成する方法を紹介します。

## [研究論文アシスタント](/guides/guide/research-assistant)

検索拡張生成（RAG）を使用して学術論文を分析するAI研究アシスタントを開発し、文書処理と質問応答を実証します。

## [株価エージェント](/guides/guide/stock-agent)

株価を取得するシンプルなエージェントを実装し、ツールの作成とMastraエージェントへの統合の基本を説明します。
