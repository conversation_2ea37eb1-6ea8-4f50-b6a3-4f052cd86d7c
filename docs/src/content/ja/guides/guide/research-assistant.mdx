---
title: "リサーチペーパーアシスタントの構築 | Mastra RAG ガイド"
description: RAG を活用して学術論文を分析し、質問に答える AI リサーチアシスタントの作成ガイド。
---

import { Steps } from "nextra/components";

# RAGを使ったリサーチペーパーアシスタントの構築

このガイドでは、Retrieval Augmented Generation（RAG）を活用して、学術論文を分析し、その内容に関する具体的な質問に答えるAIリサーチアシスタントを作成します。

例として、基礎的なTransformer論文である[Attention Is All You Need](https://arxiv.org/html/1706.03762)を使用します。

## RAGコンポーネントの理解

RAGがどのように機能するか、そして各コンポーネントをどのように実装するかを理解しましょう。

1. ナレッジストア／インデックス

   - テキストをベクトル表現に変換する
   - コンテンツの数値表現を作成する
   - 実装方法：OpenAIのtext-embedding-3-smallを使って埋め込みを作成し、PgVectorに保存します

2. リトリーバー

   - 類似度検索によって関連するコンテンツを見つける
   - クエリの埋め込みと保存されたベクトルをマッチングする
   - 実装方法：PgVectorを使って保存された埋め込みに対して類似度検索を行います

3. ジェネレーター
   - 取得したコンテンツをLLMで処理する
   - 文脈に基づいた応答を生成する
   - 実装方法：GPT-4o-miniを使い、取得したコンテンツに基づいて回答を生成します

私たちの実装では、以下のことを行います：

1. Transformer論文を埋め込みに変換する
2. それらをPgVectorに保存し、素早く取得できるようにする
3. 類似度検索を使って関連するセクションを見つける
4. 取得した文脈を使って正確な応答を生成する

## プロジェクト構成

```
research-assistant/
├── src/
│   ├── mastra/
│   │   ├── agents/
│   │   │   └── researchAgent.ts
│   │   └── index.ts
│   ├── index.ts
│   └── store.ts
├── package.json
└── .env
```

<Steps>
  ### プロジェクトの初期化と依存関係のインストール

まず、プロジェクト用の新しいディレクトリを作成し、その中に移動します。

```bash
mkdir research-assistant
cd research-assistant
```

新しい Node.js プロジェクトを初期化し、必要な依存関係をインストールします。

```bash copy
npm init -y
npm install @mastra/core@latest @mastra/rag@latest @mastra/pg@latest @ai-sdk/openai@latest ai@latest zod@latest
```

APIアクセスやデータベース接続のための環境変数を設定します。

```bash filename=".env" copy
OPENAI_API_KEY=your_openai_api_key
POSTGRES_CONNECTION_STRING=your_connection_string
```

プロジェクトに必要なファイルを作成します。

```bash copy
mkdir -p src/mastra/agents
touch src/mastra/agents/researchAgent.ts
touch src/mastra/index.ts src/store.ts src/index.ts
```

### Research Assistant エージェントの作成

次に、RAG対応のリサーチアシスタントを作成します。このエージェントは以下を利用します。

- [Vector Query Tool](/reference/tools/vector-query-tool) を使い、ベクトルストア上でセマンティック検索を行い、論文内の関連コンテンツを見つけます。
- GPT-4o-mini を用いてクエリの理解と応答の生成を行います
- 論文の分析方法、取得したコンテンツの効果的な活用方法、制限事項の認識についてエージェントを導くカスタムインストラクション

```ts copy showLineNumbers filename="src/mastra/agents/researchAgent.ts"
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { createVectorQueryTool } from "@mastra/rag";

// Create a tool for semantic search over our paper embeddings
const vectorQueryTool = createVectorQueryTool({
  vectorStoreName: "pgVector",
  indexName: "papers",
  model: openai.embedding("text-embedding-3-small"),
});

export const researchAgent = new Agent({
  name: "Research Assistant",
  instructions: `You are a helpful research assistant that analyzes academic papers and technical documents.
    Use the provided vector query tool to find relevant information from your knowledge base, 
    and provide accurate, well-supported answers based on the retrieved content.
    Focus on the specific content available in the tool and acknowledge if you cannot find sufficient information to answer a question.
    Base your responses only on the content provided, not on general knowledge.`,
  model: openai("gpt-4o-mini"),
  tools: {
    vectorQueryTool,
  },
});
```

### Mastra インスタンスとベクトルストアのセットアップ

```ts copy showLineNumbers filename="src/mastra/index.ts"
import { Mastra } from "@mastra/core";
import { PgVector } from "@mastra/pg";

import { researchAgent } from "./agents/researchAgent";

// Initialize Mastra instance
const pgVector = new PgVector({ connectionString: process.env.POSTGRES_CONNECTION_STRING! });
export const mastra = new Mastra({
  agents: { researchAgent },
  vectors: { pgVector },
});
```

### 論文の読み込みと処理

このステップでは、初期ドキュメント処理を行います。具体的には：

1. 論文をURLから取得
2. ドキュメントオブジェクトに変換
3. より良い処理のために小さなチャンクに分割

```ts copy showLineNumbers filename="src/store.ts"
import { openai } from "@ai-sdk/openai";
import { MDocument } from "@mastra/rag";
import { embedMany } from "ai";
import { mastra } from "./mastra";

// Load the paper
const paperUrl = "https://arxiv.org/html/1706.03762";
const response = await fetch(paperUrl);
const paperText = await response.text();

// Create document and chunk it
const doc = MDocument.fromText(paperText);
const chunks = await doc.chunk({
  strategy: "recursive",
  size: 512,
  overlap: 50,
  separator: "\n",
});

console.log("Number of chunks:", chunks.length);
// Number of chunks: 893
```

### 埋め込みの作成と保存

最後に、RAG用にコンテンツを準備します。手順は以下の通りです。

1. 各テキストチャンクの埋め込みを生成
2. 埋め込みを格納するベクトルストアインデックスを作成
3. 埋め込みとメタデータ（元のテキストやソース情報）をベクトルデータベースに保存

> **注**: このメタデータは、ベクトルストアが関連する一致を見つけた際に、実際のコンテンツを返すために重要です。

これにより、エージェントは効率的に関連情報を検索・取得できるようになります。

```ts copy showLineNumbers{23} filename="src/store.ts"
// Generate embeddings
const { embeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: chunks.map((chunk) => chunk.text),
});

// Get the vector store instance from Mastra
const vectorStore = mastra.getVector("pgVector");

// Create an index for our paper chunks
await vectorStore.createIndex({
  indexName: "papers",
  dimension: 1536,
});

// Store embeddings
await vectorStore.upsert({
  indexName: "papers",
  vectors: embeddings,
  metadata: chunks.map((chunk) => ({
    text: chunk.text,
    source: "transformer-paper",
  })),
});
```

これにより、以下の処理が行われます：

1. 論文をURLから読み込みます
2. 論文を扱いやすいチャンクに分割します
3. 各チャンクの埋め込みを生成します
4. 埋め込みとテキストの両方をベクトルデータベースに保存します

スクリプトを実行して埋め込みを保存するには、以下を実行します：

```bash
npx bun src/store.ts
```

### アシスタントをテストする

リサーチアシスタントをさまざまなクエリでテストしてみましょう：

```ts filename="src/index.ts" showLineNumbers copy
import { mastra } from "./mastra";
const agent = mastra.getAgent("researchAgent");

// Basic query about concepts
const query1 =
  "What problems does sequence modeling face with neural networks?";
const response1 = await agent.generate(query1);
console.log("\nQuery:", query1);
console.log("Response:", response1.text);
```

スクリプトを実行します：

```bash copy
npx bun src/index.ts
```

次のような出力が表示されるはずです：

```
Query: What problems does sequence modeling face with neural networks?
Response: Sequence modeling with neural networks faces several key challenges:
1. Vanishing and exploding gradients during training, especially with long sequences
2. Difficulty handling long-term dependencies in the input
3. Limited computational efficiency due to sequential processing
4. Challenges in parallelizing computations, resulting in longer training times
```

別の質問も試してみましょう：

```ts filename="src/index.ts" showLineNumbers{10} copy
// Query about specific findings
const query2 = "What improvements were achieved in translation quality?";
const response2 = await agent.generate(query2);
console.log("\nQuery:", query2);
console.log("Response:", response2.text);
```

出力例：

```
Query: What improvements were achieved in translation quality?
Response: The model showed significant improvements in translation quality, achieving more than 2.0
BLEU points improvement over previously reported models on the WMT 2014 English-to-German translation
task, while also reducing training costs.
```

### アプリケーションを公開する

Mastraサーバーを起動して、リサーチアシスタントをAPI経由で利用できるようにします：

```bash
mastra dev
```

リサーチアシスタントは以下のURLで利用可能になります：

```
http://localhost:4111/api/agents/researchAgent/generate
```

curlでテストするには：

```bash
curl -X POST http://localhost:4111/api/agents/researchAgent/generate \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      { "role": "user", "content": "What were the main findings about model parallelization?" }
    ]
  }'
```

</Steps>

## 高度なRAGの例

より高度なRAG技術の例を以下でご覧ください：

- メタデータを使って結果をフィルタリングする[Filter RAG](/examples/rag/usage/filter-rag)
- 情報密度を最適化する[Cleanup RAG](/examples/rag/usage/cleanup-rag)
- ワークフローを用いた複雑な推論クエリのための[Chain of Thought RAG](/examples/rag/usage/cot-rag)
- 結果の関連性を向上させる[Rerank RAG](/examples/rag/usage/rerank-rag)
