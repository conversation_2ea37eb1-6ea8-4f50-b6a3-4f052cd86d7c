---
title: "例: Cohereによるリランキング | RAG | Mastra ドキュメント"
description: Mastraを使用してCohereのリランキングサービスでドキュメント検索の関連性を向上させる例。
---

# Cohereによるリランキング

RAGのためにドキュメントを検索する際、初期のベクトル類似度検索では重要なセマンティックな一致を見逃すことがあります。

Cohereのリランキングサービスは、複数のスコアリング要素を用いてドキュメントの順序を並べ替えることで、結果の関連性を向上させます。

```typescript
import { rerank } from "@mastra/rag";

const results = rerank(
  searchResults,
  "deployment configuration",
  cohere("rerank-v3.5"),
  {
    topK: 5,
    weights: {
      semantic: 0.4,
      vector: 0.4,
      position: 0.2,
    },
  },
);
```

## リンク

- [rerank() リファレンス](/reference/rag/rerank.mdx)
- [リトリーバル ドキュメント](/reference/rag/retrieval.mdx)
