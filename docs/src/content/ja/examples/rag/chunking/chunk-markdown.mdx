---
title: "例：マークダウンのセマンティックチャンキング | RAG | Mastra ドキュメント"
description: 検索や取得目的でマークダウン文書をチャンク化するためのMastraの使用例。
---

import { GithubLink } from "@/components/github-link";

# チャンクマークダウン

Markdownは生のHTMLよりも情報密度が高く、RAGパイプラインでの作業が容易です。Markdownを扱う際には、ヘッダーやフォーマットを保持しながら小さな部分に分割する必要があります。`chunk`メソッドは、ヘッダー、リスト、コードブロックのようなMarkdown特有の要素を賢く処理します。この例は、検索や取得の目的でMarkdownドキュメントをチャンクする方法を示しています。

```tsx copy
import { MDocument } from "@mastra/rag";

const doc = MDocument.fromMarkdown("# Your markdown content...");

const chunks = await doc.chunk();
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/chunk-markdown"
  }
/>
