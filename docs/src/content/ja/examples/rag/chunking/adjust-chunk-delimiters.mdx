---
title: "例：チャンクデリミタの調整 | RAG | Mastra ドキュメント"
description: Mastraでチャンクデリミタを調整して、コンテンツ構造により適合させる方法。
---

import { GithubLink } from "@/components/github-link";

# チャンク区切りを調整する

大きなドキュメントを処理する際、テキストを小さなチャンクに分割する方法を制御したい場合があります。デフォルトでは、ドキュメントは改行で分割されますが、この動作をカスタマイズしてコンテンツ構造により適合させることができます。この例では、ドキュメントをチャンク化するためのカスタム区切り文字を指定する方法を示します。

```tsx copy
import { MDocument } from "@mastra/rag";

const doc = MDocument.fromText("Your plain text content...");

const chunks = await doc.chunk({
  separator: "\n",
});
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/adjust-chunk-delimiters"
  }
/>
