---
title: "例: ハイブリッドベクトル検索 | RAG | Mastra ドキュメント"
description: PGVector でメタデータフィルターを使用し、Mastra のベクトル検索結果を強化する例。
---

import { GithubLink } from "@/components/github-link";

# ハイブリッドベクター検索

ベクター類似性検索とメタデータフィルターを組み合わせることで、より精度が高く効率的なハイブリッド検索を実現できます。
このアプローチは以下を組み合わせています：

- 最も関連性の高いドキュメントを見つけるためのベクター類似性検索
- 追加の条件に基づいて検索結果を絞り込むためのメタデータフィルター

この例では、Mastra と PGVector を使ったハイブリッドベクター検索の方法を示します。

## 概要

このシステムは、Mastra と PGVector を使用したフィルタ付きベクトル検索を実装しています。主な機能は以下の通りです。

1. PGVector に保存されている既存の埋め込みをメタデータフィルターで検索します
2. 異なるメタデータフィールドでのフィルタリング方法を示します
3. ベクトル類似度とメタデータフィルタリングの組み合わせを実演します

> **注意**: ドキュメントからメタデータを抽出する方法の例については、[Metadata Extraction](../embedding/metadata-extraction.mdx) ガイドをご覧ください。
>
> 埋め込みの作成と保存方法については、[Upsert Embeddings](/examples/rag/upsert/upsert-embeddings) ガイドをご参照ください。

## セットアップ

### 環境設定

環境変数を必ず設定してください：

```bash filename=".env"
OPENAI_API_KEY=your_openai_api_key_here
POSTGRES_CONNECTION_STRING=your_connection_string_here
```

### 依存関係

必要な依存関係をインポートします：

```typescript copy showLineNumbers filename="src/index.ts"
import { embed } from "ai";
import { PgVector } from "@mastra/pg";
import { openai } from "@ai-sdk/openai";
```

## ベクターストアの初期化

接続文字列を使ってPgVectorを初期化します：

```typescript copy showLineNumbers{4} filename="src/index.ts"
const pgVector = new PgVector({ connectionString: process.env.POSTGRES_CONNECTION_STRING! });
```

## 使用例

### メタデータ値でフィルタリング

```typescript copy showLineNumbers{6} filename="src/index.ts"
// Create embedding for the query
const { embedding } = await embed({
  model: openai.embedding("text-embedding-3-small"),
  value: "[Insert query based on document here]",
});

// Query with metadata filter
const result = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 3,
  filter: {
    "path.to.metadata": {
      $eq: "value",
    },
  },
});

console.log("Results:", result);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/hybrid-vector-search"
  }
/>
