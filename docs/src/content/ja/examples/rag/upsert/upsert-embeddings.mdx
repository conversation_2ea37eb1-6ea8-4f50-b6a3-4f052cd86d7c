---
title: "例: 埋め込みのアップサート | RAG | Mastra ドキュメント"
description: 類似性検索のために、Mastra を使ってさまざまなベクトルデータベースに埋め込みを保存する例。
---

import { Tabs } from "nextra/components";
import { GithubLink } from "@/components/github-link";

# 埋め込みのアップサート

埋め込みを生成した後、それらをベクトル類似検索をサポートするデータベースに保存する必要があります。この例では、後で検索できるように、さまざまなベクトルデータベースに埋め込みを保存する方法を示します。

<Tabs items={['PgVector', 'Pinecone', 'Qdrant', 'Chroma', 'Astra DB', 'LibSQL', 'Upstash', 'Cloudflare', 'MongoDB']}>
  <Tabs.Tab>
    `PgVector` クラスは、pgvector拡張機能を持つPostgreSQLにインデックスを作成し、埋め込みを挿入するためのメソッドを提供します。

    ```tsx copy
    import { openai } from "@ai-sdk/openai";
    import { PgVector } from "@mastra/pg";
    import { MDocument } from "@mastra/rag";
    import { embedMany } from "ai";

    const doc = MDocument.fromText("Your text content...");

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map(chunk => chunk.text),
      model: openai.embedding("text-embedding-3-small"),
    });

    const pgVector = new PgVector({ connectionString: process.env.POSTGRES_CONNECTION_STRING! });

    await pgVector.createIndex({
      indexName: "test_index",
      dimension: 1536,
    });

    await pgVector.upsert({
      indexName: "test_index",
      vectors: embeddings,
      metadata: chunks?.map((chunk: any) => ({ text: chunk.text })),
    });
    ```

    <br />

    <hr className="dark:border-[#404040] border-gray-300" />

    <br />

    <GithubLink
      link={
      "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/insert-embedding-in-pgvector"
    }
    />

  </Tabs.Tab>

  <Tabs.Tab>
    `PineconeVector` クラスは、Pinecone（マネージド型ベクトルデータベースサービス）にインデックスを作成し、埋め込みを挿入するためのメソッドを提供します。

    ```tsx copy
    import { openai } from '@ai-sdk/openai';
    import { PineconeVector } from '@mastra/pinecone';
    import { MDocument } from '@mastra/rag';
    import { embedMany } from 'ai';

    const doc = MDocument.fromText('Your text content...');

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map(chunk => chunk.text),
      model: openai.embedding('text-embedding-3-small'),
    });

    const pinecone = new PineconeVector({
      apiKey: process.env.PINECONE_API_KEY!,
    });

    await pinecone.createIndex({
      indexName: 'testindex',
      dimension: 1536,
    });

    await pinecone.upsert({
      indexName: 'testindex',
      vectors: embeddings,
      metadata: chunks?.map(chunk => ({ text: chunk.text })),
    });
    ```

    <br />

    <hr className="dark:border-[#404040] border-gray-300" />

    <br />

    <GithubLink link={'https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/insert-embedding-in-pinecone'} />

  </Tabs.Tab>

  <Tabs.Tab>
    `QdrantVector` クラスは、高性能ベクトルデータベースであるQdrantにコレクションを作成し、埋め込みを挿入するためのメソッドを提供します。

    ```tsx copy
    import { openai } from '@ai-sdk/openai';
    import { QdrantVector } from '@mastra/qdrant';
    import { MDocument } from '@mastra/rag';
    import { embedMany } from 'ai';

    const doc = MDocument.fromText('Your text content...');

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map(chunk => chunk.text),
      model: openai.embedding('text-embedding-3-small'),
      maxRetries: 3,
    });

    const qdrant = new QdrantVector({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    await qdrant.createIndex({
      indexName: 'test_collection',
      dimension: 1536,
    });

    await qdrant.upsert({
      indexName: 'test_collection',
      vectors: embeddings,
      metadata: chunks?.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    `ChromaVector` クラスは、コレクションの作成や Chroma（オープンソースの埋め込みデータベース）への埋め込みの挿入を行うためのメソッドを提供します。

    ```tsx copy
    import { openai } from '@ai-sdk/openai';
    import { ChromaVector } from '@mastra/chroma';
    import { MDocument } from '@mastra/rag';
    import { embedMany } from 'ai';

    const doc = MDocument.fromText('Your text content...');

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map(chunk => chunk.text),
      model: openai.embedding('text-embedding-3-small'),
    });

    const chroma = new ChromaVector({
      path: "path/to/chroma/db",
    });

    await chroma.createIndex({
      indexName: 'test_collection',
      dimension: 1536,
    });

    await chroma.upsert({
      indexName: 'test_collection',
      vectors: embeddings,
      metadata: chunks.map(chunk => ({ text: chunk.text })),
      documents: chunks.map(chunk => chunk.text),
    });
    ```

    <br />

    <hr className="dark:border-[#404040] border-gray-300" />

    <br />

    <GithubLink link={'https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/insert-embedding-in-chroma'} />

  </Tabs.Tab>

  <Tabs.Tab>
    `AstraVector` クラスは、コレクションの作成や DataStax Astra DB（クラウドネイティブなベクターデータベース）への埋め込みの挿入を行うためのメソッドを提供します。

    ```tsx copy
    import { openai } from '@ai-sdk/openai';
    import { AstraVector } from '@mastra/astra';
    import { MDocument } from '@mastra/rag';
    import { embedMany } from 'ai';

    const doc = MDocument.fromText('Your text content...');

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      model: openai.embedding('text-embedding-3-small'),
      values: chunks.map(chunk => chunk.text),
    });

    const astra = new AstraVector({
      token: process.env.ASTRA_DB_TOKEN,
      endpoint: process.env.ASTRA_DB_ENDPOINT,
      keyspace: process.env.ASTRA_DB_KEYSPACE,
    });

    await astra.createIndex({
      indexName: 'test_collection',
      dimension: 1536,
    });

    await astra.upsert({
      indexName: 'test_collection',
      vectors: embeddings,
      metadata: chunks?.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    `LibSQLVector` クラスは、コレクションの作成や LibSQL（ベクター拡張機能を持つ SQLite のフォーク）への埋め込みの挿入を行うためのメソッドを提供します。

    ```tsx copy
    import { openai } from "@ai-sdk/openai";
    import { LibSQLVector } from "@mastra/core/vector/libsql";
    import { MDocument } from "@mastra/rag";
    import { embedMany } from "ai";

    const doc = MDocument.fromText("Your text content...");

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map((chunk) => chunk.text),
      model: openai.embedding("text-embedding-3-small"),
    });

    const libsql = new LibSQLVector({
      connectionUrl: process.env.DATABASE_URL,
      authToken: process.env.DATABASE_AUTH_TOKEN, // Optional: for Turso cloud databases
    });

    await libsql.createIndex({
      indexName: "test_collection",
      dimension: 1536,
    });

    await libsql.upsert({
      indexName: "test_collection",
      vectors: embeddings,
      metadata: chunks?.map((chunk) => ({ text: chunk.text })),
    });
    ```

    <br />

    <hr className="dark:border-[#404040] border-gray-300" />

    <br />

    <GithubLink link={'https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/insert-embedding-in-libsql'} />

  </Tabs.Tab>

  <Tabs.Tab>
    `UpstashVector` クラスは、コレクションの作成や埋め込みを Upstash Vector（サーバーレスベクターデータベース）に挿入するためのメソッドを提供します。

    ```tsx copy
    import { openai } from '@ai-sdk/openai';
    import { UpstashVector } from '@mastra/upstash';
    import { MDocument } from '@mastra/rag';
    import { embedMany } from 'ai';

    const doc = MDocument.fromText('Your text content...');

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map(chunk => chunk.text),
      model: openai.embedding('text-embedding-3-small'),
    });

    const upstash = new UpstashVector({
      url: process.env.UPSTASH_URL,
      token: process.env.UPSTASH_TOKEN,
    });

    await upstash.createIndex({
      indexName: 'test_collection',
      dimension: 1536,
    });

    await upstash.upsert({
      indexName: 'test_collection',
      vectors: embeddings,
      metadata: chunks?.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    `CloudflareVector` クラスは、コレクションの作成や埋め込みを Cloudflare Vectorize（サーバーレスベクターデータベースサービス）に挿入するためのメソッドを提供します。

    ```tsx copy
    import { openai } from '@ai-sdk/openai';
    import { CloudflareVector } from '@mastra/vectorize';
    import { MDocument } from '@mastra/rag';
    import { embedMany } from 'ai';

    const doc = MDocument.fromText('Your text content...');

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map(chunk => chunk.text),
      model: openai.embedding('text-embedding-3-small'),
    });

    const vectorize = new CloudflareVector({
      accountId: process.env.CF_ACCOUNT_ID,
      apiToken: process.env.CF_API_TOKEN,
    });

    await vectorize.createIndex({
      indexName: 'test_collection',
      dimension: 1536,
    });

    await vectorize.upsert({
      indexName: 'test_collection',
      vectors: embeddings,
      metadata: chunks?.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    `MongoDBVector` クラスは、インデックスの作成や埋め込みを MongoDB（Atlas Search を利用）に挿入するためのメソッドを提供します。

    ```tsx copy
    import { openai } from "@ai-sdk/openai";
    import { MongoDBVector } from "@mastra/mongodb";
    import { MDocument } from "@mastra/rag";
    import { embedMany } from "ai";

    const doc = MDocument.fromText("Your text content...");

    const chunks = await doc.chunk();

    const { embeddings } = await embedMany({
      values: chunks.map(chunk => chunk.text),
      model: openai.embedding("text-embedding-3-small"),
    });

    const vectorDB = new MongoDBVector({
      uri: process.env.MONGODB_URI!,
      dbName: process.env.MONGODB_DB_NAME!,
    });

    await vectorDB.createIndex({
      indexName: "test_index",
      dimension: 1536,
    });

    await vectorDB.upsert({
      indexName: "test_index",
      vectors: embeddings,
      metadata: chunks?.map((chunk: any) => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>
</Tabs>
