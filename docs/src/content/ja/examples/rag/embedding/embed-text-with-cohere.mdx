---
title: "例: Cohere を使ったテキスト埋め込み | RAG | Mastra ドキュメント"
description: Mastra を使って Cohere の埋め込みモデルで埋め込みを生成する例。
---

import { GithubLink } from "@/components/github-link";

# Cohereでテキストを埋め込む

他の埋め込みプロバイダーを利用する場合、選択したモデルの仕様に合ったベクトルを生成する方法が必要です。`embed`メソッドは複数のプロバイダーをサポートしており、さまざまな埋め込みサービスを切り替えて利用できます。この例では、Cohereの埋め込みモデルを使って埋め込みを生成する方法を示します。

```tsx copy
import { cohere } from "@ai-sdk/cohere";
import { MDocument } from "@mastra/rag";
import { embedMany } from "ai";

const doc = MDocument.fromText("Your text content...");

const chunks = await doc.chunk();

const { embeddings } = await embedMany({
  model: cohere.embedding("embed-english-v3.0"),
  values: chunks.map((chunk) => chunk.text),
});
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/embed-text-with-cohere"
  }
/>
