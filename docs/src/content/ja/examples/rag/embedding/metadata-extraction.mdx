---
title: "例: メタデータ抽出 | 検索 | RAG | Mastra ドキュメント"
description: Mastra でドキュメントからメタデータを抽出し、強化されたドキュメント処理と検索に活用する例。
---

import { GithubLink } from "@/components/github-link";

# メタデータ抽出

この例では、Mastra のドキュメント処理機能を使ってドキュメントからメタデータを抽出し、活用する方法を示します。
抽出されたメタデータは、ドキュメントの整理、フィルタリング、RAG システムでの高度な検索に利用できます。

## 概要

このシステムは、2つの方法でメタデータ抽出を実演します。

1. ドキュメントからの直接的なメタデータ抽出
2. チャンク分割とメタデータ抽出

## セットアップ

### 依存関係

必要な依存関係をインポートします：

```typescript copy showLineNumbers filename="src/index.ts"
import { MDocument } from "@mastra/rag";
```

## ドキュメント作成

テキストコンテンツからドキュメントを作成します：

```typescript copy showLineNumbers{3} filename="src/index.ts"
const doc = MDocument.fromText(`Title: The Benefits of Regular Exercise

Regular exercise has numerous health benefits. It improves cardiovascular health, 
strengthens muscles, and boosts mental wellbeing.

Key Benefits:
• Reduces stress and anxiety
• Improves sleep quality
• Helps maintain healthy weight
• Increases energy levels

For optimal results, experts recommend at least 150 minutes of moderate exercise 
per week.`);
```

## 1. 直接メタデータ抽出

ドキュメントから直接メタデータを抽出します：

```typescript copy showLineNumbers{17} filename="src/index.ts"
// Configure metadata extraction options
await doc.extractMetadata({
  keywords: true, // Extract important keywords
  summary: true, // Generate a concise summary
});

// Retrieve the extracted metadata
const meta = doc.getMetadata();
console.log("Extracted Metadata:", meta);

// Example Output:
// Extracted Metadata: {
//   keywords: [
//     'exercise',
//     'health benefits',
//     'cardiovascular health',
//     'mental wellbeing',
//     'stress reduction',
//     'sleep quality'
//   ],
//   summary: 'Regular exercise provides multiple health benefits including improved cardiovascular health, muscle strength, and mental wellbeing. Key benefits include stress reduction, better sleep, weight management, and increased energy. Recommended exercise duration is 150 minutes per week.'
// }
```

## 2. メタデータ付きチャンク化

ドキュメントのチャンク化とメタデータ抽出を組み合わせます：

```typescript copy showLineNumbers{40} filename="src/index.ts"
// Configure chunking with metadata extraction
await doc.chunk({
  strategy: "recursive", // Use recursive chunking strategy
  size: 200, // Maximum chunk size
  extract: {
    keywords: true, // Extract keywords per chunk
    summary: true, // Generate summary per chunk
  },
});

// Get metadata from chunks
const metaTwo = doc.getMetadata();
console.log("Chunk Metadata:", metaTwo);

// Example Output:
// Chunk Metadata: {
//   keywords: [
//     'exercise',
//     'health benefits',
//     'cardiovascular health',
//     'mental wellbeing',
//     'stress reduction',
//     'sleep quality'
//   ],
//   summary: 'Regular exercise provides multiple health benefits including improved cardiovascular health, muscle strength, and mental wellbeing. Key benefits include stress reduction, better sleep, weight management, and increased energy. Recommended exercise duration is 150 minutes per week.'
// }
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/metadata-extraction"
  }
/>
