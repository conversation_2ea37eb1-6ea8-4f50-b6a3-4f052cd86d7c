---
title: 音声から音声へ
description: Mastra を使って音声から音声へのアプリケーションを作成する例。
---

import { GithubLink } from "@/components/github-link";

# Mastraによる通話分析

このガイドでは、Mastraを使用して分析機能付きの音声会話システムを構築する方法を説明します。例として、リアルタイムの音声対話、録音管理、そして通話分析のためのRoark Analyticsとの連携が含まれています。

## 概要

このシステムは、Mastraエージェントとの音声会話を作成し、やり取り全体を録音して、その録音をCloudinaryにアップロードして保存します。その後、会話データをRoark Analyticsに送信し、詳細な通話分析を行います。

## セットアップ

### 前提条件

1. 音声認識および音声合成機能のためのOpenAI APIキー
2. 音声ファイル保存用のCloudinaryアカウント
3. 通話分析のためのRoark Analytics APIキー

### 環境構成

提供されたサンプルに基づいて `.env` ファイルを作成します：

```bash filename="speech-to-speech/call-analysis/sample.env" copy
OPENAI_API_KEY=
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=
ROARK_API_KEY=
```

### インストール

必要な依存関係をインストールします：

```bash copy
npm install
```

## 実装

### Mastraエージェントの作成

まず、音声機能を持つエージェントを定義します。

```ts filename="speech-to-speech/call-analysis/src/mastra/agents/index.ts" copy
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { createTool } from "@mastra/core/tools";
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { z } from "zod";

// Have the agent do something
export const speechToSpeechServer = new Agent({
  name: "mastra",
  instructions: "You are a helpful assistant.",
  voice: new OpenAIRealtimeVoice(),
  model: openai("gpt-4o"),
  tools: {
    salutationTool: createTool({
      id: "salutationTool",
      description: "Read the result of the tool",
      inputSchema: z.object({ name: z.string() }),
      outputSchema: z.object({ message: z.string() }),
      execute: async ({ context }) => {
        return { message: `Hello ${context.name}!` };
      },
    }),
  },
});
```

### Mastraの初期化

エージェントをMastraに登録します。

```ts filename="speech-to-speech/call-analysis/src/mastra/index.ts" copy
import { Mastra } from "@mastra/core";
import { speechToSpeechServer } from "./agents";

export const mastra = new Mastra({
  agents: {
    speechToSpeechServer,
  },
});
```

### 音声ファイル保存のためのCloudinary連携

録音した音声ファイルを保存するためにCloudinaryを設定します。

```ts filename="speech-to-speech/call-analysis/src/upload.ts" copy
import { v2 as cloudinary } from "cloudinary";

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export async function uploadToCloudinary(path: string) {
  const response = await cloudinary.uploader.upload(path, {
    resource_type: "raw",
  });
  console.log(response);
  return response.url;
}
```

### メインアプリケーションロジック

メインアプリケーションは会話の流れ、録音、分析連携を統括します。

```ts filename="speech-to-speech/call-analysis/src/base.ts" copy
import { Roark } from "@roarkanalytics/sdk";
import chalk from "chalk";

import { mastra } from "./mastra";
import { createConversation, formatToolInvocations } from "./utils";
import { uploadToCloudinary } from "./upload";
import fs from "fs";

const client = new Roark({
  bearerToken: process.env.ROARK_API_KEY,
});

async function speechToSpeechServerExample() {
  const { start, stop } = createConversation({
    mastra,
    recordingPath: "./speech-to-speech-server.mp3",
    providerOptions: {},
    initialMessage: "Howdy partner",
    onConversationEnd: async (props) => {
      // File upload
      fs.writeFileSync(props.recordingPath, props.audioBuffer);
      const url = await uploadToCloudinary(props.recordingPath);

      // Send to Roark
      console.log("Send to Roark", url);
      const response = await client.callAnalysis.create({
        recordingUrl: url,
        startedAt: props.startedAt,
        callDirection: "INBOUND",
        interfaceType: "PHONE",
        participants: [
          {
            role: "AGENT",
            spokeFirst: props.agent.spokeFirst,
            name: props.agent.name,
            phoneNumber: props.agent.phoneNumber,
          },
          {
            role: "CUSTOMER",
            name: "Yujohn Nattrass",
            phoneNumber: "987654321",
          },
        ],
        properties: props.metadata,
        toolInvocations: formatToolInvocations(props.toolInvocations),
      });

      console.log("Call Recording Posted:", response.data);
    },
    onWriting: (ev) => {
      if (ev.role === "assistant") {
        process.stdout.write(chalk.blue(ev.text));
      }
    },
  });

  await start();

  process.on("SIGINT", async (e) => {
    await stop();
  });
}

speechToSpeechServerExample().catch(console.error);
```

## 会話ユーティリティ

`utils.ts` ファイルには、会話を管理するための補助関数が含まれています。主な内容は以下の通りです。

1. 会話セッションの作成と管理
2. 音声録音の処理
3. ツール呼び出しの処理
4. 会話ライフサイクルイベントの管理

## サンプルの実行

次のコマンドで会話を開始します:

```bash copy
npm run dev
```

アプリケーションは以下の処理を行います:

1. Mastraエージェントとのリアルタイム音声会話を開始します
2. 会話全体を録音します
3. 会話終了時に録音をCloudinaryにアップロードします
4. 会話データをRoark Analyticsに送信して分析します
5. 分析結果を表示します

## 主な機能

- **リアルタイム音声対音声変換**: OpenAIの音声モデルを使用し、自然な会話を実現
- **会話録音**: 会話全体を記録し、後で分析可能
- **ツール呼び出しの追跡**: 会話中にAIツールがいつ、どのように使われたかを記録
- **分析ツール連携**: 会話データをRoark Analyticsに送信し、詳細な分析を実施
- **クラウドストレージ**: 録音をCloudinaryにアップロードし、安全に保存・アクセス

## カスタマイズ

この例は、以下の方法でカスタマイズできます：

- エージェントの指示や機能を変更する
- エージェントが使用する追加ツールを追加する
- 会話の流れや最初のメッセージを変更する
- カスタムメタデータで分析連携を拡張する

完全なサンプルコードを見るには、[Github リポジトリ](https://github.com/mastra-ai/voice-examples/tree/main/speech-to-speech/call-analysis)をご覧ください。

<br />
<br />

<GithubLink link="https://github.com/mastra-ai/voice-examples/tree/main/speech-to-speech/call-analysis" />
