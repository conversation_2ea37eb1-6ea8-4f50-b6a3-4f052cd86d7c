---
title: "例: テキスト読み上げ | Voice | Mastra ドキュメント"
description: Mastra を使ってテキスト読み上げアプリケーションを作成する例です。
---

import { GithubLink } from "@/components/github-link";

# インタラクティブストーリージェネレーター

以下のコードスニペットは、Next.js を使用したインタラクティブストーリージェネレーターアプリケーションにおける Text-to-Speech（TTS）機能の実装例を示しています。Mastra を別のバックエンド統合として利用しています。この例では、Mastra client-js SDK を使ってご自身の Mastra バックエンドに接続する方法を紹介します。Next.js との Mastra の統合について詳しくは、[Next.js との統合](/docs/frameworks/next-js) ドキュメントをご参照ください。

## TTS機能を備えたエージェントの作成

次の例は、バックエンドでTTS機能を持つストーリー生成エージェントを設定する方法を示しています。

```typescript filename="src/mastra/agents/index.ts"
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { OpenAIVoice } from "@mastra/voice-openai";
import { Memory } from "@mastra/memory";

const instructions = `
    You are an Interactive Storyteller Agent. Your job is to create engaging
    short stories with user choices that influence the narrative. // omitted for brevity
`;

export const storyTellerAgent = new Agent({
  name: "Story Teller Agent",
  instructions: instructions,
  model: openai("gpt-4o"),
  voice: new OpenAIVoice(),
});
```

## Mastra へのエージェント登録

このスニペットは、エージェントをあなたの Mastra インスタンスに登録する方法を示しています。

```typescript filename="src/mastra/index.ts"
import { PinoLogger } from "@mastra/loggers";
import { Mastra } from "@mastra/core/mastra";
import { storyTellerAgent } from "./agents";

export const mastra = new Mastra({
  agents: { storyTellerAgent },
  logger: new PinoLogger({
    name: "Mastra",
    level: "info",
  }),
});
```

## フロントエンドからMastraへの接続

ここでは、Mastra Client SDKを使用してMastraサーバーとやり取りします。Mastra Client SDKの詳細については、[ドキュメント](/docs/deployment/client)をご覧ください。

```typescript filename="src/app/page.tsx"
import { MastraClient } from "@mastra/client-js";

export const mastraClient = new MastraClient({
  baseUrl: "http://localhost:4111", // Replace with your Mastra backend URL
});
```

## ストーリーコンテンツの生成と音声への変換

この例では、Mastraエージェントへの参照を取得し、ユーザー入力に基づいてストーリーコンテンツを生成し、そのコンテンツを音声に変換する方法を示します。

```typescript filename="/app/components/StoryManager.tsx"
const handleInitialSubmit = async (formData: FormData) => {
  setIsLoading(true);
  try {
    const agent = mastraClient.getAgent("storyTellerAgent");
    const message = `Current phase: BEGINNING. Story genre: ${formData.genre}, Protagonist name: ${formData.protagonistDetails.name}, Protagonist age: ${formData.protagonistDetails.age}, Protagonist gender: ${formData.protagonistDetails.gender}, Protagonist occupation: ${formData.protagonistDetails.occupation}, Story Setting: ${formData.setting}`;
    const storyResponse = await agent.generate({
      messages: [{ role: "user", content: message }],
      threadId: storyState.threadId,
      resourceId: storyState.resourceId,
    });

    const storyText = storyResponse.text;

    const audioResponse = await agent.voice.speak(storyText);

    if (!audioResponse.body) {
      throw new Error("No audio stream received");
    }

    const audio = await readStream(audioResponse.body);

    setStoryState((prev) => ({
      phase: "beginning",
      threadId: prev.threadId,
      resourceId: prev.resourceId,
      content: {
        ...prev.content,
        beginning: storyText,
      },
    }));

    setAudioBlob(audio);
    return audio;
  } catch (error) {
    console.error("Error generating story beginning:", error);
  } finally {
    setIsLoading(false);
  }
};
```

## 音声の再生

このスニペットは、新しい音声データを監視してテキスト読み上げ音声の再生を処理する方法を示しています。音声が受信されると、コードは音声のBlobからブラウザで再生可能なURLを作成し、それをaudio要素に割り当て、自動的に再生を試みます。

```typescript filename="/app/components/StoryManager.tsx"
useEffect(() => {
  if (!audioRef.current || !audioData) return;

  // Store a reference to the HTML audio element
  const currentAudio = audioRef.current;

  // Convert the Blob/File audio data from Mastra into a URL the browser can play
  const url = URL.createObjectURL(audioData);

  const playAudio = async () => {
    try {
      currentAudio.src = url;
      await currentAudio.load();
      await currentAudio.play();
      setIsPlaying(true);
    } catch (error) {
      console.error("Auto-play failed:", error);
    }
  };

  playAudio();

  return () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.src = "";
      URL.revokeObjectURL(url);
    }
  };
}, [audioData]);
```

Interactive Story Generator の完全な実装は、私たちの GitHub リポジトリでご覧いただけます。

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/voice-examples/tree/main/text-to-speech/interactive-story"
  }
/>
