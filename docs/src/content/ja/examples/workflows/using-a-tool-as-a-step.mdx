---
title: "例: ツールをステップとして使用する | ワークフロー | Mastra ドキュメント"
description: Mastra を使ってカスタムツールをワークフローのステップとして統合する例。
---

import { GithubLink } from "@/components/github-link";

# ワークフローステップとしてのツール

この例では、カスタムツールをワークフローステップとして作成し統合する方法を示しています。入力／出力スキーマの定義方法や、ツールの実行ロジックの実装方法について説明します。

```ts showLineNumbers copy
import { createTool } from "@mastra/core/tools";
import { Workflow } from "@mastra/core/workflows";
import { z } from "zod";

const crawlWebpage = createTool({
  id: "Crawl Webpage",
  description: "Crawls a webpage and extracts the text content",
  inputSchema: z.object({
    url: z.string().url(),
  }),
  outputSchema: z.object({
    rawText: z.string(),
  }),
  execute: async ({ context }) => {
    const response = await fetch(context.triggerData.url);
    const text = await response.text();
    return { rawText: "This is the text content of the webpage: " + text };
  },
});

const contentWorkflow = new Workflow({ name: "content-review" });

contentWorkflow.step(crawlWebpage).commit();

const { start } = contentWorkflow.createRun();

const res = await start({ triggerData: { url: "https://example.com" } });

console.log(res.results);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/workflows/tool-as-workflow-step"
  }
/>
