---
title: "例: ワークフローからエージェントを呼び出す | Mastra ドキュメント"
description: ワークフローのステップ内で Mastra を使って AI エージェントを呼び出す例。
---

import { GithubLink } from "@/components/github-link";

# ワークフローからエージェントを呼び出す

この例では、AIエージェントを呼び出してメッセージを処理し、応答を生成するワークフローを作成し、それをワークフローステップ内で実行する方法を示します。

```ts showLineNumbers copy
import { openai } from "@ai-sdk/openai";
import { Mastra } from "@mastra/core";
import { Agent } from "@mastra/core/agent";
import { Step, Workflow } from "@mastra/core/workflows";
import { z } from "zod";

const penguin = new Agent({
  name: "agent skipper",
  instructions: `You are skipper from penguin of madagascar, reply as that`,
  model: openai("gpt-4o-mini"),
});

const newWorkflow = new Workflow({
  name: "pass message to the workflow",
  triggerSchema: z.object({
    message: z.string(),
  }),
});

const replyAsSkipper = new Step({
  id: "reply",
  outputSchema: z.object({
    reply: z.string(),
  }),
  execute: async ({ context, mastra }) => {
    const skipper = mastra?.getAgent("penguin");

    const res = await skipper?.generate(context?.triggerData?.message);
    return { reply: res?.text || "" };
  },
});

newWorkflow.step(replyAsSkipper);
newWorkflow.commit();

const mastra = new Mastra({
  agents: { penguin },
  workflows: { newWorkflow },
});

const { runId, start } = await mastra.getWorkflow("newWorkflow").createRun();

const runResult = await start({
  triggerData: { message: "Give me a run down of the mission to save private" },
});

console.log(runResult.results);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/workflows/calling-agent-from-workflow"
  }
/>
