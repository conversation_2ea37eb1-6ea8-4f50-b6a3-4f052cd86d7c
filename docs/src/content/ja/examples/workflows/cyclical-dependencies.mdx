---
title: "例: 循環依存関係 | ワークフロー | Mastra ドキュメント"
description: Mastra を使用して循環依存関係や条件付きループを持つワークフローを作成する例。
---

import { GithubLink } from "@/components/github-link";

# 循環依存関係を持つワークフロー

ワークフローは、条件に基づいてステップがループバックできる循環依存関係をサポートしています。以下の例では、条件付きロジックを使ってループを作成し、繰り返し実行を処理する方法を示しています。

```ts showLineNumbers copy
import { Workflow, Step } from "@mastra/core";
import { z } from "zod";

async function main() {
  const doubleValue = new Step({
    id: "doubleValue",
    description: "Doubles the input value",
    inputSchema: z.object({
      inputValue: z.number(),
    }),
    outputSchema: z.object({
      doubledValue: z.number(),
    }),
    execute: async ({ context }) => {
      const doubledValue = context.inputValue * 2;
      return { doubledValue };
    },
  });

  const incrementByOne = new Step({
    id: "incrementByOne",
    description: "Adds 1 to the input value",
    outputSchema: z.object({
      incrementedValue: z.number(),
    }),
    execute: async ({ context }) => {
      const valueToIncrement = context?.getStepResult<{ firstValue: number }>(
        "trigger",
      )?.firstValue;
      if (!valueToIncrement) throw new Error("No value to increment provided");
      const incrementedValue = valueToIncrement + 1;
      return { incrementedValue };
    },
  });

  const cyclicalWorkflow = new Workflow({
    name: "cyclical-workflow",
    triggerSchema: z.object({
      firstValue: z.number(),
    }),
  });

  cyclicalWorkflow
    .step(doubleValue, {
      variables: {
        inputValue: {
          step: "trigger",
          path: "firstValue",
        },
      },
    })
    .then(incrementByOne)
    .after(doubleValue)
    .step(doubleValue, {
      variables: {
        inputValue: {
          step: doubleValue,
          path: "doubledValue",
        },
      },
    })
    .commit();

  const { runId, start } = cyclicalWorkflow.createRun();

  console.log("Run", runId);

  const res = await start({ triggerData: { firstValue: 6 } });

  console.log(res.results);
}

main();
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/workflows/workflow-with-cyclical-deps"
  }
/>
