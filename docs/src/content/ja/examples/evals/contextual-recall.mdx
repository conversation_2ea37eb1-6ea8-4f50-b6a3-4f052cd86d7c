---
title: "例: コンテクスチュアルリコール | Evals | Mastra Docs"
description: コンテクスチュアルリコール指標を使用して、応答がどれだけ文脈情報を取り入れているかを評価する例。
---

import { GithubLink } from "@/components/github-link";

# コンテクスチュアルリコール

この例では、Mastra のコンテクスチュアルリコール指標を使用して、回答が提供されたコンテキストの情報をどれだけ効果的に取り入れているかを評価する方法を示します。

## 概要

この例では、以下の方法を示します。

1. Contextual Recallメトリクスの設定
2. コンテキストの取り込みを評価する
3. リコールスコアを分析する
4. 異なるリコールレベルへの対応

## セットアップ

### 環境セットアップ

環境変数を必ず設定してください：

```bash filename=".env"
OPENAI_API_KEY=your_api_key_here
```

### 依存関係

必要な依存関係をインポートします：

```typescript copy showLineNumbers filename="src/index.ts"
import { openai } from "@ai-sdk/openai";
import { ContextualRecallMetric } from "@mastra/evals/llm";
```

## 使用例

### 高リコールの例

すべてのコンテキスト情報を含む応答を評価します：

```typescript copy showLineNumbers{5} filename="src/index.ts"
const context1 = [
  "Product features include cloud sync.",
  "Offline mode is available.",
  "Supports multiple devices.",
];

const metric1 = new ContextualRecallMetric(openai("gpt-4o-mini"), {
  context: context1,
});

const query1 = "What are the key features of the product?";
const response1 =
  "The product features cloud synchronization, offline mode support, and the ability to work across multiple devices.";

console.log("Example 1 - High Recall:");
console.log("Context:", context1);
console.log("Query:", query1);
console.log("Response:", response1);

const result1 = await metric1.measure(query1, response1);
console.log("Metric Result:", {
  score: result1.score,
  reason: result1.info.reason,
});
// Example Output:
// Metric Result: { score: 1, reason: 'All elements of the output are supported by the context.' }
```

### 混合リコールの例

一部のコンテキスト情報を含む応答を評価します：

```typescript copy showLineNumbers{27} filename="src/index.ts"
const context2 = [
  "Python is a high-level programming language.",
  "Python emphasizes code readability.",
  "Python supports multiple programming paradigms.",
  "Python is widely used in data science.",
];

const metric2 = new ContextualRecallMetric(openai("gpt-4o-mini"), {
  context: context2,
});

const query2 = "What are Python's key characteristics?";
const response2 =
  "Python is a high-level programming language. It is also a type of snake.";

console.log("Example 2 - Mixed Recall:");
console.log("Context:", context2);
console.log("Query:", query2);
console.log("Response:", response2);

const result2 = await metric2.measure(query2, response2);
console.log("Metric Result:", {
  score: result2.score,
  reason: result2.info.reason,
});
// Example Output:
// Metric Result: { score: 0.5, reason: 'Only half of the output is supported by the context.' }
```

### 低リコールの例

ほとんどのコンテキスト情報を見落とした応答を評価します：

```typescript copy showLineNumbers{53} filename="src/index.ts"
const context3 = [
  "The solar system has eight planets.",
  "Mercury is closest to the Sun.",
  "Venus is the hottest planet.",
  "Mars is called the Red Planet.",
];

const metric3 = new ContextualRecallMetric(openai("gpt-4o-mini"), {
  context: context3,
});

const query3 = "Tell me about the solar system.";
const response3 = "Jupiter is the largest planet in the solar system.";

console.log("Example 3 - Low Recall:");
console.log("Context:", context3);
console.log("Query:", query3);
console.log("Response:", response3);

const result3 = await metric3.measure(query3, response3);
console.log("Metric Result:", {
  score: result3.score,
  reason: result3.info.reason,
});
// Example Output:
// Metric Result: { score: 0, reason: 'None of the output is supported by the context.' }
```

## 結果の理解

この指標は以下を提供します：

1. 0から1の間のリコールスコア：

   - 1.0: 完全なリコール - すべてのコンテキスト情報が使用された
   - 0.7-0.9: 高いリコール - ほとんどのコンテキスト情報が使用された
   - 0.4-0.6: 混合リコール - 一部のコンテキスト情報が使用された
   - 0.1-0.3: 低いリコール - わずかなコンテキスト情報が使用された
   - 0.0: リコールなし - コンテキスト情報が使用されていない

2. スコアの詳細な理由付け（以下の分析を含む）：
   - 情報の取り込み
   - 欠落しているコンテキスト
   - 応答の完全性
   - 全体的なリコール品質

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/evals/contextual-recall"
  }
/>
