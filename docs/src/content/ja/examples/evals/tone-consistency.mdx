---
title: "例: トーンの一貫性 | Evals | Mastra Docs"
description: トーンの一貫性メトリクスを使用して、テキスト内の感情的なトーンパターンや感情の一貫性を評価する例。
---

import { GithubLink } from "@/components/github-link";

# トーン一貫性評価

この例では、Mastra のトーン一貫性メトリックを使用して、テキスト内の感情的なトーンパターンや感情の一貫性を評価する方法を示します。

## 概要

この例では、以下の方法を示します。

1. Tone Consistencyメトリクスの設定方法
2. テキスト間の感情の比較
3. テキスト内のトーンの安定性の分析
4. 異なるトーンのシナリオへの対応

## セットアップ

### 依存関係

必要な依存関係をインポートします：

```typescript copy showLineNumbers filename="src/index.ts"
import { ToneConsistencyMetric } from "@mastra/evals/nlp";
```

## メトリック設定

トーンの一貫性メトリックを設定します：

```typescript copy showLineNumbers{4} filename="src/index.ts"
const metric = new ToneConsistencyMetric();
```

## 使用例

### 一貫したポジティブなトーンの例

類似したポジティブな感情を持つテキストを評価します：

```typescript copy showLineNumbers{7} filename="src/index.ts"
const input1 = "This product is fantastic and amazing!";
const output1 = "The product is excellent and wonderful!";

console.log("Example 1 - Consistent Positive Tone:");
console.log("Input:", input1);
console.log("Output:", output1);

const result1 = await metric.measure(input1, output1);
console.log("Metric Result:", {
  score: result1.score,
  info: result1.info,
});
// Example Output:
// Metric Result: {
//   score: 0.8333333333333335,
//   info: {
//     responseSentiment: 1.3333333333333333,
//     referenceSentiment: 1.1666666666666667,
//     difference: 0.16666666666666652
//   }
// }
```

### トーンの安定性の例

単一のテキスト内で感情の一貫性を評価します：

```typescript copy showLineNumbers{21} filename="src/index.ts"
const input2 = "Great service! Friendly staff. Perfect atmosphere.";
const output2 = ""; // Empty string for stability analysis

console.log("Example 2 - Tone Stability:");
console.log("Input:", input2);
console.log("Output:", output2);

const result2 = await metric.measure(input2, output2);
console.log("Metric Result:", {
  score: result2.score,
  info: result2.info,
});
// Example Output:
// Metric Result: {
//   score: 0.9444444444444444,
//   info: {
//     avgSentiment: 1.3333333333333333,
//     sentimentVariance: 0.05555555555555556
//   }
// }
```

### 混合トーンの例

異なる感情を持つテキストを評価します：

```typescript copy showLineNumbers{35} filename="src/index.ts"
const input3 =
  "The interface is frustrating and confusing, though it has potential.";
const output3 =
  "The design shows promise but needs significant improvements to be usable.";

console.log("Example 3 - Mixed Tone:");
console.log("Input:", input3);
console.log("Output:", output3);

const result3 = await metric.measure(input3, output3);
console.log("Metric Result:", {
  score: result3.score,
  info: result3.info,
});
// Example Output:
// Metric Result: {
//   score: 0.4181818181818182,
//   info: {
//     responseSentiment: -0.4,
//     referenceSentiment: 0.18181818181818182,
//     difference: 0.5818181818181818
//   }
// }
```

## 結果の理解

この指標は、モードに応じて異なる出力を提供します。

1. 比較モード（出力テキストが提供されている場合）:

   - 0から1のスコアでトーンの一貫性を示す
   - 入力の感情: 入力テキストの感情的トーン（-1から1）
   - 参照の感情: 出力テキストの感情的トーン（-1から1）
   - 差分: 感情値の絶対差

   スコアの解釈:

   - 0.8-1.0: 非常に一貫したトーン
   - 0.6-0.7: おおむね一貫している
   - 0.4-0.5: 混在したトーン
   - 0.0-0.3: 矛盾したトーン

2. 安定性モード（単一テキストを分析する場合）:

   - 0から1のスコアで内部一貫性を示す
   - 平均感情: 全体的な感情トーン
   - 感情の分散: 文ごとのトーンの変動度

   スコアの解釈:

   - 0.9-1.0: 非常に安定したトーン
   - 0.7-0.8: ほぼ安定している
   - 0.4-0.6: 変動するトーン
   - 0.0-0.3: 非常に不安定

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/evals/tone-consistency"
  }
/>
