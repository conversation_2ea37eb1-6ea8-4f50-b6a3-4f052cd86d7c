---
title: "例: コンテキスト関連性 | Evals | Mastra Docs"
description: Context Relevancyメトリクスを使用して、クエリに対するコンテキスト情報の関連性を評価する例。
---

import { GithubLink } from "@/components/github-link";

# コンテキスト関連性

この例では、Mastra のコンテキスト関連性指標を使用して、コンテキスト情報が特定のクエリにどれだけ関連しているかを評価する方法を示します。

## 概要

この例では、以下の方法を示します。

1. Context Relevancyメトリックの設定
2. コンテキストの関連性を評価する
3. 関連性スコアを分析する
4. 異なる関連性レベルへの対応

## セットアップ

### 環境設定

環境変数を必ず設定してください：

```bash filename=".env"
OPENAI_API_KEY=your_api_key_here
```

### 依存関係

必要な依存関係をインポートします：

```typescript copy showLineNumbers filename="src/index.ts"
import { openai } from "@ai-sdk/openai";
import { ContextRelevancyMetric } from "@mastra/evals/llm";
```

## 使用例

### 高い関連性の例

すべてのコンテキストが関連している応答を評価します：

```typescript copy showLineNumbers{5} filename="src/index.ts"
const context1 = [
  "Einstein won the Nobel Prize for his discovery of the photoelectric effect.",
  "He published his theory of relativity in 1905.",
  "His work revolutionized modern physics.",
];

const metric1 = new ContextRelevancyMetric(openai("gpt-4o-mini"), {
  context: context1,
});

const query1 = "What were some of Einstein's achievements?";
const response1 =
  "Einstein won the Nobel Prize for discovering the photoelectric effect and published his groundbreaking theory of relativity.";

console.log("Example 1 - High Relevancy:");
console.log("Context:", context1);
console.log("Query:", query1);
console.log("Response:", response1);

const result1 = await metric1.measure(query1, response1);
console.log("Metric Result:", {
  score: result1.score,
  reason: result1.info.reason,
});
// Example Output:
// Metric Result: { score: 1, reason: 'The context uses all relevant information and does not include any irrelevant information.' }
```

### 関連性が混在している例

一部のコンテキストが無関係である応答を評価します：

```typescript copy showLineNumbers{31} filename="src/index.ts"
const context2 = [
  "Solar eclipses occur when the Moon blocks the Sun.",
  "The Moon moves between the Earth and Sun during eclipses.",
  "The Moon is visible at night.",
  "The Moon has no atmosphere.",
];

const metric2 = new ContextRelevancyMetric(openai("gpt-4o-mini"), {
  context: context2,
});

const query2 = "What causes solar eclipses?";
const response2 =
  "Solar eclipses happen when the Moon moves between Earth and the Sun, blocking sunlight.";

console.log("Example 2 - Mixed Relevancy:");
console.log("Context:", context2);
console.log("Query:", query2);
console.log("Response:", response2);

const result2 = await metric2.measure(query2, response2);
console.log("Metric Result:", {
  score: result2.score,
  reason: result2.info.reason,
});
// Example Output:
// Metric Result: { score: 0.5, reason: 'The context uses some relevant information and includes some irrelevant information.' }
```

### 低い関連性の例

ほとんどのコンテキストが無関係である応答を評価します：

```typescript copy showLineNumbers{57} filename="src/index.ts"
const context3 = [
  "The Great Barrier Reef is in Australia.",
  "Coral reefs need warm water to survive.",
  "Marine life depends on coral reefs.",
  "The capital of Australia is Canberra.",
];

const metric3 = new ContextRelevancyMetric(openai("gpt-4o-mini"), {
  context: context3,
});

const query3 = "What is the capital of Australia?";
const response3 = "The capital of Australia is Canberra.";

console.log("Example 3 - Low Relevancy:");
console.log("Context:", context3);
console.log("Query:", query3);
console.log("Response:", response3);

const result3 = await metric3.measure(query3, response3);
console.log("Metric Result:", {
  score: result3.score,
  reason: result3.info.reason,
});
// Example Output:
// Metric Result: { score: 0.12, reason: 'The context only has one relevant piece, while most of the context is irrelevant.' }
```

## 結果の理解

この指標は以下を提供します：

1. 0から1の間の関連性スコア：

   - 1.0: 完全な関連性 - すべてのコンテキストがクエリに直接関連
   - 0.7-0.9: 高い関連性 - ほとんどのコンテキストがクエリに関連
   - 0.4-0.6: 混合された関連性 - 一部のコンテキストがクエリに関連
   - 0.1-0.3: 低い関連性 - わずかなコンテキストがクエリに関連
   - 0.0: 関連性なし - クエリに関連するコンテキストが存在しない

2. スコアの詳細な理由付け（以下の分析を含む）：
   - 入力クエリへの関連性
   - コンテキストからの文の抽出
   - 応答への有用性
   - 全体的なコンテキストの質

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/evals/context-relevancy"
  }
/>
