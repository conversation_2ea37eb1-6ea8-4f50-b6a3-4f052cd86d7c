---
title: "例: 回答の関連性 | Evals | Mastra Docs"
description: Answer Relevancyメトリクスを使用してクエリへの回答の関連性を評価する例。
---

import { GithubLink } from "@/components/github-link";

# 回答の関連性評価

この例では、Mastra の回答関連性指標を使用して、応答が入力クエリにどれだけ適切に対応しているかを評価する方法を示します。

## 概要

この例では、以下の方法を示します。

1. Answer Relevancyメトリクスの設定
2. クエリに対する応答の関連性を評価する
3. 関連性スコアを分析する
4. 様々な関連性シナリオへの対応

## セットアップ

### 環境設定

環境変数を必ず設定してください：

```bash filename=".env"
OPENAI_API_KEY=your_api_key_here
```

### 依存関係

必要な依存関係をインポートします：

```typescript copy showLineNumbers filename="src/index.ts"
import { openai } from "@ai-sdk/openai";
import { AnswerRelevancyMetric } from "@mastra/evals/llm";
```

## メトリック設定

カスタムパラメータでAnswer Relevancyメトリックを設定します：

```typescript copy showLineNumbers{5} filename="src/index.ts"
const metric = new AnswerRelevancyMetric(openai("gpt-4o-mini"), {
  uncertaintyWeight: 0.3, // Weight for 'unsure' verdicts
  scale: 1, // Scale for the final score
});
```

## 使用例

### 高い関連性の例

非常に関連性の高い回答を評価します：

```typescript copy showLineNumbers{11} filename="src/index.ts"
const query1 = "What are the health benefits of regular exercise?";
const response1 =
  "Regular exercise improves cardiovascular health, strengthens muscles, boosts metabolism, and enhances mental well-being through the release of endorphins.";

console.log("Example 1 - High Relevancy:");
console.log("Query:", query1);
console.log("Response:", response1);

const result1 = await metric.measure(query1, response1);
console.log("Metric Result:", {
  score: result1.score,
  reason: result1.info.reason,
});
// Example Output:
// Metric Result: { score: 1, reason: 'The response is highly relevant to the query. It provides a comprehensive overview of the health benefits of regular exercise.' }
```

### 部分的な関連性の例

部分的に関連性のある回答を評価します：

```typescript copy showLineNumbers{26} filename="src/index.ts"
const query2 = "What should a healthy breakfast include?";
const response2 =
  "A nutritious breakfast should include whole grains and protein. However, the timing of your breakfast is just as important - studies show eating within 2 hours of waking optimizes metabolism and energy levels throughout the day.";

console.log("Example 2 - Partial Relevancy:");
console.log("Query:", query2);
console.log("Response:", response2);

const result2 = await metric.measure(query2, response2);
console.log("Metric Result:", {
  score: result2.score,
  reason: result2.info.reason,
});
// Example Output:
// Metric Result: { score: 0.7, reason: 'The response is partially relevant to the query. It provides some information about healthy breakfast choices but misses the timing aspect.' }
```

### 低い関連性の例

関連性の低い回答を評価します：

```typescript copy showLineNumbers{41} filename="src/index.ts"
const query3 = "What are the benefits of meditation?";
const response3 =
  "The Great Wall of China is over 13,000 miles long and was built during the Ming Dynasty to protect against invasions.";

console.log("Example 3 - Low Relevancy:");
console.log("Query:", query3);
console.log("Response:", response3);

const result3 = await metric.measure(query3, response3);
console.log("Metric Result:", {
  score: result3.score,
  reason: result3.info.reason,
});
// Example Output:
// Metric Result: { score: 0.1, reason: 'The response is not relevant to the query. It provides information about the Great Wall of China but does not mention meditation.' }
```

## 結果の理解

この指標は以下を提供します：

1. 0から1の間の関連性スコア：

   - 1.0: 完全な関連性 - 応答がクエリに直接対応している
   - 0.7-0.9: 高い関連性 - 応答が主にクエリに対応している
   - 0.4-0.6: 中程度の関連性 - 応答が部分的にクエリに対応している
   - 0.1-0.3: 低い関連性 - 応答がほとんどクエリに対応していない
   - 0.0: 関連性なし - 応答が全くクエリに対応していない

2. スコアの詳細な理由、以下の分析を含む：
   - クエリと応答の整合性
   - トピックの焦点
   - 情報の関連性
   - 改善提案

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/evals/answer-relevancy"
  }
/>
