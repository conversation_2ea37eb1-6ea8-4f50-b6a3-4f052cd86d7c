---
title: "例: コンテキスト精度 | Evals | Mastra Docs"
description: コンテキスト精度メトリクスを使用して、コンテキスト情報がどれだけ正確に利用されているかを評価する例。
---

import { GithubLink } from "@/components/github-link";

# コンテキスト精度

この例では、Mastra のコンテキスト精度メトリクスを使用して、回答が提供されたコンテキスト情報をどれだけ正確に利用しているかを評価する方法を示します。

## 概要

この例では、以下の方法を示します：

1. Context Precisionメトリクスの設定
2. コンテキスト精度の評価
3. 精度スコアの分析
4. 異なる精度レベルの対応

## セットアップ

### 環境セットアップ

環境変数を必ず設定してください：

```bash filename=".env"
OPENAI_API_KEY=your_api_key_here
```

### 依存関係

必要な依存関係をインポートします：

```typescript copy showLineNumbers filename="src/index.ts"
import { openai } from "@ai-sdk/openai";
import { ContextPrecisionMetric } from "@mastra/evals/llm";
```

## 使用例

### 高精度の例

すべてのコンテキストが関連している応答を評価します：

```typescript copy showLineNumbers{5} filename="src/index.ts"
const context1 = [
  "Photosynthesis converts sunlight into energy.",
  "Plants use chlorophyll for photosynthesis.",
  "Photosynthesis produces oxygen as a byproduct.",
  "The process requires sunlight and chlorophyll.",
];

const metric1 = new ContextPrecisionMetric(openai("gpt-4o-mini"), {
  context: context1,
});

const query1 = "What is photosynthesis and how does it work?";
const response1 =
  "Photosynthesis is a process where plants convert sunlight into energy using chlorophyll, producing oxygen as a byproduct.";

console.log("Example 1 - High Precision:");
console.log("Context:", context1);
console.log("Query:", query1);
console.log("Response:", response1);

const result1 = await metric1.measure(query1, response1);
console.log("Metric Result:", {
  score: result1.score,
  reason: result1.info.reason,
});
// Example Output:
// Metric Result: { score: 1, reason: 'The context uses all relevant information and does not include any irrelevant information.' }
```

### 混合精度の例

一部のコンテキストが無関係な応答を評価します：

```typescript copy showLineNumbers{32} filename="src/index.ts"
const context2 = [
  "Volcanoes are openings in the Earth's crust.",
  "Volcanoes can be active, dormant, or extinct.",
  "Hawaii has many active volcanoes.",
  "The Pacific Ring of Fire has many volcanoes.",
];

const metric2 = new ContextPrecisionMetric(openai("gpt-4o-mini"), {
  context: context2,
});

const query2 = "What are the different types of volcanoes?";
const response2 =
  "Volcanoes can be classified as active, dormant, or extinct based on their activity status.";

console.log("Example 2 - Mixed Precision:");
console.log("Context:", context2);
console.log("Query:", query2);
console.log("Response:", response2);

const result2 = await metric2.measure(query2, response2);
console.log("Metric Result:", {
  score: result2.score,
  reason: result2.info.reason,
});
// Example Output:
// Metric Result: { score: 0.5, reason: 'The context uses some relevant information and includes some irrelevant information.' }
```

### 低精度の例

ほとんどのコンテキストが無関係な応答を評価します：

```typescript copy showLineNumbers{58} filename="src/index.ts"
const context3 = [
  "The Nile River is in Africa.",
  "The Nile is the longest river.",
  "Ancient Egyptians used the Nile.",
  "The Nile flows north.",
];

const metric3 = new ContextPrecisionMetric(openai("gpt-4o-mini"), {
  context: context3,
});

const query3 = "Which direction does the Nile River flow?";
const response3 = "The Nile River flows northward.";

console.log("Example 3 - Low Precision:");
console.log("Context:", context3);
console.log("Query:", query3);
console.log("Response:", response3);

const result3 = await metric3.measure(query3, response3);
console.log("Metric Result:", {
  score: result3.score,
  reason: result3.info.reason,
});
// Example Output:
// Metric Result: { score: 0.2, reason: 'The context only has one relevant piece, which is at the end.' }
```

## 結果の理解

この指標は以下を提供します：

1. 0から1の間の精度スコア：

   - 1.0: 完全な精度 - すべてのコンテキスト要素が関連しており使用されている
   - 0.7-0.9: 高い精度 - ほとんどのコンテキスト要素が関連している
   - 0.4-0.6: 混合精度 - 一部のコンテキスト要素が関連している
   - 0.1-0.3: 低い精度 - わずかなコンテキスト要素が関連している
   - 0.0: 精度なし - どのコンテキスト要素も関連していない

2. スコアの詳細な理由付け（以下の分析を含む）：
   - 各コンテキスト要素の関連性
   - 応答での使用状況
   - クエリへの回答への貢献度
   - 全体的なコンテキストの有用性

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />
<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/evals/context-precision"
  }
/>
