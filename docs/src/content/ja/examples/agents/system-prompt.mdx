---
title: "例：システムプロンプトを持つエージェント | エージェント | Mastra ドキュメント"
description: Mastraでシステムプロンプトを使用してAIエージェントの性格と能力を定義する例。
---

import { GithubLink } from "@/components/github-link";

# エージェントにシステムプロンプトを与える

AIエージェントを構築する際には、特定のタスクを効果的に処理するための具体的な指示と能力を与える必要があります。システムプロンプトを使用すると、エージェントの性格、知識領域、および行動ガイドラインを定義できます。この例では、カスタム指示を持つAIエージェントを作成し、検証済みの情報を取得するための専用ツールと統合する方法を示します。

```ts showLineNumbers copy
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { createTool } from "@mastra/core/tools";

import { z } from "zod";

const instructions = `You are a helpful cat expert assistant. When discussing cats, you should always include an interesting cat fact.

  Your main responsibilities:
  1. Answer questions about cats
  2. Use the catFact tool to provide verified cat facts
  3. Incorporate the cat facts naturally into your responses

  Always use the catFact tool at least once in your responses to ensure accuracy.`;

const getCatFact = async () => {
  const { fact } = (await fetch("https://catfact.ninja/fact").then((res) =>
    res.json(),
  )) as {
    fact: string;
  };

  return fact;
};

const catFact = createTool({
  id: "Get cat facts",
  inputSchema: z.object({}),
  description: "Fetches cat facts",
  execute: async () => {
    console.log("using tool to fetch cat fact");
    return {
      catFact: await getCatFact(),
    };
  },
});

const catOne = new Agent({
  name: "cat-one",
  instructions: instructions,
  model: openai("gpt-4o-mini"),
  tools: {
    catFact,
  },
});

const result = await catOne.generate("Tell me a cat fact");

console.log(result.text);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />

<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/agents/system-prompt"
  }
/>
