---
title: "例：鳥の分類 | エージェント | Mastra ドキュメント"
description: Unsplashからの画像が鳥を描写しているかどうかを判断するためにMastra AIエージェントを使用する例。
---

import { GithubLink } from "@/components/github-link";

# 例: AIエージェントで鳥を分類する

選択したクエリに一致するランダムな画像を[Unsplash](https://unsplash.com/)から取得し、それが鳥かどうかを判断するために[Mastra AI Agent](/docs/agents/overview.md)を使用します。

```ts showLineNumbers copy
import { anthropic } from "@ai-sdk/anthropic";
import { Agent } from "@mastra/core/agent";
import { z } from "zod";

export type Image = {
  alt_description: string;
  urls: {
    regular: string;
    raw: string;
  };
  user: {
    first_name: string;
    links: {
      html: string;
    };
  };
};

export type ImageResponse<T, K> =
  | {
      ok: true;
      data: T;
    }
  | {
      ok: false;
      error: K;
    };

const getRandomImage = async ({
  query,
}: {
  query: string;
}): Promise<ImageResponse<Image, string>> => {
  const page = Math.floor(Math.random() * 20);
  const order_by = Math.random() < 0.5 ? "relevant" : "latest";
  try {
    const res = await fetch(
      `https://api.unsplash.com/search/photos?query=${query}&page=${page}&order_by=${order_by}`,
      {
        method: "GET",
        headers: {
          Authorization: `Client-ID ${process.env.UNSPLASH_ACCESS_KEY}`,
          "Accept-Version": "v1",
        },
        cache: "no-store",
      },
    );

    if (!res.ok) {
      return {
        ok: false,
        error: "Failed to fetch image",
      };
    }

    const data = (await res.json()) as {
      results: Array<Image>;
    };
    const randomNo = Math.floor(Math.random() * data.results.length);

    return {
      ok: true,
      data: data.results[randomNo] as Image,
    };
  } catch (err) {
    return {
      ok: false,
      error: "Error fetching image",
    };
  }
};

const instructions = `
  画像を見て、それが鳥かどうかを判断できます。
  また、鳥の種と写真が撮影された場所を特定することもできます。
`;

export const birdCheckerAgent = new Agent({
  name: "Bird checker",
  instructions,
  model: anthropic("claude-3-haiku-20240307"),
});

const queries: string[] = ["wildlife", "feathers", "flying", "birds"];
const randomQuery = queries[Math.floor(Math.random() * queries.length)];

// ランダムなタイプでUnsplashから画像URLを取得
const imageResponse = await getRandomImage({ query: randomQuery });

if (!imageResponse.ok) {
  console.log("Error fetching image", imageResponse.error);
  process.exit(1);
}

console.log("Image URL: ", imageResponse.data.urls.regular);
const response = await birdCheckerAgent.generate(
  [
    {
      role: "user",
      content: [
        {
          type: "image",
          image: new URL(imageResponse.data.urls.regular),
        },
        {
          type: "text",
          text: "この画像を見て、それが鳥かどうか、そして鳥の学名を説明なしで教えてください。また、この写真の場所を高校生が理解できるように1、2文で要約してください。",
        },
      ],
    },
  ],
  {
    output: z.object({
      bird: z.boolean(),
      species: z.string(),
      location: z.string(),
    }),
  },
);

console.log(response.object);
```

<br />
<br />
<hr className="dark:border-[#404040] border-gray-300" />
<br />
<br />

<GithubLink
  link={
    "https://github.com/mastra-ai/mastra/blob/main/examples/basics/agents/bird-checker"
  }
/>
