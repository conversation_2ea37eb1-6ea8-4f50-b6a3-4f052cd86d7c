---
title: "例：ワークフローからエージェントを呼び出す | Mastra ドキュメント"
description: ワークフローステップ内からAIエージェントを呼び出すためのMastraの使用例。
---

# ワークフローからエージェントを呼び出す

この例では、提供された天候条件に基づいてアクティビティを提案するAIエージェントを呼び出すワークフローを作成し、ワークフローステップ内で実行する方法を示しています。

## セットアップ

```sh copy
npm install @ai-sdk/openai @mastra/core
```

## プランニングエージェントの定義

場所と対応する気象条件に基づいて活動を計画するためにLLM呼び出しを活用するプランニングエージェントを定義します。

```ts showLineNumbers copy filename="agents/planning-agent.ts"
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

const llm = openai("gpt-4o");

// Create a new agent for activity planning
const planningAgent = new Agent({
  name: "planningAgent",
  model: llm,
  instructions: `
        You are a local activities and travel expert who excels at weather-based planning. Analyze the weather data and provide practical activity recommendations.

        📅 [Day, Month Date, Year]
        ═══════════════════════════

        🌡️ WEATHER SUMMARY
        • Conditions: [brief description]
        • Temperature: [X°C/Y°F to A°C/B°F]
        • Precipitation: [X% chance]

        🌅 MORNING ACTIVITIES
        Outdoor:
        • [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        🌞 AFTERNOON ACTIVITIES
        Outdoor:
        • [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        🏠 INDOOR ALTERNATIVES
        • [Activity Name] - [Brief description including specific venue]
          Ideal for: [weather condition that would trigger this alternative]

        ⚠️ SPECIAL CONSIDERATIONS
        • [Any relevant weather warnings, UV index, wind conditions, etc.]

        Guidelines:
        - Suggest 2-3 time-specific outdoor activities per day
        - Include 1-2 indoor backup options
        - For precipitation >50%, lead with indoor activities
        - All activities must be specific to the location
        - Include specific venues, trails, or locations
        - Consider activity intensity based on temperature
        - Keep descriptions concise but informative

        Maintain this exact formatting for consistency, using the emoji and section headers as shown.
      `,
});

export { planningAgent };
```

## アクティビティプランニングワークフローの定義

アクティビティプランニングワークフローを2つのステップで定義します：1つはネットワーク呼び出しを介して天気を取得するステップ、もう1つはプランニングエージェントを使用してアクティビティを計画するステップです。

```ts showLineNumbers copy filename="workflows/agent-workflow.ts"
import { createWorkflow, createStep } from "@mastra/core/workflows/vNext";
import { z } from "zod";

// 数値の天気コードを人間が読める説明に変換するヘルパー関数
function getWeatherCondition(code: number): string {
  const conditions: Record<number, string> = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    95: "Thunderstorm",
  };
  return conditions[code] || "Unknown";
}

const forecastSchema = z.object({
  date: z.string(),
  maxTemp: z.number(),
  minTemp: z.number(),
  precipitationChance: z.number(),
  condition: z.string(),
  location: z.string(),
});

// ステップ1：指定された都市の天気データを取得するステップを作成
const fetchWeather = createStep({
  id: "fetch-weather",
  description: "指定された都市の天気予報を取得します",
  inputSchema: z.object({
    city: z.string(),
  }),
  outputSchema: forecastSchema,
  execute: async ({ inputData }) => {
    if (!inputData) {
      throw new Error("トリガーデータが見つかりません");
    }

    // 最初のAPI呼び出し：都市名を緯度と経度に変換
    const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(inputData.city)}&count=1`;
    const geocodingResponse = await fetch(geocodingUrl);
    const geocodingData = (await geocodingResponse.json()) as {
      results: { latitude: number; longitude: number; name: string }[];
    };

    if (!geocodingData.results?.[0]) {
      throw new Error(`場所 '${inputData.city}' が見つかりません`);
    }

    const { latitude, longitude, name } = geocodingData.results[0];

    // 2番目のAPI呼び出し：座標を使用して天気データを取得
    const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=precipitation,weathercode&timezone=auto,&hourly=precipitation_probability,temperature_2m`;
    const response = await fetch(weatherUrl);
    const data = (await response.json()) as {
      current: {
        time: string;
        precipitation: number;
        weathercode: number;
      };
      hourly: {
        precipitation_probability: number[];
        temperature_2m: number[];
      };
    };

    const forecast = {
      date: new Date().toISOString(),
      maxTemp: Math.max(...data.hourly.temperature_2m),
      minTemp: Math.min(...data.hourly.temperature_2m),
      condition: getWeatherCondition(data.current.weathercode),
      location: name,
      precipitationChance: data.hourly.precipitation_probability.reduce(
        (acc, curr) => Math.max(acc, curr),
        0,
      ),
    };

    return forecast;
  },
});

// ステップ2：エージェントを使用してアクティビティの推奨事項を生成するステップを作成
const planActivities = createStep({
  id: "plan-activities",
  description: "天候に基づいてアクティビティを提案します",
  inputSchema: forecastSchema,
  outputSchema: z.object({
    activities: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    const forecast = inputData;

    if (!forecast) {
      throw new Error("予報データが見つかりません");
    }

    const prompt = `${forecast.location}の以下の天気予報に基づいて、適切なアクティビティを提案してください：
      ${JSON.stringify(forecast, null, 2)}
      `;

    const agent = mastra?.getAgent("planningAgent");
    if (!agent) {
      throw new Error("プランニングエージェントが見つかりません");
    }

    const response = await agent.stream([
      {
        role: "user",
        content: prompt,
      },
    ]);

    let activitiesText = "";
    for await (const chunk of response.textStream) {
      process.stdout.write(chunk);
      activitiesText += chunk;
    }

    return {
      activities: activitiesText,
    };
  },
});

const activityPlanningWorkflow = createWorkflow({
  steps: [fetchWeather, planActivities],
  id: "activity-planning-step1-single-day",
  inputSchema: z.object({
    city: z.string().describe("天気を取得する都市"),
  }),
  outputSchema: z.object({
    activities: z.string(),
  }),
})
  .then(fetchWeather)
  .then(planActivities);

activityPlanningWorkflow.commit();

export { activityPlanningWorkflow };
```

## Mastraクラスにエージェントとワークフローのインスタンスを登録する

プランニングエージェントとアクティビティプランニングワークフローをmastraインスタンスに登録します。
これはアクティビティプランニングワークフロー内でプランニングエージェントへのアクセスを可能にするために重要です。

```ts showLineNumbers copy filename="index.ts"
import { Mastra } from "@mastra/core/mastra";
import { PinoLogger } from "@mastra/loggers";
import { activityPlanningWorkflow } from "./workflows/agent-workflow";
import { planningAgent } from "./agents/planning-agent";

// Create a new Mastra instance and register components
const mastra = new Mastra({
  vnext_workflows: {
    activityPlanningWorkflow,
  },
  agents: {
    planningAgent,
  },
  logger: new PinoLogger({
    name: "Mastra",
    level: "info",
  }),
});

export { mastra };
```

## アクティビティプランニングワークフローを実行する

ここでは、mastraインスタンスからアクティビティプランニングワークフローを取得し、実行を作成して、必要なinputDataで作成した実行を実行します。

```ts showLineNumbers copy filename="exec.ts"
import { mastra } from "./";

const workflow = mastra.vnext_getWorkflow("activityPlanningWorkflow");
const run = workflow.createRun();

// Start the workflow with New York as the city input
const result = await run.start({ inputData: { city: "New York" } });
console.dir(result, { depth: null });
```
