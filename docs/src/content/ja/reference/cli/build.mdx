---
title: "mastra build"
description: "本番環境へのデプロイのためにMastraプロジェクトをビルドする"
---

`mastra build`コマンドは、Mastraプロジェクトを本番環境用のHonoサーバーにバンドルします。Honoは、型安全なルーティングとミドルウェアのサポートを提供する軽量のWebフレームワークで、MastraエージェントをHTTPエンドポイントとしてデプロイするのに理想的です。

## 使用方法

```bash
mastra build [options]
```

## オプション

- `--dir <path>`: Mastraプロジェクトを含むディレクトリ（デフォルト：現在のディレクトリ）

## 高度な使用法

### 並列処理の制限

CIや、リソースが制限された環境で実行する場合、`MASTRA_CONCURRENCY`を設定することで、同時に実行される重いタスクの数を制限できます。

```bash
MASTRA_CONCURRENCY=2 mastra build
```

設定を解除すると、CLIはホストの性能に基づいて並列処理を行います。

### テレメトリの無効化

匿名のビルド分析をオプトアウトするには、以下を設定します：

```bash
MASTRA_TELEMETRY_DISABLED=1 mastra build
```

### カスタムプロバイダーエンドポイント

ビルド時には、`mastra dev`と同じ`OPENAI_BASE_URL`および`ANTHROPIC_BASE_URL`変数が尊重されます。これらはAI SDKによって、プロバイダーを呼び出すワークフローやツールに転送されます。

## 機能

1. Mastraのエントリーファイル（`src/mastra/index.ts`または`src/mastra/index.js`）を見つけます
2. `.mastra`出力ディレクトリを作成します
3. 以下の機能を備えたRollupを使用してコードをバンドルします：
   - 最適なバンドルサイズのためのツリーシェイキング
   - Node.js環境のターゲティング
   - デバッグ用のソースマップ生成

## 例

```bash
# Build from current directory
mastra build

# Build from specific directory
mastra build --dir ./my-mastra-project
```

## 出力

このコマンドは、`.mastra` ディレクトリに本番用バンドルを生成します。内容は以下の通りです：

- Mastra エージェントがエンドポイントとして公開された Hono ベースの HTTP サーバー
- 本番環境向けに最適化されたバンドル済み JavaScript ファイル
- デバッグ用のソースマップ
- 必要な依存関係

この出力は以下の用途に適しています：

- クラウドサーバー（EC2、Digital Ocean）へのデプロイ
- コンテナ化された環境での実行
- コンテナオーケストレーションシステムとの併用

## デプロイヤー

デプロイヤーを使用すると、ビルド出力が自動的に対象プラットフォーム向けに準備されます。例：

- [Vercel Deployer](/reference/deployer/vercel)
- [Netlify Deployer](/reference/deployer/netlify)
- [Cloudflare Deployer](/reference/deployer/cloudflare)
