---
title: "`mastra dev` リファレンス | ローカル開発 | Mastra CLI"
description: エージェント、ツール、ワークフローの開発サーバーを起動するmastra devコマンドのドキュメント。
---

# `mastra dev` リファレンス

`mastra dev` コマンドは、エージェント、ツール、ワークフローのRESTルートを公開する開発サーバーを起動します。

## パラメータ

<PropertiesTable
  content={[
    {
      name: "--dir",
      type: "string",
      description:
        "Mastraフォルダ（エージェント、ツール、ワークフローを含む）へのパスを指定します。デフォルトでは現在の作業ディレクトリになります。",
      isOptional: true,
    },
    {
      name: "--tools",
      type: "string",
      description:
        "登録する追加のツールディレクトリへのパスをカンマ区切りで指定します。例：'src/tools/dbTools,src/tools/scraperTools'。",
      isOptional: true,
    },
  ]}
/>

## ルート

`mastra dev`でサーバーを起動すると、デフォルトで一連のRESTルートが公開されます。

### システムルート

- **GET `/api`**: APIのステータスを取得します。

### エージェントルート

エージェントは`src/mastra/agents`からエクスポートされている必要があります。

- **GET `/api/agents`**: Mastraフォルダ内で登録されているエージェントの一覧を取得します。
- **GET `/api/agents/:agentId`**: IDでエージェントを取得します。
- **GET `/api/agents/:agentId/evals/ci`**: エージェントIDでCI評価を取得します。
- **GET `/api/agents/:agentId/evals/live`**: エージェントIDでライブ評価を取得します。
- **POST `/api/agents/:agentId/generate`**: 指定したエージェントにテキストベースのプロンプトを送信し、エージェントの応答を返します。
- **POST `/api/agents/:agentId/stream`**: エージェントからの応答をストリームします。
- **POST `/api/agents/:agentId/instructions`**: エージェントの指示を更新します。
- **POST `/api/agents/:agentId/instructions/enhance`**: 指示から改良されたシステムプロンプトを生成します。
- **GET `/api/agents/:agentId/speakers`**: エージェントで利用可能なスピーカーを取得します。
- **POST `/api/agents/:agentId/speak`**: エージェントの音声プロバイダーを使ってテキストを音声に変換します。
- **POST `/api/agents/:agentId/listen`**: エージェントの音声プロバイダーを使って音声をテキストに変換します。
- **POST `/api/agents/:agentId/tools/:toolId/execute`**: エージェントを通じてツールを実行します。

### ツールルート

ツールは`src/mastra/tools`（または設定されたツールディレクトリ）からエクスポートされている必要があります。

- **GET `/api/tools`**: すべてのツールを取得します。
- **GET `/api/tools/:toolId`**: IDでツールを取得します。
- **POST `/api/tools/:toolId/execute`**: 指定したツール名でツールを呼び出し、リクエストボディで入力データを渡します。

### ワークフロールート

ワークフローは`src/mastra/workflows`（または設定されたワークフローディレクトリ）からエクスポートされている必要があります。

- **GET `/api/workflows`**: すべてのワークフローを取得します。
- **GET `/api/workflows/:workflowId`**: IDでワークフローを取得します。
- **POST `/api/workflows/:workflowName/start`**: 指定したワークフローを開始します。
- **POST `/api/workflows/:workflowName/:instanceId/event`**: 既存のワークフローインスタンスにイベントやトリガーシグナルを送信します。
- **GET `/api/workflows/:workflowName/:instanceId/status`**: 実行中のワークフローインスタンスのステータス情報を返します。
- **POST `/api/workflows/:workflowId/resume`**: 一時停止中のワークフローステップを再開します。
- **POST `/api/workflows/:workflowId/resume-async`**: 一時停止中のワークフローステップを非同期で再開します。
- **POST `/api/workflows/:workflowId/createRun`**: 新しいワークフローランを作成します。
- **POST `/api/workflows/:workflowId/start-async`**: ワークフローを非同期で実行・開始します。
- **GET `/api/workflows/:workflowId/watch`**: ワークフローの遷移をリアルタイムで監視します。

### メモリルート

- **GET `/api/memory/status`**: メモリのステータスを取得します。
- **GET `/api/memory/threads`**: すべてのスレッドを取得します。
- **GET `/api/memory/threads/:threadId`**: IDでスレッドを取得します。
- **GET `/api/memory/threads/:threadId/messages`**: スレッドのメッセージを取得します。
- **POST `/api/memory/threads`**: 新しいスレッドを作成します。
- **PATCH `/api/memory/threads/:threadId`**: スレッドを更新します。
- **DELETE `/api/memory/threads/:threadId`**: スレッドを削除します。
- **POST `/api/memory/save-messages`**: メッセージを保存します。

### テレメトリルート

- **GET `/api/telemetry`**: すべてのトレースを取得します。

### ログルート

- **GET `/api/logs`**: すべてのログを取得します。
- **GET `/api/logs/transports`**: すべてのログトランスポートの一覧。
- **GET `/api/logs/:runId`**: 実行IDでログを取得します。

### ベクトルルート

- **POST `/api/vector/:vectorName/upsert`**: ベクターをインデックスにアップサートします。
- **POST `/api/vector/:vectorName/create-index`**: 新しいベクターインデックスを作成します。
- **POST `/api/vector/:vectorName/query`**: インデックスからベクターをクエリします。
- **GET `/api/vector/:vectorName/indexes`**: ベクターストア内のすべてのインデックスを一覧表示します。
- **GET `/api/vector/:vectorName/indexes/:indexName`**: 特定のインデックスの詳細を取得します。
- **DELETE `/api/vector/:vectorName/indexes/:indexName`**: 特定のインデックスを削除します。

### OpenAPI仕様

- **GET `/openapi.json`**: プロジェクトのルート用に自動生成されたOpenAPI仕様を返します。
- **GET `/swagger-ui`**: APIドキュメント用のSwagger UIにアクセスします。

## 追加の注意事項

ポートのデフォルトは4111です。ポートとホスト名の両方はmastraサーバー設定で構成できます。設定の詳細については[開発サーバーの起動](/docs/local-dev/mastra-dev#launch-development-server)を参照してください。

使用するプロバイダー（例：`OPENAI_API_KEY`、`ANTHROPIC_API_KEY`など）の環境変数が`.env.development`または`.env`ファイルに設定されていることを確認してください。

Mastraフォルダ内の`index.ts`ファイルが、開発サーバーが読み取るためのMastraインスタンスをエクスポートしていることを確認してください。

### リクエスト例

`mastra dev`を実行した後にエージェントをテストするには：

```bash
curl -X POST http://localhost:4111/api/agents/myAgent/generate \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      { "role": "user", "content": "Hello, how can you assist me today?" }
    ]
  }'
```

## 高度な使用方法

`mastra dev`サーバーは、開発中に便利ないくつかの追加環境変数に従います：

### ビルドキャッシュの無効化

`MASTRA_DEV_NO_CACHE=1`を設定すると、`.mastra/`以下のキャッシュされたアセットを使用する代わりに、完全な再ビルドを強制します：

```bash
MASTRA_DEV_NO_CACHE=1 mastra dev
```

これは、バンドラープラグインをデバッグしている場合や、古い出力が疑われる場合に役立ちます。

### 並列処理の制限

`MASTRA_CONCURRENCY`は、並列で実行される高負荷の操作（主にビルドと評価ステップ）の数を制限します。例えば：

```bash
MASTRA_CONCURRENCY=4 mastra dev
```

未設定のままにすると、CLIがマシンに適した既定値を選択します。

### カスタムプロバイダーエンドポイント

Vercel AI SDKでサポートされているプロバイダーを使用する場合、ベースURLを設定することで、リクエストをプロキシや内部ゲートウェイを通じてリダイレクトできます。OpenAIの場合：

```bash
OPENAI_API_KEY=sk-this_key \
OPENAI_BASE_URL=https://openrouter.example/v1 \
mastra dev
```

そしてAnthropicの場合：

```bash
ANTHROPIC_API_KEY=sk-anthropic42 \
ANTHROPIC_BASE_URL=https://anthropic.internal \
mastra dev
```

これらはAI SDKによって転送され、`openai()`または`anthropic()`呼び出しで動作します。

### テレメトリの無効化

匿名のCLI分析をオプトアウトするには、`MASTRA_TELEMETRY_DISABLED=1`を設定します。これにより、ローカルプレイグラウンド内のトラッキングも防止されます。

```bash
MASTRA_TELEMETRY_DISABLED=1 mastra dev
```
