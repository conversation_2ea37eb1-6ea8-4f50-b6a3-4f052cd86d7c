---
title: "`mastra init` リファレンス | プロジェクト作成 | Mastra CLI"
description: インタラクティブなセットアップオプションで新しいMastraプロジェクトを作成する「mastra init」コマンドのドキュメント。
---

# `mastra init` リファレンス

## `mastra init`

これは新しいMastraプロジェクトを作成します。以下の3つの方法で実行できます：

1. **インタラクティブモード（推奨）**
   フラグなしで実行すると、インタラクティブプロンプトが表示され、以下の手順が案内されます：

   - Mastraファイルを保存するディレクトリの選択
   - インストールするコンポーネント（エージェント、ツール、ワークフロー）の選択
   - デフォルトのLLMプロバイダー（OpenAI、Anthropic、またはGroq）の選択
   - サンプルコードを含めるかどうかの決定

2. **デフォルト設定でのクイックスタート**

   ```bash
   mastra init --default
   ```

   これにより、以下の設定でプロジェクトがセットアップされます：

   - ソースディレクトリ：`src/`
   - すべてのコンポーネント：エージェント、ツール、ワークフロー
   - OpenAIをデフォルトプロバイダーとして設定
   - サンプルコードなし

3. **カスタムセットアップ**
   ```bash
   mastra init --dir src/mastra --components agents,tools --llm openai --example
   ```
   オプション：
   - `-d, --dir`：Mastraファイルのディレクトリ（デフォルトはsrc/mastra）
   - `-c, --components`：コンポーネントのカンマ区切りリスト（agents, tools, workflows）
   - `-l, --llm`：デフォルトのモデルプロバイダー（openai, anthropic, groq, google または cerebras）
   - `-k, --llm-api-key`：選択したLLMプロバイダーのAPIキー（.envファイルに追加されます）
   - `-e, --example`：サンプルコードを含める
   - `-ne, --no-example`：サンプルコードをスキップする

## 高度な使用法

### 分析機能を無効にする

匿名の使用状況データを送信したくない場合は、コマンドを実行する際に
`MASTRA_TELEMETRY_DISABLED=1` 環境変数を設定してください：

```bash
MASTRA_TELEMETRY_DISABLED=1 mastra init
```

### カスタムプロバイダーエンドポイント

初期化されたプロジェクトは、`OPENAI_BASE_URL`および
`ANTHROPIC_BASE_URL`変数が存在する場合、それらを尊重します。これにより、後で開発サーバーを起動する際に、
プロバイダーのトラフィックをプロキシやプライベートゲートウェイを通じてルーティングすることができます。
