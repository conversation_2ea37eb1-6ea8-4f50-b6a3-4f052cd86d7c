---
title: Mastra クライアントワークフローAPI
description: client-js SDKを使用してMastraで自動化されたワークフローを操作および実行する方法を学びます。
---

# ワークフローAPI

ワークフローAPIは、Mastraの自動化されたワークフローを操作および実行するためのメソッドを提供します。

## Mastraクライアントの初期化

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## すべてのワークフローの取得

利用可能なすべてのワークフローのリストを取得します：

```typescript
const workflows = await client.getWorkflows();
```

## 特定のワークフローの操作

特定のワークフローのインスタンスを取得します：

```typescript
const workflow = client.getWorkflow("workflow-id");
```

## ワークフローメソッド

### ワークフローの詳細を取得

ワークフローの詳細情報を取得します：

```typescript
const details = await workflow.details();
```

### ワークフロー実行を非同期で開始

triggerData を使ってワークフロー実行を開始し、実行結果を待ちます：

```typescript
const { runId } = workflow.createRun();

const result = await workflow.startAsync({
  runId,
  triggerData: {
    param1: "value1",
    param2: "value2",
  },
});
```

### ワークフロー実行を非同期で再開

一時停止中のワークフローステップを再開し、実行結果を待ちます：

```typescript
const { runId } = createRun({ runId: prevRunId });

const result = await workflow.resumeAsync({
  runId,
  stepId: "step-id",
  contextData: { key: "value" },
});
```

### ワークフローの監視

ワークフローの遷移を監視します

```typescript
try {
  // Get workflow instance
  const workflow = client.getWorkflow("workflow-id");

  // Create a workflow run
  const { runId } = workflow.createRun();

  // Watch workflow run
  workflow.watch({ runId }, (record) => {
    // Every new record is the latest transition state of the workflow run

    console.log({
      activePaths: record.activePaths,
      results: record.results,
      timestamp: record.timestamp,
      runId: record.runId,
    });
  });

  // Start workflow run
  workflow.start({
    runId,
    triggerData: {
      city: "New York",
    },
  });
} catch (e) {
  console.error(e);
}
```

### ワークフローの再開

ワークフロー実行を再開し、ワークフローステップの遷移を監視します

```typescript
try {
  //To resume a workflow run, when a step is suspended
  const { run } = createRun({ runId: prevRunId });

  //Watch run
  workflow.watch({ runId }, (record) => {
    // Every new record is the latest transition state of the workflow run

    console.log({
      activePaths: record.activePaths,
      results: record.results,
      timestamp: record.timestamp,
      runId: record.runId,
    });
  });

  //resume run
  workflow.resume({
    runId,
    stepId: "step-id",
    contextData: { key: "value" },
  });
} catch (e) {
  console.error(e);
}
```

### ワークフロー実行結果

ワークフロー実行の結果は以下の内容を含みます：

| フィールド    | 型                                                                             | 説明                                                     |
| ------------- | ------------------------------------------------------------------------------ | -------------------------------------------------------- |
| `activePaths` | `Record<string, { status: string; suspendPayload?: any; stepPath: string[] }>` | ワークフロー内で現在アクティブなパスとその実行ステータス |
| `results`     | `CoreWorkflowRunResult<any, any, any>['results']`                              | ワークフロー実行からの結果                               |
| `timestamp`   | `number`                                                                       | この遷移が発生した Unix タイムスタンプ                   |
| `runId`       | `string`                                                                       | このワークフロー実行インスタンスの一意な識別子           |
