---
title: Mastraクライアントのエラー処理
description: Mastra client-js SDKに組み込まれた再試行メカニズムとエラー処理機能について学びます。
---

# エラー処理

Mastra Client SDKには、組み込みの再試行メカニズムとエラー処理機能が含まれています。

## エラー処理

すべてのAPIメソッドは、キャッチして処理できるエラーをスローする可能性があります：

```typescript
try {
  const agent = client.getAgent("agent-id");
  const response = await agent.generate({
    messages: [{ role: "user", content: "Hello" }],
  });
} catch (error) {
  console.error("An error occurred:", error.message);
}
```

## リトライメカニズム

クライアントは失敗したリクエストを指数バックオフで自動的に再試行します：

```typescript
const client = new MastraClient({
  baseUrl: "http://localhost:4111",
  retries: 3, // リトライ試行回数
  backoffMs: 300, // 初期バックオフ時間
  maxBackoffMs: 5000, // 最大バックオフ時間
});
```

### リトライの仕組み

1. 最初の試行が失敗 → 300ms待機
2. 2回目の試行が失敗 → 600ms待機
3. 3回目の試行が失敗 → 1200ms待機
4. 最終試行が失敗 → エラーをスロー
