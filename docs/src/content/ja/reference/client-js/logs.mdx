---
title: Mastra クライアントログ API
description: client-js SDKを使用してMastraでシステムログとデバッグ情報にアクセスし、クエリを実行する方法を学びます。
---

# ログ API

ログ API は、Mastra のシステムログとデバッグ情報にアクセスして照会するためのメソッドを提供します。

## Mastraクライアントの初期化

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## ログの取得

オプションのフィルタリングを使用してシステムログを取得します：

```typescript
const logs = await client.getLogs({
  transportId: "transport-1",
});
```

## 特定の実行ランのログを取得する

特定の実行ランのログを取得します：

```typescript
const runLogs = await client.getLogForRun({
  runId: "run-1",
  transportId: "transport-1",
});
```
