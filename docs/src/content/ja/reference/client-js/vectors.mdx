---
title: Mastra クライアントベクトル API
description: client-js SDKを使用して、セマンティック検索や類似性マッチングのためのベクトル埋め込みをMastraで操作する方法を学びます。
---

# Vectors API

Vectors APIは、Mastraでのセマンティック検索や類似性マッチングのためのベクトル埋め込みを操作するメソッドを提供します。

## Mastraクライアントの初期化

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## ベクトルの操作

ベクトルストアのインスタンスを取得する：

```typescript
const vector = client.getVector("vector-name");
```

## ベクトルメソッド

### ベクトルインデックスの詳細を取得

特定のベクトルインデックスに関する情報を取得します：

```typescript
const details = await vector.details("index-name");
```

### ベクトルインデックスの作成

新しいベクトルインデックスを作成します：

```typescript
const result = await vector.createIndex({
  indexName: "new-index",
  dimension: 128,
  metric: "cosine", // 'cosine', 'euclidean', または 'dotproduct'
});
```

### ベクトルのアップサート

インデックスにベクトルを追加または更新します：

```typescript
const ids = await vector.upsert({
  indexName: "my-index",
  vectors: [
    [0.1, 0.2, 0.3], // 最初のベクトル
    [0.4, 0.5, 0.6], // 2番目のベクトル
  ],
  metadata: [{ label: "first" }, { label: "second" }],
  ids: ["id1", "id2"], // オプション：カスタムID
});
```

### ベクトルのクエリ

類似したベクトルを検索します：

```typescript
const results = await vector.query({
  indexName: "my-index",
  queryVector: [0.1, 0.2, 0.3],
  topK: 10,
  filter: { label: "first" }, // オプション：メタデータフィルター
  includeVector: true, // オプション：結果にベクトルを含める
});
```

### すべてのインデックスを取得

利用可能なすべてのインデックスを一覧表示します：

```typescript
const indexes = await vector.getIndexes();
```

### インデックスの削除

ベクトルインデックスを削除します：

```typescript
const result = await vector.delete("index-name");
```
