---
title: Mastra クライアントエージェント API
description: Mastra AIエージェントとの対話方法を学びましょう。レスポンスの生成、ストリーミング対話、client-js SDKを使用したエージェントツールの管理などが含まれます。
---

# Agents API

Agents APIは、Mastra AIエージェントと対話するためのメソッドを提供し、レスポンスの生成、ストリーミング対話、エージェントツールの管理などが含まれます。

## Mastraクライアントの初期化

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## すべてのエージェントの取得

利用可能なすべてのエージェントのリストを取得します：

```typescript
const agents = await client.getAgents();
```

## 特定のエージェントの操作

特定のエージェントのインスタンスを取得する：

```typescript
const agent = client.getAgent("agent-id");
```

## エージェントメソッド

### エージェントの詳細を取得

エージェントの詳細情報を取得します：

```typescript
const details = await agent.details();
```

### 応答を生成

エージェントから応答を生成します：

```typescript
const response = await agent.generate({
  messages: [
    {
      role: "user",
      content: "Hello, how are you?",
    },
  ],
  threadId: "thread-1", // オプション: 会話コンテキスト用のスレッドID
  resourceid: "resource-1", // オプション: リソースID
  output: {}, // オプション: 出力設定
});
```

### 応答をストリーム

リアルタイムのやり取りのために、エージェントからの応答をストリーミングします：

```typescript
const response = await agent.stream({
  messages: [
    {
      role: "user",
      content: "Tell me a story",
    },
  ],
});

// processDataStreamユーティリティでデータストリームを処理
response.processDataStream({
  onTextPart: (text) => {
    process.stdout.write(text);
  },
  onFilePart: (file) => {
    console.log(file);
  },
  onDataPart: (data) => {
    console.log(data);
  },
  onErrorPart: (error) => {
    console.error(error);
  },
});

// response bodyから直接読み取ることも可能です
const reader = response.body.getReader();
while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  console.log(new TextDecoder().decode(value));
}
```

### エージェントツールを取得

エージェントが利用可能な特定のツールの情報を取得します：

```typescript
const tool = await agent.getTool("tool-id");
```

### エージェント評価を取得

エージェントの評価結果を取得します：

```typescript
// CI評価を取得
const evals = await agent.evals();

// ライブ評価を取得
const liveEvals = await agent.liveEvals();
```
