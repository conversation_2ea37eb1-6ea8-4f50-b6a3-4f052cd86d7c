---
title: Mastra クライアントメモリAPI
description: client-js SDKを使用してMastraで会話スレッドとメッセージ履歴を管理する方法を学びます。
---

# メモリーAPI

メモリーAPIは、Mastraでの会話スレッドとメッセージ履歴を管理するためのメソッドを提供します。

## Mastraクライアントの初期化

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## メモリスレッド操作

### すべてのスレッドを取得

特定のリソースに対するすべてのメモリスレッドを取得します：

```typescript
const threads = await client.getMemoryThreads({
  resourceId: "resource-1",
  agentId: "agent-1",
});
```

### 新しいスレッドを作成

新しいメモリスレッドを作成します：

```typescript
const thread = await client.createMemoryThread({
  title: "New Conversation",
  metadata: { category: "support" },
  resourceid: "resource-1",
  agentId: "agent-1",
});
```

### 特定のスレッドを操作する

特定のメモリスレッドのインスタンスを取得します：

```typescript
const thread = client.getMemoryThread("thread-id", "agent-id");
```

## スレッドメソッド

### スレッド詳細の取得

特定のスレッドに関する詳細を取得します：

```typescript
const details = await thread.get();
```

### スレッドの更新

スレッドのプロパティを更新します：

```typescript
const updated = await thread.update({
  title: "Updated Title",
  metadata: { status: "resolved" },
  resourceid: "resource-1",
});
```

### スレッドの削除

スレッドとそのメッセージを削除します：

```typescript
await thread.delete();
```

## メッセージ操作

### メッセージの保存

メッセージをメモリに保存します：

```typescript
const savedMessages = await client.saveMessageToMemory({
  messages: [
    {
      role: "user",
      content: "Hello!",
      id: "1",
      threadId: "thread-1",
      createdAt: new Date(),
      type: "text",
    },
  ],
  agentId: "agent-1",
});
```

### メモリステータスの取得

メモリシステムのステータスを確認します：

```typescript
const status = await client.getMemoryStatus("agent-id");
```
