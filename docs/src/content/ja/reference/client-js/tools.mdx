---
title: Mastra クライアントツール API
description: client-js SDKを使用してMastraプラットフォームで利用可能なツールを操作および実行する方法を学びます。
---

# ツールAPI

ツールAPIは、Mastraプラットフォームで利用可能なツールを操作および実行するためのメソッドを提供します。

## Mastraクライアントの初期化

```typescript
import { MastraClient } from "@mastra/client-js";

const client = new MastraClient();
```

## すべてのツールの取得

利用可能なすべてのツールのリストを取得します：

```typescript
const tools = await client.getTools();
```

## 特定のツールの操作

特定のツールのインスタンスを取得する：

```typescript
const tool = client.getTool("tool-id");
```

## ツールメソッド

### ツールの詳細を取得

ツールの詳細情報を取得します：

```typescript
const details = await tool.details();
```

### ツールの実行

特定の引数でツールを実行します：

```typescript
const result = await tool.execute({
  args: {
    param1: "value1",
    param2: "value2",
  },
  threadId: "thread-1", // Optional: Thread context
  resourceid: "resource-1", // Optional: Resource identifier
});
```
