---
title: "リファレンス: createVectorQueryTool() | RAG | Mastra Tools ドキュメント"
description: Mastraのベクタークエリツールに関するドキュメント。フィルタリングと再ランキング機能を備えたベクターストアでのセマンティック検索を容易にします。
---

# createVectorQueryTool()

`createVectorQueryTool()` 関数は、ベクトルストアに対するセマンティック検索のためのツールを作成します。フィルタリング、再ランク付け、そして様々なベクトルストアバックエンドとの統合をサポートしています。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { createVectorQueryTool } from "@mastra/rag";

const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "vectorStoreName",
      type: "string",
      description:
        "クエリを実行するベクトルストアの名前（Mastraで設定されている必要があります）",
      isOptional: false,
    },
    {
      name: "indexName",
      type: "string",
      description: "ベクトルストア内のインデックス名",
      isOptional: false,
    },
    {
      name: "model",
      type: "EmbeddingModel",
      description: "ベクトル検索に使用する埋め込みモデル",
      isOptional: false,
    },
    {
      name: "reranker",
      type: "RerankConfig",
      description: "結果のリランキング用オプション",
      isOptional: true,
    },
    {
      name: "id",
      type: "string",
      description:
        "ツールのカスタムID（デフォルトは 'VectorQuery {vectorStoreName} {indexName} Tool'）",
      isOptional: true,
    },
    {
      name: "description",
      type: "string",
      description:
        "ツールのカスタム説明。デフォルト: 'ナレッジベースにアクセスして、ユーザーの質問に答えるために必要な情報を検索します'",
      isOptional: true,
    },
  ]}
/>

### RerankConfig

<PropertiesTable
  content={[
    {
      name: "model",
      type: "MastraLanguageModel",
      description: "リランキングに使用する言語モデル",
      isOptional: false,
    },
    {
      name: "options",
      type: "RerankerOptions",
      description: "リランキング処理のオプション",
      isOptional: true,
      properties: [
        {
          type: "object",
          parameters: [
            {
              name: "weights",
              description:
                "スコアリング要素の重み（semantic: 0.4, vector: 0.4, position: 0.2）",
              isOptional: true,
              type: "WeightConfig",
            },
            {
              name: "topK",
              description: "返す上位結果の数",
              isOptional: true,
              type: "number",
              defaultValue: "3",
            },
          ],
        },
      ],
    },
  ]}
/>

## 戻り値

このツールは以下のオブジェクトを返します：

<PropertiesTable
  content={[
    {
      name: "relevantContext",
      type: "string",
      description: "最も関連性の高いドキュメントチャンクから結合されたテキスト",
    },
  ]}
/>

## デフォルトツールの説明

デフォルトの説明は以下に重点を置いています：

- 保存された知識から関連情報を見つけること
- ユーザーの質問に答えること
- 事実に基づく内容を取得すること

## 結果の処理

このツールは、ユーザーのクエリに基づいて返す結果の数を決定し、デフォルトでは10件の結果を返します。これはクエリの要件に応じて調整することができます。

## フィルター付きの例

```typescript
const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  enableFilter: true,
});
```

フィルタリングが有効になっている場合、このツールはクエリを処理し、セマンティック検索と組み合わせるメタデータフィルターを構築します。プロセスは次のように動作します：

1. ユーザーが「'version' フィールドが2.0より大きいコンテンツを探す」など、特定のフィルター条件を含むクエリを行います。
2. エージェントがクエリを解析し、適切なフィルターを構築します：
   ```typescript
   {
      "version": { "$gt": 2.0 }
   }
   ```

このエージェント駆動型のアプローチは以下のことを行います：

- 自然言語のクエリをフィルター仕様に変換
- ベクトルストア固有のフィルター構文を実装
- クエリ用語をフィルター演算子に変換

詳細なフィルター構文やストア固有の機能については、[Metadata Filters](../rag/metadata-filters) のドキュメントをご覧ください。

エージェント駆動型フィルタリングの動作例については、[Agent-Driven Metadata Filtering](../../../examples/rag/usage/filter-rag) の例をご参照ください。

## リランキング付きの例

```typescript
const queryTool = createVectorQueryTool({
  vectorStoreName: "milvus",
  indexName: "documentation",
  model: openai.embedding("text-embedding-3-small"),
  reranker: {
    model: openai("gpt-4o-mini"),
    options: {
      weights: {
        semantic: 0.5, // Semantic relevance weight
        vector: 0.3, // Vector similarity weight
        position: 0.2, // Original position weight
      },
      topK: 5,
    },
  },
});
```

リランキングは、以下を組み合わせることで結果の品質を向上させます：

- セマンティック関連性：LLMベースのテキスト類似度スコアを使用
- ベクトル類似度：元のベクトル距離スコア
- ポジションバイアス：元の結果の順序を考慮
- クエリ分析：クエリの特徴に基づく調整

リランカーは初期のベクトル検索結果を処理し、関連性を最適化した順序でリストを返します。

## カスタム説明付きの例

```typescript
const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  description:
    "Search through document archives to find relevant information for answering questions about company policies and procedures",
});
```

この例では、情報検索という基本的な目的を維持しつつ、特定のユースケースに合わせてツールの説明をカスタマイズする方法を示しています。

## ツールの詳細

このツールは以下のように作成されています：

- **ID**: `VectorQuery {vectorStoreName} {indexName} Tool`
- **入力スキーマ**: queryText と filter オブジェクトが必要
- **出力スキーマ**: relevantContext 文字列を返す

## 関連

- [rerank()](../rag/rerank)
- [createGraphRAGTool](./graph-rag-tool)
