---
title: "リファレンス: createGraphRAGTool() | RAG | Mastra Tools ドキュメント"
description: Mastra の Graph RAG Tool のドキュメント。これは、ドキュメント間のセマンティックな関係のグラフを構築することで RAG を強化します。
---

# createGraphRAGTool()

`createGraphRAGTool()` は、ドキュメント間のセマンティックな関係のグラフを構築することでRAGを強化するツールを作成します。内部的には `GraphRAG` システムを利用してグラフベースの検索を提供し、直接的な類似性だけでなく、関連する接続関係を通じて適切なコンテンツを見つけ出します。

## 使用例

```typescript
import { openai } from "@ai-sdk/openai";
import { createGraphRAGTool } from "@mastra/rag";

const graphTool = createGraphRAGTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  graphOptions: {
    dimension: 1536,
    threshold: 0.7,
    randomWalkSteps: 100,
    restartProb: 0.15,
  },
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "vectorStoreName",
      type: "string",
      description: "クエリを実行するベクトルストアの名前",
      isOptional: false,
    },
    {
      name: "indexName",
      type: "string",
      description: "ベクトルストア内のインデックス名",
      isOptional: false,
    },
    {
      name: "model",
      type: "EmbeddingModel",
      description: "ベクトル検索に使用する埋め込みモデル",
      isOptional: false,
    },
    {
      name: "graphOptions",
      type: "GraphOptions",
      description: "グラフベースの検索のための設定",
      isOptional: true,
      defaultValue: "デフォルトのグラフオプション",
    },
    {
      name: "description",
      type: "string",
      description:
        "ツールのカスタム説明。デフォルト: 'ナレッジベース内の情報間の関係性をアクセス・分析し、接続やパターンに関する複雑な質問に答えます'",
      isOptional: true,
    },
  ]}
/>

### GraphOptions

<PropertiesTable
  content={[
    {
      name: "dimension",
      type: "number",
      description: "埋め込みベクトルの次元数",
      isOptional: true,
      defaultValue: "1536",
    },
    {
      name: "threshold",
      type: "number",
      description: "ノード間のエッジ作成のための類似度しきい値（0-1）",
      isOptional: true,
      defaultValue: "0.7",
    },
    {
      name: "randomWalkSteps",
      type: "number",
      description: "グラフ探索のためのランダムウォークのステップ数",
      isOptional: true,
      defaultValue: "100",
    },
    {
      name: "restartProb",
      type: "number",
      description: "クエリノードからランダムウォークを再開する確率",
      isOptional: true,
      defaultValue: "0.15",
    },
  ]}
/>

## 戻り値

このツールは以下のオブジェクトを返します：

<PropertiesTable
  content={[
    {
      name: "relevantContext",
      type: "string",
      description:
        "グラフベースのランキングを用いて取得した、最も関連性の高いドキュメントチャンクから結合されたテキスト",
    },
  ]}
/>

## デフォルトツールの説明

デフォルトの説明は以下に重点を置いています：

- ドキュメント間の関係性の分析
- パターンやつながりの発見
- 複雑なクエリへの回答

## 応用例

```typescript
const graphTool = createGraphRAGTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  graphOptions: {
    dimension: 1536,
    threshold: 0.8, // Higher similarity threshold
    randomWalkSteps: 200, // More exploration steps
    restartProb: 0.2, // Higher restart probability
  },
});
```

## カスタム説明付きの例

```typescript
const graphTool = createGraphRAGTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  description:
    "Analyze document relationships to find complex patterns and connections in our company's historical data",
});
```

この例では、関係性分析という基本的な目的を維持しつつ、特定のユースケースに合わせてツールの説明をカスタマイズする方法を示しています。

## 関連

- [createVectorQueryTool](./vector-query-tool)
- [GraphRAG](../rag/graph-rag)
