---
title: "リファレンス: MastraMCPClient | ツールディスカバリー | Mastra ドキュメント"
description: MastraMCPClient の API リファレンス - Model Context Protocol 用のクライアント実装。
---

# MastraMCPClient（非推奨）

`MastraMCPClient` クラスは、Model Context Protocol（MCP）サーバーとやり取りするためのクライアント実装を提供します。このクラスは、MCPプロトコルを通じて接続管理、リソースの発見、およびツールの実行を行います。

## 非推奨のお知らせ

`MastraMCPClient` は [`MCPClient`](./mcp-client) への移行に伴い非推奨となります。単一のMCPサーバーと複数のMCPサーバーを管理するために異なるインターフェースを用意するのではなく、たとえ単一のMCPサーバーを使用する場合でも複数サーバー管理用のインターフェースを推奨することにしました。

## コンストラクター

MastraMCPClient の新しいインスタンスを作成します。

```typescript
constructor({
    name,
    version = '1.0.0',
    server,
    capabilities = {},
    timeout = 60000,
}: {
    name: string;
    server: MastraMCPServerDefinition;
    capabilities?: ClientCapabilities;
    version?: string;
    timeout?: number;
})
```

### パラメーター

<br />
<PropertiesTable
  content={[
    {
      name: "name",
      type: "string",
      description: "このクライアントインスタンスの名前識別子。",
    },
    {
      name: "version",
      type: "string",
      isOptional: true,
      defaultValue: "1.0.0",
      description: "クライアントのバージョン。",
    },
    {
      name: "server",
      type: "MastraMCPServerDefinition",
      description:
        "stdio サーバー接続または SSE サーバー接続のいずれかの構成パラメーター。ログハンドラーやサーバーログの設定も含めることができます。",
    },
    {
      name: "capabilities",
      type: "ClientCapabilities",
      isOptional: true,
      defaultValue: "{}",
      description: "クライアントのオプション機能設定。",
    },
    {
      name: "timeout",
      type: "number",
      isOptional: true,
      defaultValue: 60000,
      description:
        "クライアントツール呼び出しのタイムアウト時間（ミリ秒単位）。",
    },
  ]}
/>

### MastraMCPServerDefinition

MCP サーバーはこの定義を使って設定できます。クライアントは、指定されたパラメーターに基づいて自動的にトランスポートタイプを検出します：

- `command` が指定されている場合、Stdio トランスポートを使用します。
- `url` が指定されている場合、まず Streamable HTTP トランスポートを試み、初回接続に失敗した場合はレガシー SSE トランスポートにフォールバックします。

<br />
<PropertiesTable
  content={[
    {
      name: "command",
      type: "string",
      isOptional: true,
      description: "Stdio サーバー用：実行するコマンド。",
    },
    {
      name: "args",
      type: "string[]",
      isOptional: true,
      description: "Stdio サーバー用：コマンドに渡す引数。",
    },
    {
      name: "env",
      type: "Record<string, string>",
      isOptional: true,
      description: "Stdio サーバー用：コマンドに設定する環境変数。",
    },
    {
      name: "url",
      type: "URL",
      isOptional: true,
      description:
        "HTTP サーバー（Streamable HTTP または SSE）用：サーバーの URL。",
    },
    {
      name: "requestInit",
      type: "RequestInit",
      isOptional: true,
      description: "HTTP サーバー用：fetch API のリクエスト設定。",
    },
    {
      name: "eventSourceInit",
      type: "EventSourceInit",
      isOptional: true,
      description:
        "SSE フォールバック用：SSE 接続のためのカスタム fetch 設定。SSE でカスタムヘッダーを使用する場合に必須です。",
    },
    {
      name: "logger",
      type: "LogHandler",
      isOptional: true,
      description: "オプションの追加ログハンドラー。",
    },
    {
      name: "timeout",
      type: "number",
      isOptional: true,
      description: "サーバー固有のタイムアウト（ミリ秒単位）。",
    },
    {
      name: "capabilities",
      type: "ClientCapabilities",
      isOptional: true,
      description: "サーバー固有の機能設定。",
    },
    {
      name: "enableServerLogs",
      type: "boolean",
      isOptional: true,
      defaultValue: "true",
      description: "このサーバーのログを有効にするかどうか。",
    },
  ]}
/>

### LogHandler

`LogHandler` 関数は `LogMessage` オブジェクトをパラメーターとして受け取り、void を返します。`LogMessage` オブジェクトには以下のプロパティがあります。`LoggingLevel` 型は `debug`、`info`、`warn`、`error` の値を持つ文字列の列挙型です。

<br />
<PropertiesTable
  content={[
    {
      name: "level",
      type: "LoggingLevel",
      description: "ログレベル（debug, info, warn, error）",
    },
    {
      name: "message",
      type: "string",
      description: "ログメッセージの内容",
    },
    {
      name: "timestamp",
      type: "Date",
      description: "ログが生成された日時",
    },
    {
      name: "serverName",
      type: "string",
      description: "ログを生成したサーバー名",
    },
    {
      name: "details",
      type: "Record<string, any>",
      isOptional: true,
      description: "オプションの追加ログ詳細",
    },
  ]}
/>

## メソッド

### connect()

MCPサーバーへの接続を確立します。

```typescript
async connect(): Promise<void>
```

### disconnect()

MCPサーバーとの接続を切断します。

```typescript
async disconnect(): Promise<void>
```

### resources()

サーバーから利用可能なリソースの一覧を取得します。

```typescript
async resources(): Promise<ListResourcesResult>
```

### tools()

サーバーから利用可能なツールを取得し、初期化してMastra互換のツール形式に変換します。

```typescript
async tools(): Promise<Record<string, Tool>>
```

ツール名を対応するMastraツール実装にマッピングしたオブジェクトを返します。

## 例

### Mastra Agentとの併用

#### Stdioサーバーを使った例

```typescript
import { Agent } from "@mastra/core/agent";
import { MastraMCPClient } from "@mastra/mcp";
import { openai } from "@ai-sdk/openai";

// Initialize the MCP client using mcp/fetch as an example https://hub.docker.com/r/mcp/fetch
// Visit https://github.com/docker/mcp-servers for other reference docker mcp servers
const fetchClient = new MastraMCPClient({
  name: "fetch",
  server: {
    command: "docker",
    args: ["run", "-i", "--rm", "mcp/fetch"],
    logger: (logMessage) => {
      console.log(`[${logMessage.level}] ${logMessage.message}`);
    },
  },
});

// Create a Mastra Agent
const agent = new Agent({
  name: "Fetch agent",
  instructions:
    "You are able to fetch data from URLs on demand and discuss the response data with the user.",
  model: openai("gpt-4o-mini"),
});

try {
  // Connect to the MCP server
  await fetchClient.connect();

  // Gracefully handle process exits so the docker subprocess is cleaned up
  process.on("exit", () => {
    fetchClient.disconnect();
  });

  // Get available tools
  const tools = await fetchClient.tools();

  // Use the agent with the MCP tools
  const response = await agent.generate(
    "Tell me about mastra.ai/docs. Tell me generally what this page is and the content it includes.",
    {
      toolsets: {
        fetch: tools,
      },
    },
  );

  console.log("\n\n" + response.text);
} catch (error) {
  console.error("Error:", error);
} finally {
  // Always disconnect when done
  await fetchClient.disconnect();
}
```

### SSEサーバーを使った例

```typescript
// Initialize the MCP client using an SSE server
const sseClient = new MastraMCPClient({
  name: "sse-client",
  server: {
    url: new URL("https://your-mcp-server.com/sse"),
    // Optional fetch request configuration - Note: requestInit alone isn't enough for SSE
    requestInit: {
      headers: {
        Authorization: "Bearer your-token",
      },
    },
    // Required for SSE connections with custom headers
    eventSourceInit: {
      fetch(input: Request | URL | string, init?: RequestInit) {
        const headers = new Headers(init?.headers || {});
        headers.set("Authorization", "Bearer your-token");
        return fetch(input, {
          ...init,
          headers,
        });
      },
    },
    // Optional additional logging configuration
    logger: (logMessage) => {
      console.log(
        `[${logMessage.level}] ${logMessage.serverName}: ${logMessage.message}`,
      );
    },
    // Disable server logs
    enableServerLogs: false,
  },
});

// The rest of the usage is identical to the stdio example
```

### SSE認証に関する重要な注意点

SSE接続で認証やカスタムヘッダーを使用する場合、`requestInit`と`eventSourceInit`の両方を設定する必要があります。これは、SSE接続がブラウザのEventSource APIを使用しており、カスタムヘッダーを直接サポートしていないためです。

`eventSourceInit`の設定により、SSE接続で使用される内部のfetchリクエストをカスタマイズでき、認証ヘッダーが正しく含まれるようになります。
`eventSourceInit`がない場合、`requestInit`で指定した認証ヘッダーは接続リクエストに含まれず、401 Unauthorizedエラーが発生します。

## 関連情報

- アプリケーションで複数のMCPサーバーを管理する場合は、[MCPClientのドキュメント](./mcp-client)をご覧ください
- Model Context Protocolの詳細については、[@modelcontextprotocol/sdkのドキュメント](https://github.com/modelcontextprotocol/typescript-sdk)をご参照ください。
