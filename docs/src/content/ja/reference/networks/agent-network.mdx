---
title: "AgentNetwork（実験的）"
description: "AgentNetworkクラスのリファレンスドキュメント"
---

# AgentNetwork（実験的）

> **注意:** AgentNetwork機能は実験的であり、今後のリリースで変更される可能性があります。

`AgentNetwork` クラスは、複雑なタスクを解決するために協力できる専門的なエージェントのネットワークを作成する方法を提供します。Workflowsが実行経路を明示的に制御する必要があるのに対し、AgentNetworkはLLMベースのルーターを使用して、次にどのエージェントを呼び出すかを動的に決定します。

## 主要な概念

- **LLMベースのルーティング**: AgentNetworkは、エージェントの最適な活用方法を判断するためにLLMのみを使用します
- **エージェントの協働**: 複数の専門エージェントが協力して複雑なタスクを解決できます
- **動的な意思決定**: ルーターがタスクの要件に基づいてどのエージェントを呼び出すかを決定します

## 使い方

```typescript
import { AgentNetwork } from "@mastra/core/network";
import { openai } from "@mastra/openai";

// Create specialized agents
const webSearchAgent = new Agent({
  name: "Web Search Agent",
  instructions: "You search the web for information.",
  model: openai("gpt-4o"),
  tools: {
    /* web search tools */
  },
});

const dataAnalysisAgent = new Agent({
  name: "Data Analysis Agent",
  instructions: "You analyze data and provide insights.",
  model: openai("gpt-4o"),
  tools: {
    /* data analysis tools */
  },
});

// Create the network
const researchNetwork = new AgentNetwork({
  name: "Research Network",
  instructions: "Coordinate specialized agents to research topics thoroughly.",
  model: openai("gpt-4o"),
  agents: [webSearchAgent, dataAnalysisAgent],
});

// Use the network
const result = await researchNetwork.generate(
  "Research the impact of climate change on agriculture",
);
console.log(result.text);
```

## コンストラクター

```typescript
constructor(config: AgentNetworkConfig)
```

### パラメーター

- `config`: AgentNetworkのための設定オブジェクト
  - `name`: ネットワークの名前
  - `instructions`: ルーティングエージェントへの指示
  - `model`: ルーティングに使用する言語モデル
  - `agents`: ネットワーク内の専門エージェントの配列

## メソッド

### generate()

エージェントネットワークを使用して応答を生成します。このメソッドは、コードベース全体の一貫性のために非推奨となった `run()` メソッドの代わりとなります。

```typescript
async generate(
  messages: string | string[] | CoreMessage[],
  args?: AgentGenerateOptions
): Promise<GenerateTextResult>
```

### stream()

エージェントネットワークを使用して応答をストリーミングします。

```typescript
async stream(
  messages: string | string[] | CoreMessage[],
  args?: AgentStreamOptions
): Promise<StreamTextResult>
```

### getRoutingAgent()

ネットワークで使用されているルーティングエージェントを返します。

```typescript
getRoutingAgent(): Agent
```

### getAgents()

ネットワーク内の専門エージェントの配列を返します。

```typescript
getAgents(): Agent[]
```

### getAgentHistory()

特定のエージェントに対するやり取りの履歴を返します。

```typescript
getAgentHistory(agentId: string): Array<{
  input: string;
  output: string;
  timestamp: string;
}>
```

### getAgentInteractionHistory()

ネットワーク内で発生したすべてのエージェントのやり取り履歴を返します。

```typescript
getAgentInteractionHistory(): Record<
  string,
  Array<{
    input: string;
    output: string;
    timestamp: string;
  }>
>
```

### getAgentInteractionSummary()

エージェントのやり取りを時系列でまとめたフォーマット済みのサマリーを返します。

```typescript
getAgentInteractionSummary(): string
```

## AgentNetwork と Workflows の使い分け

- **AgentNetwork を使う場合:** タスクの要件に基づいて、AI にエージェントの最適な使い方や動的なルーティングを任せたいとき。

- **Workflows を使う場合:** 実行経路を明確に制御したいときや、エージェントの呼び出し順序や条件分岐をあらかじめ決めておきたいとき。

## 内部ツール

AgentNetwork は、ルーティングエージェントが専門のエージェントを呼び出すことを可能にする特別な `transmit` ツールを使用します。このツールは以下を処理します：

- 単一エージェントの呼び出し
- 複数エージェントの並列呼び出し
- エージェント間のコンテキスト共有

## 制限事項

- AgentNetworkのアプローチは、同じタスクに対してよく設計されたWorkflowよりも多くのトークンを使用する場合があります
- ルーティングの判断がLLMによって行われるため、デバッグがより困難になることがあります
- パフォーマンスは、ルーティング指示の質や各専門エージェントの能力によって変動する可能性があります
