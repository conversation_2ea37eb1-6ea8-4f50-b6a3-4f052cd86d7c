---
title: "Cloudflare Storage | ストレージシステム | Mastra Core"
description: MastraにおけるCloudflare KVストレージ実装のドキュメント。
---

# Cloudflare Storage

Cloudflare KV ストレージの実装は、Cloudflare Workers KV を使用したグローバル分散型のサーバーレスなキー・バリュー・ストアソリューションを提供します。

## インストール

```bash copy
npm install @mastra/cloudflare@latest
```

## 使い方

```typescript copy showLineNumbers
import { CloudflareStore } from "@mastra/cloudflare";

// --- Example 1: Using Workers Binding ---
const storageWorkers = new CloudflareStore({
  bindings: {
    threads: THREADS_KV, // KVNamespace binding for threads table
    messages: MESSAGES_KV, // KVNamespace binding for messages table
    // Add other tables as needed
  },
  keyPrefix: "dev_", // Optional: isolate keys per environment
});

// --- Example 2: Using REST API ---
const storageRest = new CloudflareStore({
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID!, // Cloudflare Account ID
  apiToken: process.env.CLOUDFLARE_API_TOKEN!, // Cloudflare API Token
  namespacePrefix: "dev_", // Optional: isolate namespaces per environment
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "bindings",
      type: "Record<string, KVNamespace>",
      description:
        "Cloudflare Workers KV バインディング（Workers ランタイム用）",
      isOptional: true,
    },
    {
      name: "accountId",
      type: "string",
      description: "Cloudflare アカウントID（REST API用）",
      isOptional: true,
    },
    {
      name: "apiToken",
      type: "string",
      description: "Cloudflare APIトークン（REST API用）",
      isOptional: true,
    },
    {
      name: "namespacePrefix",
      type: "string",
      description:
        "すべてのネームスペース名に付与するオプションのプレフィックス（環境の分離に便利）",
      isOptional: true,
    },
    {
      name: "keyPrefix",
      type: "string",
      description:
        "すべてのキーに付与するオプションのプレフィックス（環境の分離に便利）",
      isOptional: true,
    },
  ]}
/>

#### 追加の注意事項

### スキーマ管理

このストレージ実装は、スキーマの作成と更新を自動的に処理します。以下のテーブルが作成されます：

- `threads`: 会話スレッドを保存します
- `messages`: 個々のメッセージを保存します
- `metadata`: スレッドやメッセージの追加メタデータを保存します

### 一貫性と伝播

Cloudflare KV は最終的な一貫性を持つストアであり、書き込み後すぐにすべてのリージョンでデータが利用可能になるとは限りません。

### キー構造とネームスペース

Cloudflare KV のキーは、設定可能なプレフィックスとテーブル固有のフォーマット（例：`threads:threadId`）の組み合わせで構成されています。
Workers デプロイメントでは、`keyPrefix` を使用してネームスペース内のデータを分離します。REST API デプロイメントでは、`namespacePrefix` を使用して環境やアプリケーション間でネームスペース全体を分離します。
