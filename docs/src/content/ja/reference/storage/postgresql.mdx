---
title: "PostgreSQLストレージ | ストレージシステム | Mastra Core"
description: MastraにおけるPostgreSQLストレージ実装のドキュメント。
---

# PostgreSQLストレージ

PostgreSQLストレージ実装は、PostgreSQLデータベースを使用した本番環境対応のストレージソリューションを提供します。

## インストール

```bash copy
npm install @mastra/pg@latest
```

## 使い方

```typescript copy showLineNumbers
import { PostgresStore } from "@mastra/pg";

const storage = new PostgresStore({
  connectionString: process.env.DATABASE_URL,
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "connectionString",
      type: "string",
      description:
        "PostgreSQL の接続文字列（例: ********************************/dbname）",
      isOptional: false,
    },
    {
      name: "schemaName",
      type: "string",
      description:
        "ストレージで使用したいスキーマ名。指定しない場合はデフォルトのスキーマが使用されます。",
      isOptional: true,
    },
  ]}
/>

## コンストラクタの例

`PostgresStore` は以下の方法でインスタンス化できます。

```ts
import { PostgresStore } from "@mastra/pg";

// 接続文字列のみを使用する場合
const store1 = new PostgresStore({
  connectionString: "postgresql://user:password@localhost:5432/mydb",
});

// 接続文字列とカスタムスキーマ名を使用する場合
const store2 = new PostgresStore({
  connectionString: "postgresql://user:password@localhost:5432/mydb",
  schemaName: "custom_schema", // オプション
});

// 個別の接続パラメータを使用する場合
const store4 = new PostgresStore({
  host: "localhost",
  port: 5432,
  database: "mydb",
  user: "user",
  password: "password",
});

// 個別パラメータと schemaName を使用する場合
const store5 = new PostgresStore({
  host: "localhost",
  port: 5432,
  database: "mydb",
  user: "user",
  password: "password",
  schemaName: "custom_schema", // オプション
});
```

## 追加の注意事項

### スキーマ管理

ストレージ実装はスキーマの作成と更新を自動的に処理します。以下のテーブルが作成されます：

- `threads`: 会話スレッドを保存します
- `messages`: 個々のメッセージを保存します
- `metadata`: スレッドやメッセージの追加メタデータを保存します
