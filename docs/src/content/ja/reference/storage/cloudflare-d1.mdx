---
title: "Cloudflare D1 ストレージ | ストレージシステム | Mastra Core"
description: Mastra における Cloudflare D1 SQL ストレージ実装のドキュメントです。
---

# Cloudflare D1 Storage

Cloudflare D1ストレージの実装は、Cloudflare D1を利用したサーバーレスのSQLデータベースソリューションを提供し、リレーショナル操作とトランザクションの一貫性をサポートします。

## インストール

```bash
npm install @mastra/cloudflare-d1@latest
```

## 使用方法

```typescript copy showLineNumbers
import { D1Store } from "@mastra/cloudflare-d1";

// --- Example 1: Using Workers Binding ---
const storageWorkers = new D1Store({
  binding: D1Database, // D1Database binding provided by the Workers runtime
  tablePrefix: "dev_", // Optional: isolate tables per environment
});

// --- Example 2: Using REST API ---
const storageRest = new D1Store({
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID!, // Cloudflare Account ID
  databaseId: process.env.CLOUDFLARE_D1_DATABASE_ID!, // D1 Database ID
  apiToken: process.env.CLOUDFLARE_API_TOKEN!, // Cloudflare API Token
  tablePrefix: "dev_", // Optional: isolate tables per environment
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "binding",
      type: "D1Database",
      description:
        "Cloudflare D1 Workers バインディング（Workers ランタイム用）",
      isOptional: true,
    },
    {
      name: "accountId",
      type: "string",
      description: "Cloudflare アカウント ID（REST API 用）",
      isOptional: true,
    },
    {
      name: "databaseId",
      type: "string",
      description: "Cloudflare D1 データベース ID（REST API 用）",
      isOptional: true,
    },
    {
      name: "apiToken",
      type: "string",
      description: "Cloudflare API トークン（REST API 用）",
      isOptional: true,
    },
    {
      name: "tablePrefix",
      type: "string",
      description:
        "すべてのテーブル名に付与する任意のプレフィックス（環境ごとの分離に便利）",
      isOptional: true,
    },
  ]}
/>

## 追加の注意事項

### スキーマ管理

このストレージ実装は、スキーマの作成と更新を自動的に処理します。以下のテーブルが作成されます：

- `threads`: 会話スレッドを保存します
- `messages`: 個々のメッセージを保存します
- `metadata`: スレッドやメッセージの追加メタデータを保存します

### トランザクションと一貫性

Cloudflare D1 は、単一行の操作に対してトランザクション保証を提供します。これにより、複数の操作をすべて成功するかすべて失敗するかの単位でまとめて実行できます。

### テーブル作成とマイグレーション

テーブルはストレージの初期化時に自動的に作成されます（`tablePrefix` オプションを使って環境ごとに分離することも可能です）が、カラムの追加やデータ型の変更、インデックスの修正などの高度なスキーマ変更には、手動でのマイグレーションとデータ損失を避けるための慎重な計画が必要です。
