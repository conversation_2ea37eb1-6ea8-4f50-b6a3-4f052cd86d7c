---
title: "Netlify Deployer"
description: "NetlifyDeployerクラスのドキュメント。MastraアプリケーションをNetlify Functionsにデプロイします。"
---

# NetlifyDeployer

NetlifyDeployerは、Mastraアプリケーションをネットリファイ・ファンクションにデプロイし、サイトの作成、設定、およびデプロイプロセスを処理します。抽象Deployerクラスを拡張して、Netlify固有のデプロイ機能を提供します。

## 使用例

```typescript
import { Mastra } from "@mastra/core";
import { NetlifyDeployer } from "@mastra/deployer-netlify";

const mastra = new Mastra({
  deployer: new NetlifyDeployer({
    scope: "your-team-slug",
    projectName: "your-project-name",
    token: "your-netlify-token",
  }),
  // ... other Mastra configuration options
});
```

## パラメーター

### コンストラクターのパラメーター

<PropertiesTable
  content={[
    {
      name: "scope",
      type: "string",
      description: "あなたのNetlifyチームのスラッグまたはID。",
      isOptional: false,
    },
    {
      name: "projectName",
      type: "string",
      description:
        "あなたのNetlifyサイトの名前（存在しない場合は作成されます）。",
      isOptional: false,
    },
    {
      name: "token",
      type: "string",
      description: "あなたのNetlify認証トークン。",
      isOptional: false,
    },
  ]}
/>

### 環境変数

NetlifyDeployerは複数のソースから環境変数を扱います：

1. **環境ファイル**: `.env.production` および `.env` ファイルからの変数。
2. **設定**: Mastraの設定を通じて渡された変数。
3. **Netlifyダッシュボード**: Netlifyのウェブインターフェースからも変数を管理できます。

## Mastraプロジェクトのリント

Mastraプロジェクトをリントして、ビルドに問題がないことを確認します

```bash
npx mastra lint
```

## Mastraプロジェクトのビルド

Netlifyへのデプロイ用にMastraプロジェクトをビルドするには、以下の手順を実行します。

```bash
npx mastra build
```

ビルドプロセスにより、`.mastra/output`ディレクトリに次のような出力構造が生成されます。

```
.mastra/output/
├── netlify/
│   └── functions/
│       └── api/
│           └── index.mjs    # アプリケーションのエントリーポイント
└── netlify.toml             # Netlifyの設定ファイル
```

### Netlifyの設定

NetlifyDeployerは、`.mastra/output`内に自動的に`netlify.toml`設定ファイルを次の内容で生成します。

```toml
[functions]
node_bundler = "esbuild"
directory = "netlify/functions"

[[redirects]]
force = true
from = "/*"
status = 200
to = "/.netlify/functions/api/:splat"
```

## デプロイメントオプション

ビルド後、Mastraアプリケーションの `.mastra/output` をNetlifyに以下のいずれかの方法でデプロイできます：

1. **Netlify CLI**: Netlifyの公式CLIツールを使って直接デプロイ

   - CLIをインストール: `npm install -g netlify-cli`
   - 出力ディレクトリに移動: `cd .mastra/output`
   - functionsディレクトリを指定してデプロイ: `netlify deploy --dir . --functions ./netlify/functions`
   - 本番環境にデプロイする場合は `--prod` フラグを追加: `netlify deploy --prod --dir . --functions ./netlify/functions`

2. **Netlifyダッシュボード**: Gitリポジトリを接続するか、Netlifyダッシュボードでビルド出力をドラッグ＆ドロップ

3. **Netlify Dev**: Netlifyの開発環境でMastraアプリケーションをローカルで実行

> 出力ディレクトリ `.mastra/output` で `netlify dev` を実行することで、Mastraアプリケーションをローカルでテストすることもできます。

## プラットフォームドキュメント

- [Netlify](https://docs.netlify.com/)
