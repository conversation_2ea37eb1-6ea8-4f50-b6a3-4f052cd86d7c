---
title: "リファレンス: createTool() | ツール | エージェント | Mastra ドキュメント"
description: <PERSON><PERSON> の createTool 関数のドキュメント。エージェントやワークフロー用のカスタムツールを作成します。
---

# `createTool()`

`createTool()` 関数は、エージェントやワークフローによって実行可能な型付きツールを作成します。ツールには、組み込みのスキーマバリデーション、実行コンテキスト、そしてMastraエコシステムとの統合が備わっています。

## 概要

ツールは、Mastraにおける基本的な構成要素であり、エージェントが外部システムと連携したり、計算を実行したり、データへアクセスしたりすることを可能にします。各ツールには以下の特徴があります。

- 一意の識別子
- AIがツールをいつ、どのように使うべきかを理解するための説明
- バリデーションのためのオプションの入力および出力スキーマ
- ツールのロジックを実装する実行関数

## 使用例

```ts filename="src/tools/stock-tools.ts" showLineNumbers copy
import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// Helper function to fetch stock data
const getStockPrice = async (symbol: string) => {
  const response = await fetch(
    `https://mastra-stock-data.vercel.app/api/stock-data?symbol=${symbol}`,
  );
  const data = await response.json();
  return data.prices["4. close"];
};

// Create a tool to get stock prices
export const stockPriceTool = createTool({
  id: "getStockPrice",
  description: "Fetches the current stock price for a given ticker symbol",
  inputSchema: z.object({
    symbol: z.string().describe("The stock ticker symbol (e.g., AAPL, MSFT)"),
  }),
  outputSchema: z.object({
    symbol: z.string(),
    price: z.number(),
    currency: z.string(),
    timestamp: z.string(),
  }),
  execute: async ({ context }) => {
    const price = await getStockPrice(context.symbol);

    return {
      symbol: context.symbol,
      price: parseFloat(price),
      currency: "USD",
      timestamp: new Date().toISOString(),
    };
  },
});

// Create a tool that uses the thread context
export const threadInfoTool = createTool({
  id: "getThreadInfo",
  description: "Returns information about the current conversation thread",
  inputSchema: z.object({
    includeResource: z.boolean().optional().default(false),
  }),
  execute: async ({ context, threadId, resourceId }) => {
    return {
      threadId,
      resourceId: context.includeResource ? resourceId : undefined,
      timestamp: new Date().toISOString(),
    };
  },
});
```

## APIリファレンス

### パラメータ

`createTool()` は、以下のプロパティを持つ単一のオブジェクトを受け取ります。

<PropertiesTable
  content={[
    {
      name: "id",
      type: "string",
      required: true,
      description:
        "ツールの一意な識別子。この値はツールの機能を説明するものにしてください。",
    },
    {
      name: "description",
      type: "string",
      required: true,
      description:
        "ツールが何をするのか、いつ使用すべきか、どのような入力が必要かの詳細な説明。AIがツールを効果的に利用するための助けとなります。",
    },
    {
      name: "execute",
      type: "(context: ToolExecutionContext, options?: any) => Promise<any>",
      required: false,
      description:
        "ツールのロジックを実装する非同期関数。実行コンテキストとオプションの設定を受け取ります。",
      properties: [
        {
          type: "ToolExecutionContext",
          parameters: [
            {
              name: "context",
              type: "object",
              description: "inputSchemaに一致する検証済みの入力データ",
            },
            {
              name: "threadId",
              type: "string",
              isOptional: true,
              description: "会話スレッドの識別子（利用可能な場合）",
            },
            {
              name: "resourceId",
              type: "string",
              isOptional: true,
              description: "ツールとやり取りするユーザーまたはリソースの識別子",
            },
            {
              name: "mastra",
              type: "Mastra",
              isOptional: true,
              description: "利用可能な場合のMastraインスタンスへの参照",
            },
          ],
        },
        {
          type: "ToolOptions",
          parameters: [
            {
              name: "toolCallId",
              type: "string",
              description:
                "ツール呼び出しのID。たとえば、ストリームデータとともにツール呼び出し関連情報を送信する際などに使用できます。",
            },
            {
              name: "messages",
              type: "CoreMessage[]",
              description:
                "ツール呼び出しを含む応答を生成するために言語モデルに送信されたメッセージ。これらのメッセージにはsystemプロンプトやツール呼び出しを含むassistantの応答は含まれません。",
            },
            {
              name: "abortSignal",
              type: "AbortSignal",
              isOptional: true,
              description:
                "全体の操作を中止すべきことを示すオプションのアボートシグナル。",
            },
          ],
        },
      ],
    },
    {
      name: "inputSchema",
      type: "ZodSchema",
      required: false,
      description:
        "ツールの入力パラメータを定義・検証するZodスキーマ。指定しない場合、ツールは任意の入力を受け付けます。",
    },
    {
      name: "outputSchema",
      type: "ZodSchema",
      required: false,
      description:
        "ツールの出力を定義・検証するZodスキーマ。ツールが期待される形式でデータを返すことを保証します。",
    },
  ]}
/>

### 戻り値

<PropertiesTable
  content={[
    {
      name: "Tool",
      type: "Tool<TSchemaIn, TSchemaOut>",
      description:
        "エージェントやワークフローで使用したり、直接実行できるToolインスタンス。",
      properties: [
        {
          type: "Tool",
          parameters: [
            {
              name: "id",
              type: "string",
              description: "ツールの一意な識別子",
            },
            {
              name: "description",
              type: "string",
              description: "ツールの機能の説明",
            },
            {
              name: "inputSchema",
              type: "ZodSchema | undefined",
              description: "入力の検証用スキーマ",
            },
            {
              name: "outputSchema",
              type: "ZodSchema | undefined",
              description: "出力の検証用スキーマ",
            },
            {
              name: "execute",
              type: "Function",
              description: "ツールの実行関数",
            },
          ],
        },
      ],
    },
  ]}
/>

## 型安全性

`createTool()` 関数は、TypeScript のジェネリクスを通じて完全な型安全性を提供します。

- 入力型は `inputSchema` から推論されます
- 出力型は `outputSchema` から推論されます
- 実行コンテキストは入力スキーマに基づいて正しく型付けされます

これにより、アプリケーション全体でツールが型安全であることが保証されます。

## ベストプラクティス

1. **わかりやすいID**: `getWeatherForecast` や `searchDatabase` のような、明確で行動を示すIDを使用する
2. **詳細な説明**: ツールの使用タイミングや方法を説明する、包括的な説明を提供する
3. **入力バリデーション**: Zodスキーマを使って入力を検証し、わかりやすいエラーメッセージを提供する
4. **エラーハンドリング**: execute関数で適切なエラーハンドリングを実装する
5. **冪等性**: 可能であれば、ツールを冪等にする（同じ入力で常に同じ出力を返す）
6. **パフォーマンス**: ツールは軽量で高速に実行できるようにする
