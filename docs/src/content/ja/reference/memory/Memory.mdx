# Memory クラスリファレンス

`Memory` クラスは、Mastraで会話履歴とスレッドベースのメッセージストレージを管理するための堅牢なシステムを提供します。会話の永続的な保存、セマンティック検索機能、効率的なメッセージ取得を可能にします。デフォルトでは、ストレージとベクトル検索にLibSQLを使用し、埋め込みにFastEmbedを使用します。

## 基本的な使い方

```typescript copy showLineNumbers
import { Memory } from "@mastra/memory";
import { Agent } from "@mastra/core/agent";

const agent = new Agent({
  memory: new Memory(),
  ...otherOptions,
});
```

## カスタム設定

```typescript copy showLineNumbers
import { Memory } from "@mastra/memory";
import { LibSQLStore, LibSQLVector } from "@mastra/libsql";
import { Agent } from "@mastra/core/agent";

const memory = new Memory({
  // Optional storage configuration - libsql will be used by default
  storage: new LibSQLStore({
    url: "file:./memory.db",
  }),

  // Optional vector database for semantic search - libsql will be used by default
  vector: new LibSQLVector({
    url: "file:./vector.db",
  }),

  // Memory configuration options
  options: {
    // Number of recent messages to include
    lastMessages: 20,

    // Semantic search configuration
    semanticRecall: {
      topK: 3, // Number of similar messages to retrieve
      messageRange: {
        // Messages to include around each result
        before: 2,
        after: 1,
      },
    },

    // Working memory configuration
    workingMemory: {
      enabled: true,
      template: `
# User
- First Name:
- Last Name:
`,
    },
  },
});

const agent = new Agent({
  memory,
  ...otherOptions,
});
```

### ワーキングメモリ

ワーキングメモリ機能により、エージェントは会話全体で永続的な情報を維持することができます。有効にすると、Memoryクラスはテキストストリームタグまたはツールコールを通じて、ワーキングメモリの更新を自動的に管理します。

ワーキングメモリの更新を処理するには、次の2つのモードがあります：

1. **text-stream**（デフォルト）：エージェントはMarkdownを含むXMLタグ（`<working_memory># User \n ## Preferences...</working_memory>`）を使用して、応答に直接ワーキングメモリの更新を含めます。これらのタグは自動的に処理され、表示される出力からは削除されます。

2. **tool-call**：エージェントはワーキングメモリを更新するための専用ツールを使用します。このモードは`toDataStream()`と連携する場合に使用すべきです。なぜならtext-streamモードはデータストリーミングと互換性がないためです。さらに、このモードはメモリ更新に対するより明示的な制御を提供し、テキストタグの管理よりもツールの使用が得意なエージェントと連携する場合に好まれることがあります。

設定例：

```typescript copy showLineNumbers
const memory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      template: "# User\n- **First Name**:\n- **Last Name**:",
      use: "tool-call", // または 'text-stream'
    },
  },
});
```

テンプレートが提供されない場合、Memoryクラスはユーザーの詳細、設定、目標、およびその他のコンテキスト情報をMarkdown形式で含むデフォルトのテンプレートを使用します。詳細な使用例とベストプラクティスについては、[ワーキングメモリガイド](/docs/memory/working-memory.mdx#designing-effective-templates)を参照してください。

### embedder

`semanticRecall`が有効になっている場合、埋め込みモデルが必要です。

一つのオプションは`@mastra/fastembed`を使用することです。これは[FastEmbed](https://github.com/Anush008/fastembed-js)を使用したオンデバイス/ローカルの埋め込みモデルを提供します。このモデルはローカルで実行され、APIキーやネットワークリクエストを必要としません。

使用するには、まずパッケージをインストールします：

```bash npm2yarn copy
npm install @mastra/fastembed
```

次に、`Memory`インスタンスで設定します：

```typescript {2,7}
import { Memory } from "@mastra/memory";
import { fastembed } from "@mastra/fastembed";
import { Agent } from "@mastra/core/agent";

const agent = new Agent({
  memory: new Memory({
    embedder: fastembed,
    // ... その他のメモリ設定
  }),
});
```

プロジェクトをデプロイする場所によっては、FastEmbedの大きな内部依存関係のため、プロジェクトがデプロイできない場合があることに注意してください。

あるいは、OpenAIのようなAPIベースの埋め込みツール（この問題がない）を使用することもできます：

```typescript {2,7}
import { Memory } from "@mastra/memory";
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";

const agent = new Agent({
  memory: new Memory({
    embedder: openai.embedding("text-embedding-3-small"),
  }),
});
```

Mastraは[Vercel AI SDK](https://sdk.vercel.ai/docs/ai-sdk-core/embeddings)を通じて、OpenAI、Google、Mistral、Cohereなどのオプションを含む多くの埋め込みモデルをサポートしています。

## パラメータ

<PropertiesTable
  content={[
    {
      name: "storage",
      type: "MastraStorage",
      description: "メモリデータを永続化するためのストレージ実装",
      isOptional: true,
    },
    {
      name: "vector",
      type: "MastraVector",
      description: "セマンティック検索機能のためのベクトルストア",
      isOptional: true,
    },
    {
      name: "embedder",
      type: "EmbeddingModel",
      description:
        "ベクトル埋め込みのためのエンベッダーインスタンス。デフォルトではFastEmbed（bge-small-en-v1.5）を使用",
      isOptional: true,
    },
    {
      name: "options",
      type: "MemoryConfig",
      description: "一般的なメモリ設定オプション",
      isOptional: true,
    },
  ]}
/>

### options

<PropertiesTable
  content={[
    {
      name: "lastMessages",
      type: "number | false",
      description:
        "取得する最新メッセージの数。無効にするにはfalseに設定します。",
      isOptional: true,
      defaultValue: "40",
    },
    {
      name: "semanticRecall",
      type: "boolean | SemanticRecallConfig",
      description:
        "メッセージ履歴でのセマンティック検索を有効にします。ベクトルストアが提供されると自動的に有効になります。",
      isOptional: true,
      defaultValue: "false（ベクトルストアが提供されている場合はtrue）",
    },
    {
      name: "topK",
      type: "number",
      description: "セマンティック検索を使用する際に取得する類似メッセージの数",
      isOptional: true,
      defaultValue: "2",
    },
    {
      name: "messageRange",
      type: "number | { before: number; after: number }",
      description: "セマンティック検索結果の周囲に含めるメッセージの範囲",
      isOptional: true,
      defaultValue: "2",
    },
    {
      name: "workingMemory",
      type: "{ enabled: boolean; template?: string; use?: 'text-stream' | 'tool-call' }",
      description:
        "会話間でユーザー情報を永続的に保存できるワーキングメモリ機能の設定。'use'設定は、ワーキングメモリの更新がテキストストリームタグを通じて処理されるか、ツール呼び出しを通じて処理されるかを決定します。ワーキングメモリはマークダウン形式を使用して、継続的に関連する情報を構造化して保存します。",
      isOptional: true,
      defaultValue:
        "{ enabled: false, template: '# User Information\\n- **First Name**:\\n- **Last Name**:\\n...', use: 'text-stream' }",
    },
    {
      name: "threads",
      type: "{ generateTitle?: boolean }",
      description:
        "メモリスレッド作成に関する設定。`generateTitle`を設定すると、ユーザーの最初のメッセージからLLMの要約によってthread.titleが生成されます。",
      isOptional: true,
      defaultValue: "{ generateTitle: true }",
    },
  ]}
/>

### 関連項目

- [メモリの使用を開始する](/docs/memory/overview.mdx)
- [セマンティックリコール](/docs/memory/semantic-recall.mdx)
- [ワーキングメモリ](/docs/memory/working-memory.mdx)
- [メモリプロセッサ](/docs/memory/memory-processors.mdx)
- [createThread](/reference/memory/createThread.mdx)
- [query](/reference/memory/query.mdx)
- [getThreadById](/reference/memory/getThreadById.mdx)
- [getThreadsByResourceId](/reference/memory/getThreadsByResourceId.mdx)
