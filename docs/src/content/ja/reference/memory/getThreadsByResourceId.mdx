# getThreadsByResourceId リファレンス

`getThreadsByResourceId` 関数は、特定のリソースIDに関連付けられたすべてのスレッドをストレージから取得します。

## 使用例

```typescript
import { Memory } from "@mastra/core/memory";

const memory = new Memory(config);

const threads = await memory.getThreadsByResourceId({
  resourceId: "resource-123",
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "resourceId",
      type: "string",
      description: "スレッドを取得したいリソースのID。",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "StorageThreadType[]",
      type: "Promise",
      description:
        "指定されたリソースIDに関連付けられたスレッドの配列を解決するPromise。",
    },
  ]}
/>

### 関連情報

- [Memory クラスリファレンス](/reference/memory/Memory.mdx)
- [Memory のはじめ方](/docs/memory/overview.mdx)（スレッド／リソースの概念を解説）
- [createThread](/reference/memory/createThread.mdx)
- [getThreadById](/reference/memory/getThreadById.mdx)
