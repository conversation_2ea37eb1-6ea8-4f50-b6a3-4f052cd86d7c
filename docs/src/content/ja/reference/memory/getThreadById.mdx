# getThreadById リファレンス

`getThreadById` 関数は、ストレージから指定したIDのスレッドを取得します。

## 使用例

```typescript
import { Memory } from "@mastra/core/memory";

const memory = new Memory(config);

const thread = await memory.getThreadById({ threadId: "thread-123" });
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "threadId",
      type: "string",
      description: "取得するスレッドのID。",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "StorageThreadType | null",
      type: "Promise",
      description:
        "指定されたIDに関連付けられたスレッドが見つかった場合はそのスレッド、見つからなかった場合はnullを返すPromise。",
    },
  ]}
/>

### 関連情報

- [Memory クラスリファレンス](/reference/memory/Memory.mdx)
- [Memory のはじめ方](/docs/memory/overview.mdx)（スレッドの概念について解説）
- [createThread](/reference/memory/createThread.mdx)
- [getThreadsByResourceId](/reference/memory/getThreadsByResourceId.mdx)
