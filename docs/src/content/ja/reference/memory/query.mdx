# query

特定のスレッドからメッセージを取得します。ページネーションやフィルタリングオプションにも対応しています。

## 使用例

```typescript
import { Memory } from "@mastra/memory";

const memory = new Memory({
  /* config */
});

// Get last 50 messages
const { messages, uiMessages } = await memory.query({
  threadId: "thread-123",
  selectBy: {
    last: 50,
  },
});

// Get messages with context around specific messages
const { messages: contextMessages } = await memory.query({
  threadId: "thread-123",
  selectBy: {
    include: [
      {
        id: "msg-123", // Get just this message (no context)
      },
      {
        id: "msg-456", // Get this message with custom context
        withPreviousMessages: 3, // 3 messages before
        withNextMessages: 1, // 1 message after
      },
    ],
  },
});

// Semantic search in messages
const { messages } = await memory.query({
  threadId: "thread-123",
  selectBy: {
    vectorSearchString: "What was discussed about deployment?",
  },
  threadConfig: {
    historySearch: true,
  },
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "threadId",
      type: "string",
      description: "メッセージを取得するスレッドの一意の識別子",
      isOptional: false,
    },
    {
      name: "resourceId",
      type: "string",
      description:
        "スレッドを所有するリソースのオプションID。指定された場合、スレッドの所有権を検証します",
      isOptional: true,
    },
    {
      name: "selectBy",
      type: "object",
      description: "メッセージをフィルタリングするためのオプション",
      isOptional: true,
    },
    {
      name: "threadConfig",
      type: "MemoryConfig",
      description: "メッセージ取得のための設定オプション",
      isOptional: true,
    },
  ]}
/>

### selectBy

<PropertiesTable
  content={[
    {
      name: "vectorSearchString",
      type: "string",
      description: "意味的に類似したメッセージを検索するための検索文字列",
      isOptional: true,
    },
    {
      name: "last",
      type: "number | false",
      description:
        "取得する最新メッセージの数。制限を無効にするにはfalseを設定します。注意: threadConfig.lastMessages（デフォルト: 40）がこれより小さい場合はそちらが優先されます。",
      isOptional: true,
      defaultValue: "40",
    },
    {
      name: "include",
      type: "array",
      description: "コンテキストと共に含めるメッセージIDの配列",
      isOptional: true,
    },
  ]}
/>

### include

<PropertiesTable
  content={[
    {
      name: "id",
      type: "string",
      description: "含めるメッセージのID",
      isOptional: false,
    },
    {
      name: "withPreviousMessages",
      type: "number",
      description:
        "このメッセージの前に含めるメッセージ数。ベクトル検索を使用する場合はデフォルトで2、それ以外は0です。",
      isOptional: true,
    },
    {
      name: "withNextMessages",
      type: "number",
      description:
        "このメッセージの後に含めるメッセージ数。ベクトル検索を使用する場合はデフォルトで2、それ以外は0です。",
      isOptional: true,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "messages",
      type: "CoreMessage[]",
      description: "コア形式で取得されたメッセージの配列",
    },
    {
      name: "uiMessages",
      type: "AiMessage[]",
      description: "UI表示用にフォーマットされたメッセージの配列",
    },
  ]}
/>

## 補足

`query` 関数は2つの異なるメッセージ形式を返します：

- `messages`: 内部で使用されるコアメッセージ形式
- `uiMessages`: UI表示に適した形式のメッセージで、ツールコールやその結果のスレッド化が適切に行われています

### 関連

- [Memory クラスリファレンス](/reference/memory/Memory.mdx)
- [Memory のはじめ方](/docs/memory/overview.mdx)
- [セマンティックリコール](/docs/memory/semantic-recall.mdx)
- [createThread](/reference/memory/createThread.mdx)
