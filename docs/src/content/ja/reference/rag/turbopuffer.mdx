---
title: "リファレンス: Turbopuffer ベクターストア | ベクターデータベース | RAG | Mastra ドキュメント"
description: TurbopufferをMastraと統合するためのドキュメント。効率的な類似検索のための高性能ベクターデータベース。
---

# Turbopuffer Vector Store

TurbopufferVector クラスは、RAG アプリケーション向けに最適化された高性能ベクターデータベースである [Turbopuffer](https://turbopuffer.com/) を使用したベクター検索を提供します。Turbopuffer は、高度なフィルタリング機能と効率的なストレージ管理を備えた高速なベクター類似検索を提供します。

## コンストラクタオプション

<PropertiesTable
  content={[
    {
      name: "apiKey",
      type: "string",
      description: "Turbopufferで認証するためのAPIキー。",
    },
    {
      name: "baseUrl",
      type: "string",
      isOptional: true,
      defaultValue: "https://api.turbopuffer.com",
      description: "Turbopuffer APIのベースURL。",
    },
    {
      name: "connectTimeout",
      type: "number",
      isOptional: true,
      defaultValue: "10000",
      description:
        "接続を確立するためのタイムアウト（ミリ秒）。NodeとDenoでのみ適用されます。",
    },
    {
      name: "connectionIdleTimeout",
      type: "number",
      isOptional: true,
      defaultValue: "60000",
      description:
        "ソケットのアイドルタイムアウト（ミリ秒）。NodeとDenoでのみ適用されます。",
    },
    {
      name: "warmConnections",
      type: "number",
      isOptional: true,
      defaultValue: "0",
      description: "新しいクライアントを作成する際に最初に開く接続の数。",
    },
    {
      name: "compression",
      type: "boolean",
      isOptional: true,
      defaultValue: "true",
      description:
        "リクエストを圧縮し、圧縮されたレスポンスを受け入れるかどうか。",
    },
    {
      name: "schemaConfigForIndex",
      type: "function",
      isOptional: true,
      description:
        "インデックス名を受け取り、そのインデックスの設定オブジェクトを返すコールバック関数。これにより、インデックスごとに明示的なスキーマを定義できます。",
    },
  ]}
/>

## メソッド

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "作成するインデックスの名前",
    },
    {
      name: "dimension",
      type: "number",
      description: "ベクトルの次元（埋め込みモデルに一致する必要があります）",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description: "類似性検索のための距離メトリック",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "vectors",
      type: "number[][]",
      description: "埋め込みベクトルの配列",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "各ベクトルのメタデータ",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description: "オプションのベクトルID（指定されない場合は自動生成）",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "クエリを実行するインデックスの名前",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "類似ベクトルを見つけるためのクエリベクトル",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "返す結果の数",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "クエリのメタデータフィルター",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "結果にベクトルを含めるかどうか",
    },
  ]}
/>

### listIndexes()

文字列としてインデックス名の配列を返します。

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "説明するインデックスの名前",
    },
  ]}
/>

返される内容:

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "削除するインデックスの名前",
    },
  ]}
/>

## 応答タイプ

クエリ結果はこの形式で返されます:

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[]; // Only included if includeVector is true
}
```

## スキーマ構成

`schemaConfigForIndex` オプションを使用すると、異なるインデックスに対して明示的なスキーマを定義できます:

```typescript copy
schemaConfigForIndex: (indexName: string) => {
  // Mastraのデフォルトの埋め込みモデルとメモリメッセージのインデックス:
  if (indexName === "memory_messages_384") {
    return {
      dimensions: 384,
      schema: {
        thread_id: {
          type: "string",
          filterable: true,
        },
      },
    };
  } else {
    throw new Error(`TODO: add schema for index: ${indexName}`);
  }
};
```

## エラーハンドリング

このストアは、キャッチ可能な型付きエラーをスローします:

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // 追加のエラーコンテキスト
  }
}
```

## 関連

- [メタデータフィルター](./metadata-filters)
