---
title: "リファレンス: GraphRAG | グラフベースRAG | RAG | Mastra ドキュメント"
description: MastraのGraphRAGクラスのドキュメント。グラフベースの検索拡張生成手法を実装しています。
---

# GraphRAG

`GraphRAG` クラスは、グラフベースのリトリーバル拡張生成手法を実装しています。ドキュメントのチャンクからナレッジグラフを作成し、ノードはドキュメントを、エッジはセマンティックな関係を表します。これにより、直接的な類似性マッチングだけでなく、グラフの探索を通じて関連コンテンツを発見することも可能になります。

## 基本的な使い方

```typescript
import { GraphRAG } from "@mastra/rag";

const graphRag = new GraphRAG({
  dimension: 1536,
  threshold: 0.7,
});

// Create the graph from chunks and embeddings
graphRag.createGraph(documentChunks, embeddings);

// Query the graph with embedding
const results = await graphRag.query({
  query: queryEmbedding,
  topK: 10,
  randomWalkSteps: 100,
  restartProb: 0.15,
});
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "dimension",
      type: "number",
      description: "埋め込みベクトルの次元数",
      isOptional: true,
      defaultValue: "1536",
    },
    {
      name: "threshold",
      type: "number",
      description: "ノード間のエッジを作成するための類似度しきい値（0-1）",
      isOptional: true,
      defaultValue: "0.7",
    },
  ]}
/>

## メソッド

### createGraph

ドキュメントチャンクとその埋め込みからナレッジグラフを作成します。

```typescript
createGraph(chunks: GraphChunk[], embeddings: GraphEmbedding[]): void
```

#### パラメータ

<PropertiesTable
  content={[
    {
      name: "chunks",
      type: "GraphChunk[]",
      description: "テキストとメタデータを含むドキュメントチャンクの配列",
      isOptional: false,
    },
    {
      name: "embeddings",
      type: "GraphEmbedding[]",
      description: "チャンクに対応する埋め込みの配列",
      isOptional: false,
    },
  ]}
/>

### query

ベクトル類似度とグラフ探索を組み合わせたグラフベースの検索を実行します。

```typescript
query({
  query,
  topK = 10,
  randomWalkSteps = 100,
  restartProb = 0.15
}: {
  query: number[];
  topK?: number;
  randomWalkSteps?: number;
  restartProb?: number;
}): RankedNode[]
```

#### パラメータ

<PropertiesTable
  content={[
    {
      name: "query",
      type: "number[]",
      description: "クエリの埋め込みベクトル",
      isOptional: false,
    },
    {
      name: "topK",
      type: "number",
      description: "返す結果の数",
      isOptional: true,
      defaultValue: "10",
    },
    {
      name: "randomWalkSteps",
      type: "number",
      description: "ランダムウォークのステップ数",
      isOptional: true,
      defaultValue: "100",
    },
    {
      name: "restartProb",
      type: "number",
      description: "クエリノードからウォークを再開する確率",
      isOptional: true,
      defaultValue: "0.15",
    },
  ]}
/>

#### 戻り値

`RankedNode` オブジェクトの配列を返します。各ノードには以下が含まれます：

<PropertiesTable
  content={[
    {
      name: "id",
      type: "string",
      description: "ノードの一意な識別子",
    },
    {
      name: "content",
      type: "string",
      description: "ドキュメントチャンクのテキスト内容",
    },
    {
      name: "metadata",
      type: "Record<string, any>",
      description: "チャンクに関連付けられた追加メタデータ",
    },
    {
      name: "score",
      type: "number",
      description: "グラフ探索による総合関連スコア",
    },
  ]}
/>

## 高度な例

```typescript
const graphRag = new GraphRAG({
  dimension: 1536,
  threshold: 0.8, // Stricter similarity threshold
});

// Create graph from chunks and embeddings
graphRag.createGraph(documentChunks, embeddings);

// Query with custom parameters
const results = await graphRag.query({
  query: queryEmbedding,
  topK: 5,
  randomWalkSteps: 200,
  restartProb: 0.2,
});
```

## 関連

- [createGraphRAGTool](../tools/graph-rag-tool)
