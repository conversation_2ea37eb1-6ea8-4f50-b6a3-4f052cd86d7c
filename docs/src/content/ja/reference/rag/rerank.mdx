---
title: "リファレンス: Rerank | ドキュメント検索 | RAG | Mastra ドキュメント"
description: Mastra の rerank 機能に関するドキュメント。ベクトル検索結果に対して高度なリランキング機能を提供します。
---

# rerank()

`rerank()` 関数は、セマンティックな関連性、ベクトル類似度、位置に基づくスコアリングを組み合わせることで、ベクトル検索結果に対して高度なリランキング機能を提供します。

```typescript
function rerank(
  results: QueryResult[],
  query: string,
  modelConfig: ModelConfig,
  options?: RerankerFunctionOptions,
): Promise<RerankResult[]>;
```

## 使用例

```typescript
import { openai } from "@ai-sdk/openai";
import { rerank } from "@mastra/rag";

const model = openai("gpt-4o-mini");

const rerankedResults = await rerank(
  vectorSearchResults,
  "How do I deploy to production?",
  model,
  {
    weights: {
      semantic: 0.5,
      vector: 0.3,
      position: 0.2,
    },
    topK: 3,
  },
);
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "results",
      type: "QueryResult[]",
      description: "再ランク付けするベクトル検索結果",
      isOptional: false,
    },
    {
      name: "query",
      type: "string",
      description: "関連性を評価するために使用される検索クエリテキスト",
      isOptional: false,
    },
    {
      name: "model",
      type: "MastraLanguageModel",
      description: "再ランク付けに使用する言語モデル",
      isOptional: false,
    },
    {
      name: "options",
      type: "RerankerFunctionOptions",
      description: "再ランク付けモデルのオプション",
      isOptional: true,
    },
  ]}
/>

rerank関数は、Vercel AI SDKの任意のLanguageModelを受け付けます。Cohereモデルの`rerank-v3.5`を使用する場合、自動的にCohereの再ランク付け機能が利用されます。

> **注意:** セマンティックスコアリングが再ランク付け時に正しく機能するためには、各結果の`metadata.text`フィールドにテキストコンテンツが含まれている必要があります。

### RerankerFunctionOptions

<PropertiesTable
  content={[
    {
      name: "weights",
      type: "WeightConfig",
      description:
        "異なるスコアリング要素の重み（合計が1になる必要があります）",
      isOptional: true,
      properties: [
        {
          type: "number",
          parameters: [
            {
              name: "semantic",
              description: "セマンティック関連性の重み",
              isOptional: true,
              type: "number (default: 0.4)",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "vector",
              description: "ベクトル類似度の重み",
              isOptional: true,
              type: "number (default: 0.4)",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "position",
              description: "位置ベースのスコアリングの重み",
              isOptional: true,
              type: "number (default: 0.2)",
            },
          ],
        },
      ],
    },
    {
      name: "queryEmbedding",
      type: "number[]",
      description: "クエリの埋め込みベクトル",
      isOptional: true,
    },
    {
      name: "topK",
      type: "number",
      description: "返す上位結果の数",
      isOptional: true,
      defaultValue: "3",
    },
  ]}
/>

## 戻り値

この関数は `RerankResult` オブジェクトの配列を返します：

<PropertiesTable
  content={[
    {
      name: "result",
      type: "QueryResult",
      description: "元のクエリ結果",
    },
    {
      name: "score",
      type: "number",
      description: "統合リランキングスコア (0-1)",
    },
    {
      name: "details",
      type: "ScoringDetails",
      description: "詳細なスコア情報",
    },
  ]}
/>

### ScoringDetails

<PropertiesTable
  content={[
    {
      name: "semantic",
      type: "number",
      description: "セマンティック関連度スコア (0-1)",
    },
    {
      name: "vector",
      type: "number",
      description: "ベクトル類似度スコア (0-1)",
    },
    {
      name: "position",
      type: "number",
      description: "ポジションベースのスコア (0-1)",
    },
    {
      name: "queryAnalysis",
      type: "object",
      description: "クエリ分析の詳細",
      isOptional: true,
      properties: [
        {
          type: "number",
          parameters: [
            {
              name: "magnitude",
              description: "クエリの大きさ",
            },
          ],
        },
        {
          type: "number[]",
          parameters: [
            {
              name: "dominantFeatures",
              description: "クエリの主要な特徴",
            },
          ],
        },
      ],
    },
  ]}
/>

## 関連

- [createVectorQueryTool](../tools/vector-query-tool)
