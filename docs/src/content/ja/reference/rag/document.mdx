---
title: "リファレンス: MDocument | ドキュメント処理 | RAG | Mastra Docs"
description: ドキュメント処理とチャンク化を担当するMastraのMDocumentクラスのドキュメントです。
---

# MDocument

MDocumentクラスは、RAGアプリケーション向けにドキュメントを処理します。主なメソッドは `.chunk()` と `.extractMetadata()` です。

## コンストラクタ

<PropertiesTable
  content={[
    {
      name: "docs",
      type: "Array<{ text: string, metadata?: Record<string, any> }>",
      description:
        "テキストコンテンツとオプションのメタデータを含むドキュメントチャンクの配列",
    },
    {
      name: "type",
      type: "'text' | 'html' | 'markdown' | 'json' | 'latex'",
      description: "ドキュメントコンテンツの種類",
    },
  ]}
/>

## 静的メソッド

### fromText()

プレーンテキストの内容からドキュメントを作成します。

```typescript
static fromText(text: string, metadata?: Record<string, any>): MDocument
```

### fromHTML()

HTMLコンテンツからドキュメントを作成します。

```typescript
static fromHTML(html: string, metadata?: Record<string, any>): MDocument
```

### fromMarkdown()

Markdownコンテンツからドキュメントを作成します。

```typescript
static fromMarkdown(markdown: string, metadata?: Record<string, any>): MDocument
```

### fromJSON()

JSONコンテンツからドキュメントを作成します。

```typescript
static fromJSON(json: string, metadata?: Record<string, any>): MDocument
```

## インスタンスメソッド

### chunk()

ドキュメントをチャンクに分割し、オプションでメタデータを抽出します。

```typescript
async chunk(params?: ChunkParams): Promise<Chunk[]>
```

詳細なオプションについては、[chunk() リファレンス](./chunk) を参照してください。

### getDocs()

処理済みドキュメントチャンクの配列を返します。

```typescript
getDocs(): Chunk[]
```

### getText()

チャンクからテキスト文字列の配列を返します。

```typescript
getText(): string[]
```

### getMetadata()

チャンクからメタデータオブジェクトの配列を返します。

```typescript
getMetadata(): Record<string, any>[]
```

### extractMetadata()

指定したエクストラクターを使用してメタデータを抽出します。詳細は [ExtractParams リファレンス](./extract-params) を参照してください。

```typescript
async extractMetadata(params: ExtractParams): Promise<MDocument>
```

## 例

```typescript
import { MDocument } from "@mastra/rag";

// Create document from text
const doc = MDocument.fromText("Your content here");

// Split into chunks with metadata extraction
const chunks = await doc.chunk({
  strategy: "markdown",
  headers: [
    ["#", "title"],
    ["##", "section"],
  ],
  extract: {
    summary: true, // Extract summaries with default settings
    keywords: true, // Extract keywords with default settings
  },
});

// Get processed chunks
const docs = doc.getDocs();
const texts = doc.getText();
const metadata = doc.getMetadata();
```
