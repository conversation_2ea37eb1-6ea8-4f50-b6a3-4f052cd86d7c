---
title: "リファレンス: MongoDB ベクターストア | ベクターデータベース | RAG | Mastra ドキュメント"
description: Mastra の MongoDBVector クラスのドキュメント。MongoDB Atlas および Atlas Vector Search を使用したベクター検索を提供します。
---

# MongoDB Vector Store

`MongoDBVector` クラスは、[MongoDB Atlas Vector Search](https://www.mongodb.com/docs/atlas/atlas-vector-search/) を使用したベクター検索を提供します。これにより、MongoDB コレクション内で効率的な類似性検索とメタデータフィルタリングが可能になります。

## インストール

```bash copy
npm install @mastra/mongodb
```

## 使用例

```typescript copy showLineNumbers
import { MongoDBVector } from "@mastra/mongodb";

const store = new MongoDBVector({
  url: process.env.MONGODB_URL,
  database: process.env.MONGODB_DATABASE,
});
```

## コンストラクターオプション

<PropertiesTable
  content={[
    {
      name: "url",
      type: "string",
      description: "MongoDB 接続文字列 (URI)",
    },
    {
      name: "database",
      type: "string",
      description: "使用する MongoDB データベース名",
    },
    {
      name: "options",
      type: "MongoClientOptions",
      isOptional: true,
      description: "オプションの MongoDB クライアントオプション",
    },
  ]}
/>

## メソッド

### createIndex()

MongoDBに新しいベクトルインデックス（コレクション）を作成します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "作成するコレクションの名前（以下の命名規則を参照）",
    },
    {
      name: "dimension",
      type: "number",
      description: "ベクトルの次元数（埋め込みモデルと一致する必要があります）",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description: "類似性検索の距離メトリック",
    },
  ]}
/>

### upsert()

コレクションにベクトルとそのメタデータを追加または更新します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "挿入先のコレクション名",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "埋め込みベクトルの配列",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "各ベクトルのメタデータ",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description: "オプションのベクトルID（提供されない場合は自動生成）",
    },
  ]}
/>

### query()

オプションのメタデータフィルタリングを使用して類似ベクトルを検索します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "検索対象のコレクション名",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "類似ベクトルを検索するためのクエリベクトル",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "返す結果の数",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "メタデータフィルター（`metadata`フィールドに適用）",
    },
    {
      name: "documentFilter",
      type: "Record<string, any>",
      isOptional: true,
      description:
        "元のドキュメントフィールドに対するフィルター（メタデータだけでなく）",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "結果にベクトルデータを含めるかどうか",
    },
    {
      name: "minScore",
      type: "number",
      isOptional: true,
      defaultValue: "0",
      description: "最小類似性スコアのしきい値",
    },
  ]}
/>

### describeIndex()

インデックス（コレクション）に関する情報を返します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "説明するコレクションの名前",
    },
  ]}
/>

戻り値：

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

コレクションとそのすべてのデータを削除します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "削除するコレクションの名前",
    },
  ]}
/>

### listIndexes()

MongoDBデータベース内のすべてのベクトルコレクションを一覧表示します。

戻り値：`Promise<string[]>`

### updateVector()

IDで特定のベクトルエントリを新しいベクトルデータやメタデータで更新します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "ベクトルを含むコレクションの名前",
    },
    {
      name: "id",
      type: "string",
      description: "更新するベクトルエントリのID",
    },
    {
      name: "update",
      type: "object",
      description: "ベクトルやメタデータを含む更新データ",
    },
    {
      name: "update.vector",
      type: "number[]",
      isOptional: true,
      description: "更新する新しいベクトルデータ",
    },
    {
      name: "update.metadata",
      type: "Record<string, any>",
      isOptional: true,
      description: "更新する新しいメタデータ",
    },
  ]}
/>

### deleteVector()

IDによってインデックスから特定のベクトルエントリを削除します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "ベクトルを含むコレクションの名前",
    },
    {
      name: "id",
      type: "string",
      description: "削除するベクトルエントリのID",
    },
  ]}
/>

### disconnect()

MongoDBクライアントの接続を閉じます。ストアの使用が終わったら呼び出す必要があります。

## レスポンスタイプ

クエリ結果は次の形式で返されます：

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[]; // Only included if includeVector is true
}
```

## エラー処理

ストアは捕捉可能な型付きエラーをスローします：

```typescript copy
try {
  await store.query({
    indexName: "my_collection",
    queryVector: queryVector,
  });
} catch (error) {
  // Handle specific error cases
  if (error.message.includes("Invalid collection name")) {
    console.error(
      "Collection name must start with a letter or underscore and contain only valid characters.",
    );
  } else if (error.message.includes("Collection not found")) {
    console.error("The specified collection does not exist");
  } else {
    console.error("Vector store error:", error.message);
  }
}
```

## ベストプラクティス

- クエリのパフォーマンスを最適化するために、フィルターで使用するメタデータフィールドにインデックスを作成してください。
- 予期しないクエリ結果を避けるために、メタデータ内のフィールド名を一貫して使用してください。
- 効率的な検索を維持するために、インデックスやコレクションの統計情報を定期的に監視してください。

## 関連

- [メタデータフィルター](./metadata-filters)
