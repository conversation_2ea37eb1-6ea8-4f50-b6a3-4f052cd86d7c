---
title: "リファレンス: Qdrant ベクトルストア | ベクトルデータベース | RAG | Mastra ドキュメント"
description: Mastraとの統合のためのQdrantのドキュメント。Qdrantはベクトルとペイロードを管理するためのベクトル類似性検索エンジンです。
---

# Qdrant ベクトルストア

QdrantVectorクラスは、[Qdrant](https://qdrant.tech/)を使用したベクトル検索を提供します。Qdrantはベクトル類似性検索エンジンです。
これは、追加のペイロードと拡張フィルタリングサポートを備えたベクトルの保存、検索、管理を行うための便利なAPIを持つ、本番環境対応のサービスを提供します。

## コンストラクタオプション

<PropertiesTable
  content={[
    {
      name: "url",
      type: "string",
      description:
        "QdrantインスタンスのREST URL。例：https://xyz-example.eu-central.aws.cloud.qdrant.io:6333",
    },
    {
      name: "apiKey",
      type: "string",
      description: "オプションのQdrant APIキー",
    },
    {
      name: "https",
      type: "boolean",
      description:
        "接続を設定する際にTLSを使用するかどうか。推奨されています。",
    },
  ]}
/>

## メソッド

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "作成するインデックスの名前",
    },
    {
      name: "dimension",
      type: "number",
      description: "ベクトルの次元数（埋め込みモデルと一致する必要があります）",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description: "類似性検索の距離メトリック",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "vectors",
      type: "number[][]",
      description: "埋め込みベクトルの配列",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "各ベクトルのメタデータ",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description:
        "オプションのベクトルID（提供されない場合は自動生成されます）",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "クエリを実行するインデックスの名前",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "類似ベクトルを検索するためのクエリベクトル",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "返す結果の数",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "クエリのメタデータフィルター",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "結果にベクトルを含めるかどうか",
    },
  ]}
/>

### listIndexes()

インデックス名の文字列配列を返します。

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "説明するインデックスの名前",
    },
  ]}
/>

戻り値：

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "削除するインデックスの名前",
    },
  ]}
/>

### updateVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "更新するインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "更新するベクトルのID",
    },
    {
      name: "update",
      type: "{ vector?: number[]; metadata?: Record<string, any>; }",
      description: "更新するベクトルやメタデータを含むオブジェクト",
    },
  ]}
/>

指定されたインデックス内のベクトルやそのメタデータを更新します。ベクトルとメタデータの両方が提供された場合、両方が更新されます。一方のみが提供された場合、そのみが更新されます。

### deleteVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "ベクトルを削除するインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "削除するベクトルのID",
    },
  ]}
/>

IDによって指定されたインデックスからベクトルを削除します。

## レスポンスタイプ

クエリ結果は以下の形式で返されます：

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[]; // Only included if includeVector is true
}
```

## エラー処理

ストアは捕捉できる型付きエラーをスローします：

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

## 関連

- [メタデータフィルター](./metadata-filters)
