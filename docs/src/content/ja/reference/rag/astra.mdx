---
title: "リファレンス: Astra ベクトルストア | ベクトルデータベース | RAG | Mastra ドキュメント"
description: Mastraの中のAstraVectorクラスのドキュメント。DataStax Astra DBを使用したベクトル検索を提供します。
---

# Astra Vector Store

AstraVectorクラスは、[DataStax Astra DB](https://www.datastax.com/products/datastax-astra)を使用したベクトル検索を提供します。これはApache Cassandraをベースにしたクラウドネイティブでサーバーレスなデータベースです。
エンタープライズグレードのスケーラビリティと高可用性を備えたベクトル検索機能を提供します。

## コンストラクタオプション

<PropertiesTable
  content={[
    {
      name: "token",
      type: "string",
      description: "Astra DBのAPIトークン",
    },
    {
      name: "endpoint",
      type: "string",
      description: "Astra DBのAPIエンドポイント",
    },
    {
      name: "keyspace",
      type: "string",
      isOptional: true,
      description: "オプションのキースペース名",
    },
  ]}
/>

## メソッド

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "作成するインデックスの名前",
    },
    {
      name: "dimension",
      type: "number",
      description: "ベクトルの次元数（埋め込みモデルと一致する必要があります）",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description:
        "類似性検索の距離メトリック（dotproductの場合はdot_productにマッピングされます）",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "アップサートするインデックスの名前",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "埋め込みベクトルの配列",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "各ベクトルのメタデータ",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description:
        "オプションのベクトルID（提供されない場合は自動生成されます）",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "クエリを実行するインデックスの名前",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "類似ベクトルを検索するためのクエリベクトル",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "返す結果の数",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "クエリのメタデータフィルター",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "結果にベクトルを含めるかどうか",
    },
  ]}
/>

### listIndexes()

インデックス名の文字列配列を返します。

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "説明するインデックスの名前",
    },
  ]}
/>

戻り値：

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "削除するインデックスの名前",
    },
  ]}
/>

### updateVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "ベクトルを含むインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "更新するベクトルのID",
    },
    {
      name: "update",
      type: "object",
      description: "ベクトルやメタデータの変更を含む更新オブジェクト",
      properties: [
        {
          name: "vector",
          type: "number[]",
          isOptional: true,
          description: "新しいベクトル値",
        },
        {
          name: "metadata",
          type: "Record<string, any>",
          isOptional: true,
          description: "新しいメタデータ値",
        },
      ],
    },
  ]}
/>

### deleteVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "ベクトルを含むインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "削除するベクトルのID",
    },
  ]}
/>

## レスポンスタイプ

クエリ結果は以下の形式で返されます：

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[]; // Only included if includeVector is true
}
```

## エラー処理

ストアは捕捉可能な型付きエラーをスローします：

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

## 環境変数

必要な環境変数：

- `ASTRA_DB_TOKEN`: Astra DBのAPIトークン
- `ASTRA_DB_ENDPOINT`: Astra DBのAPIエンドポイント

## 関連

- [メタデータフィルター](./metadata-filters)
