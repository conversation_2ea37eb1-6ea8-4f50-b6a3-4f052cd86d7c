---
title: "リファレンス: メタデータフィルター | メタデータフィルタリング | RAG | Mastra ドキュメント"
description: Mastra のメタデータフィルタリング機能に関するドキュメント。これにより、さまざまなベクトルストアでのベクトル検索結果に対して精密なクエリが可能になります。
---

# メタデータフィルター

Mastra は、すべてのベクトルストアで統一されたメタデータフィルタリング構文を提供しており、MongoDB/Sift クエリ構文に基づいています。各ベクトルストアは、これらのフィルターを自分たちのネイティブな形式に変換します。

## 基本例

```typescript
import { PgVector } from "@mastra/pg";

const store = new PgVector({ connectionString });

const results = await store.query({
  indexName: "my_index",
  queryVector: queryVector,
  topK: 10,
  filter: {
    category: "electronics", // Simple equality
    price: { $gt: 100 }, // Numeric comparison
    tags: { $in: ["sale", "new"] }, // Array membership
  },
});
```

## サポートされている演算子

<OperatorsTable
  title="基本比較"
  operators={[
    {
      name: "$eq",
      description: "指定した値と等しい値に一致します",
      example: "{ age: { $eq: 25 } }",
      supportedBy: ["All"],
    },
    {
      name: "$ne",
      description: "等しくない値に一致します",
      example: "{ status: { $ne: 'inactive' } }",
      supportedBy: ["All"],
    },
    {
      name: "$gt",
      description: "より大きい",
      example: "{ price: { $gt: 100 } }",
      supportedBy: ["All"],
    },
    {
      name: "$gte",
      description: "以上",
      example: "{ rating: { $gte: 4.5 } }",
      supportedBy: ["All"],
    },
    {
      name: "$lt",
      description: "より小さい",
      example: "{ stock: { $lt: 20 } }",
      supportedBy: ["All"],
    },
    {
      name: "$lte",
      description: "以下",
      example: "{ priority: { $lte: 3 } }",
      supportedBy: ["All"],
    },
  ]}
/>

<OperatorsTable
  title="配列演算子"
  operators={[
    {
      name: "$in",
      description: "配列内のいずれかの値に一致します",
      example: '{ category: { $in: ["A", "B"] } }',
      supportedBy: ["All"],
    },
    {
      name: "$nin",
      description: "いずれの値にも一致しません",
      example: '{ status: { $nin: ["deleted", "archived"] } }',
      supportedBy: ["All"],
    },
    {
      name: "$all",
      description: "すべての要素を含む配列に一致します",
      example: '{ tags: { $all: ["urgent", "high"] } }',
      supportedBy: ["Astra", "Pinecone", "Upstash", "MongoDB"],
    },
    {
      name: "$elemMatch",
      description: "条件を満たす配列要素に一致します",
      example: "{ scores: { $elemMatch: { $gt: 80 } } }",
      supportedBy: ["LibSQL", "PgVector", "MongoDB"],
    },
  ]}
/>

<OperatorsTable
  title="論理演算子"
  operators={[
    {
      name: "$and",
      description: "論理積（AND）",
      example: "{ $and: [{ price: { $gt: 100 } }, { stock: { $gt: 0 } }] }",
      supportedBy: ["All except Vectorize"],
    },
    {
      name: "$or",
      description: "論理和（OR）",
      example: '{ $or: [{ status: "active" }, { priority: "high" }] }',
      supportedBy: ["All except Vectorize"],
    },
    {
      name: "$not",
      description: "論理否定（NOT）",
      example: "{ price: { $not: { $lt: 100 } } }",
      supportedBy: [
        "Astra",
        "Qdrant",
        "Upstash",
        "PgVector",
        "LibSQL",
        "MongoDB",
      ],
    },
    {
      name: "$nor",
      description: "論理否定和（NOR）",
      example: '{ $nor: [{ status: "deleted" }, { archived: true }] }',
      supportedBy: ["Qdrant", "Upstash", "PgVector", "LibSQL", "MongoDB"],
    },
  ]}
/>

<OperatorsTable
  title="要素演算子"
  operators={[
    {
      name: "$exists",
      description: "フィールドが存在するドキュメントに一致します",
      example: "{ rating: { $exists: true } }",
      supportedBy: ["All except Vectorize, Chroma"],
    },
  ]}
/>

<OperatorsTable
  title="カスタムオペレーター"
  operators={[
    {
      name: "$contains",
      description: "テキストが部分文字列を含む",
      example: '{ description: { $contains: "sale" } }',
      supportedBy: ["Upstash", "LibSQL", "PgVector"],
    },
    {
      name: "$regex",
      description: "正規表現による一致",
      example: '{ name: { $regex: "^test" } }',
      supportedBy: ["Qdrant", "PgVector", "Upstash", "MongoDB"],
    },
    {
      name: "$size",
      description: "配列の長さをチェック",
      example: "{ tags: { $size: { $gt: 2 } } }",
      supportedBy: ["Astra", "LibSQL", "PgVector", "MongoDB"],
    },
    {
      name: "$geo",
      description: "地理空間クエリ",
      example: '{ location: { $geo: { type: "radius", ... } } }',
      supportedBy: ["Qdrant"],
    },
    {
      name: "$datetime",
      description: "日時範囲クエリ",
      example: '{ created: { $datetime: { range: { gt: "2024-01-01" } } } }',
      supportedBy: ["Qdrant"],
    },
    {
      name: "$hasId",
      description: "ベクターIDの存在チェック",
      example: '{ $hasId: ["id1", "id2"] }',
      supportedBy: ["Qdrant"],
    },
    {
      name: "$hasVector",
      description: "ベクターの存在チェック",
      example: "{ $hasVector: true }",
      supportedBy: ["Qdrant"],
    },
  ]}
/>

## 共通ルールと制限事項

1. フィールド名は以下のことができません:

   - ネストされたフィールドを参照する場合を除き、ドット（.）を含むこと
   - $で始まる、またはヌル文字を含むこと
   - 空文字列であること

2. 値は以下でなければなりません:

   - 有効なJSON型（文字列、数値、真偽値、オブジェクト、配列）
   - undefinedでないこと
   - 演算子に対して適切な型であること（例：数値比較には数値）

3. 論理演算子:

   - 有効な条件を含む必要があります
   - 空であってはなりません
   - 適切にネストされている必要があります
   - トップレベル、または他の論理演算子内でのみ使用可能です
   - フィールドレベルやフィールド内でネストして使用することはできません
   - 演算子内で使用することはできません
   - 有効: `{ "$and": [{ "field": { "$gt": 100 } }] }`
   - 有効: `{ "$or": [{ "$and": [{ "field": { "$gt": 100 } }] }] }`
   - 無効: `{ "field": { "$and": [{ "$gt": 100 }] } }`
   - 無効: `{ "field": { "$gt": { "$and": [{...}] } } }`

4. $not演算子:

   - オブジェクトでなければなりません
   - 空であってはなりません
   - フィールドレベルまたはトップレベルで使用可能です
   - 有効: `{ "$not": { "field": "value" } }`
   - 有効: `{ "field": { "$not": { "$eq": "value" } } }`

5. 演算子のネスト:
   - 論理演算子はフィールド条件を含む必要があり、直接演算子を含んではいけません
   - 有効: `{ "$and": [{ "field": { "$gt": 100 } }] }`
   - 無効: `{ "$and": [{ "$gt": 100 }] }`

## ストア固有の注意事項

### Astra

- ネストされたフィールドクエリはドット表記でサポートされています
- 配列フィールドはメタデータ内で明示的に配列として定義する必要があります
- メタデータの値は大文字と小文字が区別されます

### ChromaDB

- Whereフィルターは、フィルター対象のフィールドがメタデータに存在する場合のみ結果を返します
- 空のメタデータフィールドはフィルター結果に含まれません
- 否定一致（例：$ne）には、メタデータフィールドが存在している必要があります（フィールドが存在しないドキュメントには一致しません）

### Cloudflare Vectorize

- フィルタリングを使用する前に、明示的なメタデータインデックス作成が必要です
- フィルター対象のフィールドをインデックスするには `createMetadataIndex()` を使用してください
- Vectorizeインデックスごとに最大10個のメタデータインデックスが作成可能です
- 文字列値は最初の64バイトまでインデックスされます（UTF-8境界で切り捨て）
- 数値値はfloat64精度で扱われます
- フィルター用JSONは2048バイト未満である必要があります
- フィールド名にドット（.）を含めたり、$で始めることはできません
- フィールド名は512文字までに制限されています
- 新しいメタデータインデックス作成後、ベクトルを再アップサートしないとフィルター結果に含まれません
- 非常に大規模なデータセット（約1,000万ベクトル以上）では範囲クエリの精度が低下する場合があります

### LibSQL

- ドット表記によるネストされたオブジェクトクエリをサポートしています
- 配列フィールドは有効なJSON配列であることが検証されます
- 数値比較は適切な型処理を維持します
- 条件内の空配列も適切に処理されます
- メタデータは効率的なクエリのためにJSONBカラムに格納されます

### PgVector

- PostgreSQLのネイティブなJSONクエリ機能を完全サポート
- ネイティブの配列関数による効率的な配列操作
- 数値、文字列、ブール値の適切な型処理
- ネストされたフィールドクエリは内部的にPostgreSQLのJSONパス構文を使用
- メタデータは効率的なインデックス作成のためにJSONBカラムに格納されます

### Pinecone

- メタデータフィールド名は512文字までに制限されています
- 数値値は±1e38の範囲内でなければなりません
- メタデータ内の配列は合計64KBまでに制限されています
- ネストされたオブジェクトはドット表記でフラット化されます
- メタデータの更新はメタデータオブジェクト全体を置き換えます

### Qdrant

- ネストされた条件による高度なフィルタリングをサポート
- フィルタリングにはペイロード（メタデータ）フィールドを明示的にインデックス化する必要があります
- ジオスペーシャルクエリの効率的な処理
- null値や空値の特別な取り扱い
- ベクトル固有のフィルタリング機能
- 日時値はRFC 3339形式でなければなりません

### Upstash

- メタデータフィールドキーは512文字まで
- クエリサイズに制限あり（大きなIN句は避けてください）
- フィルターでnull/undefined値はサポートされていません
- 内部的にSQLライクな構文に変換されます
- 文字列比較は大文字小文字を区別します
- メタデータの更新はアトミックに行われます

### MongoDB

- メタデータフィルターに対してMongoDB/Siftクエリ構文を完全サポート
- すべての標準的な比較、配列、論理、要素演算子をサポート
- メタデータ内のネストされたフィールドや配列もサポート
- フィルタリングは `metadata` および元のドキュメント内容の両方に `filter` と `documentFilter` オプションで適用可能
- `filter` はメタデータオブジェクトに、`documentFilter` は元のドキュメントフィールドに適用されます
- フィルターサイズや複雑さに人工的な制限はありません（MongoDBのクエリ制限の範囲内）
- 最適なパフォーマンスのためにメタデータフィールドのインデックス作成を推奨します

## 関連

- [Astra](./astra)
- [Chroma](./chroma)
- [Cloudflare Vectorize](./vectorize)
- [LibSQL](./libsql)
- [MongoDB](./mongodb)
- [PgStore](./pg)
- [Pinecone](./pinecone)
- [Qdrant](./qdrant)
- [Upstash](./upstash)
