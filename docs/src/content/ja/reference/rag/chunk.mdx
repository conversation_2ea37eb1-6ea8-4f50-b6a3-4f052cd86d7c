---
title: "リファレンス: .chunk() | ドキュメント処理 | RAG | Mastra ドキュメント"
description: Mastra の chunk 関数のドキュメント。さまざまな戦略を用いてドキュメントを小さなセグメントに分割します。
---

# リファレンス: .chunk()

`.chunk()` 関数は、さまざまな戦略やオプションを用いてドキュメントをより小さなセグメントに分割します。

## 例

```typescript
import { MDocument } from "@mastra/rag";

const doc = MDocument.fromMarkdown(`
# Introduction
This is a sample document that we want to split into chunks.

## Section 1
Here is the first section with some content.

## Section 2 
Here is another section with different content.
`);

// Basic chunking with defaults
const chunks = await doc.chunk();

// Markdown-specific chunking with header extraction
const chunksWithMetadata = await doc.chunk({
  strategy: "markdown",
  headers: [
    ["#", "title"],
    ["##", "section"],
  ],
  extract: {
    summary: true, // Extract summaries with default settings
    keywords: true, // Extract keywords with default settings
  },
});
```

## パラメーター

<PropertiesTable
  content={[
    {
      name: "strategy",
      type: "'recursive' | 'character' | 'token' | 'markdown' | 'html' | 'json' | 'latex'",
      isOptional: true,
      description:
        "使用するチャンク化戦略。指定しない場合はドキュメントタイプに基づいてデフォルトが選択されます。チャンク化戦略によっては追加のオプションがあります。デフォルト: .md ファイル → 'markdown'、.html/.htm → 'html'、.json → 'json'、.tex → 'latex'、その他 → 'recursive'",
    },
    {
      name: "size",
      type: "number",
      isOptional: true,
      defaultValue: "512",
      description: "各チャンクの最大サイズ",
    },
    {
      name: "overlap",
      type: "number",
      isOptional: true,
      defaultValue: "50",
      description: "チャンク間で重複する文字数またはトークン数。",
    },
    {
      name: "separator",
      type: "string",
      isOptional: true,
      defaultValue: "\\n\\n",
      description:
        "分割に使用する文字。テキストコンテンツの場合、デフォルトはダブル改行です。",
    },
    {
      name: "isSeparatorRegex",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "セパレーターが正規表現パターンかどうか",
    },
    {
      name: "keepSeparator",
      type: "'start' | 'end'",
      isOptional: true,
      description: "セパレーターをチャンクの先頭または末尾に保持するかどうか",
    },
    {
      name: "extract",
      type: "ExtractParams",
      isOptional: true,
      description:
        "メタデータ抽出の設定。詳細は[ExtractParams のリファレンス](./extract-params)を参照してください。",
    },
  ]}
/>

## 戦略固有のオプション

戦略固有のオプションは、strategy パラメータと並んでトップレベルのパラメータとして渡されます。例えば：

```typescript showLineNumbers copy
// HTML戦略の例
const chunks = await doc.chunk({
  strategy: "html",
  headers: [
    ["h1", "title"],
    ["h2", "subtitle"],
  ], // HTML固有のオプション
  sections: [["div.content", "main"]], // HTML固有のオプション
  size: 500, // 一般的なオプション
});

// Markdown戦略の例
const chunks = await doc.chunk({
  strategy: "markdown",
  headers: [
    ["#", "title"],
    ["##", "section"],
  ], // Markdown固有のオプション
  stripHeaders: true, // Markdown固有のオプション
  overlap: 50, // 一般的なオプション
});

// Token戦略の例
const chunks = await doc.chunk({
  strategy: "token",
  encodingName: "gpt2", // Token固有のオプション
  modelName: "gpt-3.5-turbo", // Token固有のオプション
  size: 1000, // 一般的なオプション
});
```

以下に記載されているオプションは、設定オブジェクトのトップレベルで直接渡され、別の options オブジェクト内にネストされません。

### HTML

<PropertiesTable
  content={[
    {
      name: "headers",
      type: "Array<[string, string]>",
      description:
        "ヘッダー単位で分割するための [セレクタ, メタデータキー] のペアの配列",
    },
    {
      name: "sections",
      type: "Array<[string, string]>",
      description:
        "セクション単位で分割するための [セレクタ, メタデータキー] のペアの配列",
    },
    {
      name: "returnEachLine",
      type: "boolean",
      isOptional: true,
      description: "各行を個別のチャンクとして返すかどうか",
    },
  ]}
/>

### Markdown

<PropertiesTable
  content={[
    {
      name: "headers",
      type: "Array<[string, string]>",
      description: "[ヘッダーレベル, メタデータキー] のペアの配列",
    },
    {
      name: "stripHeaders",
      type: "boolean",
      isOptional: true,
      description: "出力からヘッダーを削除するかどうか",
    },
    {
      name: "returnEachLine",
      type: "boolean",
      isOptional: true,
      description: "各行を個別のチャンクとして返すかどうか",
    },
  ]}
/>

### Token

<PropertiesTable
  content={[
    {
      name: "encodingName",
      type: "string",
      isOptional: true,
      description: "使用するトークンエンコーディングの名前",
    },
    {
      name: "modelName",
      type: "string",
      isOptional: true,
      description: "トークナイズに使用するモデル名",
    },
  ]}
/>

### JSON

<PropertiesTable
  content={[
    {
      name: "maxSize",
      type: "number",
      description: "各チャンクの最大サイズ",
    },
    {
      name: "minSize",
      type: "number",
      isOptional: true,
      description: "各チャンクの最小サイズ",
    },
    {
      name: "ensureAscii",
      type: "boolean",
      isOptional: true,
      description: "ASCIIエンコーディングを保証するかどうか",
    },
    {
      name: "convertLists",
      type: "boolean",
      isOptional: true,
      description: "JSON内のリストを変換するかどうか",
    },
  ]}
/>

## 戻り値

チャンク化されたドキュメントを含む `MDocument` インスタンスを返します。各チャンクには以下が含まれます：

```typescript
interface DocumentNode {
  text: string;
  metadata: Record<string, any>;
  embedding?: number[];
}
```
