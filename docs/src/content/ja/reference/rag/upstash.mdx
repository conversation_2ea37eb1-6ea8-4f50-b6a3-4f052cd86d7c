---
title: "リファレンス: Upstashベクトルストア | ベクトルデータベース | RAG | Mastraドキュメント"
description: Mastraにおける、Upstash Vectorを使用したベクトル検索を提供するUpstashVectorクラスのドキュメント。
---

# Upstash Vector Store

UpstashVectorクラスは、[Upstash Vector](https://upstash.com/vector)を使用したベクトル検索を提供します。Upstash Vectorは、メタデータフィルタリング機能を備えたベクトル類似性検索を提供するサーバーレスベクトルデータベースサービスです。

## コンストラクタオプション

<PropertiesTable
  content={[
    {
      name: "url",
      type: "string",
      description: "Upstash Vectorデータベース URL",
    },
    {
      name: "token",
      type: "string",
      description: "Upstash Vector APIトークン",
    },
  ]}
/>

## メソッド

### createIndex()

注意：Upstashではインデックスは自動的に作成されるため、このメソッドは何も実行しません。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "作成するインデックスの名前",
    },
    {
      name: "dimension",
      type: "number",
      description: "ベクトルの次元数（埋め込みモデルと一致する必要があります）",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description: "類似性検索の距離メトリック",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "アップサートするインデックスの名前",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "埋め込みベクトルの配列",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "各ベクトルのメタデータ",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description:
        "オプションのベクトルID（提供されない場合は自動生成されます）",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "クエリを実行するインデックスの名前",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "類似ベクトルを検索するためのクエリベクトル",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "返す結果の数",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "クエリのメタデータフィルター",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "結果にベクトルを含めるかどうか",
    },
  ]}
/>

### listIndexes()

インデックス名（名前空間）の文字列配列を返します。

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "説明するインデックスの名前",
    },
  ]}
/>

戻り値：

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "削除するインデックス（名前空間）の名前",
    },
  ]}
/>

### updateVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "更新するインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "更新するアイテムのID",
    },
    {
      name: "update",
      type: "object",
      description: "ベクトルやメタデータを含む更新オブジェクト",
    },
  ]}
/>

`update`オブジェクトは以下のプロパティを持つことができます：

- `vector`（オプション）：新しいベクトルを表す数値の配列。
- `metadata`（オプション）：メタデータのキーと値のペアのレコード。

`vector`も`metadata`も提供されない場合、または`metadata`のみが提供される場合はエラーがスローされます。

### deleteVector()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "アイテムを削除するインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "削除するアイテムのID",
    },
  ]}
/>

指定されたインデックスからIDによってアイテムを削除しようとします。削除に失敗した場合はエラーメッセージをログに記録します。

## レスポンスタイプ

クエリ結果は以下の形式で返されます：

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[]; // Only included if includeVector is true
}
```

## エラー処理

ストアは捕捉可能な型付きエラーをスローします：

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

## 環境変数

必要な環境変数：

- `UPSTASH_VECTOR_URL`: あなたのUpstash VectorデータベースのURL
- `UPSTASH_VECTOR_TOKEN`: あなたのUpstash Vector APIトークン

## 関連

- [メタデータフィルター](./metadata-filters)
