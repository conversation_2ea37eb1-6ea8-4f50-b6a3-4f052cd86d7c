---
title: "リファレンス: embed() | ドキュメント埋め込み | RAG | Mastra ドキュメント"
description: MastraでAI SDKを使用した埋め込み機能のドキュメント。
---

# 埋め込み

Mastraは、AI SDKの`embed`および`embedMany`関数を使用してテキスト入力のベクトル埋め込みを生成し、類似性検索やRAGワークフローを実現します。

## 単一埋め込み

`embed` 関数は、単一のテキスト入力に対してベクトル埋め込みを生成します。

```typescript
import { embed } from "ai";

const result = await embed({
  model: openai.embedding("text-embedding-3-small"),
  value: "Your text to embed",
  maxRetries: 2, // optional, defaults to 2
});
```

### パラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "EmbeddingModel",
      description:
        "使用する埋め込みモデル（例: openai.embedding('text-embedding-3-small')）",
    },
    {
      name: "value",
      type: "string | Record<string, any>",
      description: "埋め込むテキストコンテンツまたはオブジェクト",
    },
    {
      name: "maxRetries",
      type: "number",
      description:
        "埋め込み呼び出しごとの最大リトライ回数。リトライを無効にするには0を設定します。",
      isOptional: true,
      defaultValue: "2",
    },
    {
      name: "abortSignal",
      type: "AbortSignal",
      description:
        "リクエストをキャンセルするためのオプションのアボートシグナル",
      isOptional: true,
    },
    {
      name: "headers",
      type: "Record<string, string>",
      description:
        "リクエストに追加するHTTPヘッダー（HTTPベースのプロバイダーのみ）",
      isOptional: true,
    },
  ]}
/>

### 戻り値

<PropertiesTable
  content={[
    {
      name: "embedding",
      type: "number[]",
      description: "入力に対する埋め込みベクトル",
    },
  ]}
/>

## 複数の埋め込み

複数のテキストを一度に埋め込むには、`embedMany` 関数を使用します。

```typescript
import { embedMany } from "ai";

const result = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: ["First text", "Second text", "Third text"],
  maxRetries: 2, // optional, defaults to 2
});
```

### パラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "EmbeddingModel",
      description:
        "使用する埋め込みモデル（例: openai.embedding('text-embedding-3-small')）",
    },
    {
      name: "values",
      type: "string[] | Record<string, any>[]",
      description: "埋め込むテキストコンテンツまたはオブジェクトの配列",
    },
    {
      name: "maxRetries",
      type: "number",
      description:
        "埋め込み呼び出しごとの最大リトライ回数。リトライを無効にするには0を設定します。",
      isOptional: true,
      defaultValue: "2",
    },
    {
      name: "abortSignal",
      type: "AbortSignal",
      description:
        "リクエストをキャンセルするためのオプションのアボートシグナル",
      isOptional: true,
    },
    {
      name: "headers",
      type: "Record<string, string>",
      description:
        "リクエスト用の追加HTTPヘッダー（HTTPベースのプロバイダーのみ）",
      isOptional: true,
    },
  ]}
/>

### 戻り値

<PropertiesTable
  content={[
    {
      name: "embeddings",
      type: "number[][]",
      description: "入力値に対応する埋め込みベクトルの配列",
    },
  ]}
/>

## 使用例

```typescript
import { embed, embedMany } from "ai";
import { openai } from "@ai-sdk/openai";

// Single embedding
const singleResult = await embed({
  model: openai.embedding("text-embedding-3-small"),
  value: "What is the meaning of life?",
});

// Multiple embeddings
const multipleResult = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: [
    "First question about life",
    "Second question about universe",
    "Third question about everything",
  ],
});
```

Vercel AI SDK における埋め込みの詳細については、以下をご覧ください：

- [AI SDK 埋め込みの概要](https://sdk.vercel.ai/docs/ai-sdk-core/embeddings)
- [embed()](https://sdk.vercel.ai/docs/reference/ai-sdk-core/embed)
- [embedMany()](https://sdk.vercel.ai/docs/reference/ai-sdk-core/embed-many)
