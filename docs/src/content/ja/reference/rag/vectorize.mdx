---
title: "リファレンス: Cloudflare ベクトルストア | ベクトルデータベース | RAG | Mastra ドキュメント"
description: Mastraの CloudflareVectorクラスに関するドキュメント。Cloudflare Vectorizeを使用したベクトル検索を提供します。
---

# Cloudflare Vector Store

CloudflareVectorクラスは、Cloudflareのエッジネットワークと統合されたベクトルデータベースサービスである[Cloudflare Vectorize](https://developers.cloudflare.com/vectorize/)を使用してベクトル検索を提供します。

## コンストラクタオプション

<PropertiesTable
  content={[
    {
      name: "accountId",
      type: "string",
      description: "CloudflareアカウントのアカウントID",
    },
    {
      name: "apiToken",
      type: "string",
      description: "Vectorize権限を持つCloudflare APIトークン",
    },
  ]}
/>

## メソッド

### createIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "作成するインデックスの名前",
    },
    {
      name: "dimension",
      type: "number",
      description: "ベクトルの次元数（埋め込みモデルと一致する必要があります）",
    },
    {
      name: "metric",
      type: "'cosine' | 'euclidean' | 'dotproduct'",
      isOptional: true,
      defaultValue: "cosine",
      description:
        "類似性検索の距離メトリック（dotproductはドット積に対応します）",
    },
  ]}
/>

### upsert()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "アップサートするインデックスの名前",
    },
    {
      name: "vectors",
      type: "number[][]",
      description: "埋め込みベクトルの配列",
    },
    {
      name: "metadata",
      type: "Record<string, any>[]",
      isOptional: true,
      description: "各ベクトルのメタデータ",
    },
    {
      name: "ids",
      type: "string[]",
      isOptional: true,
      description: "オプションのベクトルID（指定しない場合は自動生成されます）",
    },
  ]}
/>

### query()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "クエリを実行するインデックスの名前",
    },
    {
      name: "queryVector",
      type: "number[]",
      description: "類似ベクトルを検索するためのクエリベクトル",
    },
    {
      name: "topK",
      type: "number",
      isOptional: true,
      defaultValue: "10",
      description: "返す結果の数",
    },
    {
      name: "filter",
      type: "Record<string, any>",
      isOptional: true,
      description: "クエリのメタデータフィルター",
    },
    {
      name: "includeVector",
      type: "boolean",
      isOptional: true,
      defaultValue: "false",
      description: "結果にベクトルを含めるかどうか",
    },
  ]}
/>

### listIndexes()

インデックス名の文字列配列を返します。

### describeIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "説明するインデックスの名前",
    },
  ]}
/>

戻り値：

```typescript copy
interface IndexStats {
  dimension: number;
  count: number;
  metric: "cosine" | "euclidean" | "dotproduct";
}
```

### deleteIndex()

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "削除するインデックスの名前",
    },
  ]}
/>

### createMetadataIndex()

フィルタリングを可能にするためにメタデータフィールドにインデックスを作成します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "メタデータフィールドを含むインデックスの名前",
    },
    {
      name: "propertyName",
      type: "string",
      description: "インデックスを作成するメタデータフィールドの名前",
    },
    {
      name: "indexType",
      type: "'string' | 'number' | 'boolean'",
      description: "メタデータフィールドの型",
    },
  ]}
/>

### deleteMetadataIndex()

メタデータフィールドからインデックスを削除します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "メタデータフィールドを含むインデックスの名前",
    },
    {
      name: "propertyName",
      type: "string",
      description: "インデックスを削除するメタデータフィールドの名前",
    },
  ]}
/>

### listMetadataIndexes()

インデックスのすべてのメタデータフィールドインデックスを一覧表示します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "メタデータインデックスを一覧表示するインデックスの名前",
    },
  ]}
/>

### updateVector()

インデックス内の特定のIDのベクトルまたはメタデータを更新します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "更新するIDを含むインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "更新するベクトルまたはメタデータの一意の識別子",
    },
    {
      name: "update",
      type: "{ vector?: number[]; metadata?: Record<string, any>; }",
      description: "更新するベクトルやメタデータを含むオブジェクト",
    },
  ]}
/>

### deleteVector()

インデックス内の特定のIDに対するベクトルとそれに関連するメタデータを削除します。

<PropertiesTable
  content={[
    {
      name: "indexName",
      type: "string",
      description: "削除するIDを含むインデックスの名前",
    },
    {
      name: "id",
      type: "string",
      description: "削除するベクトルとメタデータの一意の識別子",
    },
  ]}
/>

## レスポンスタイプ

クエリ結果は以下の形式で返されます：

```typescript copy
interface QueryResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  vector?: number[];
}
```

## エラー処理

ストアは型付きエラーをスローし、それをキャッチすることができます：

```typescript copy
try {
  await store.query({
    indexName: "index_name",
    queryVector: queryVector,
  });
} catch (error) {
  if (error instanceof VectorStoreError) {
    console.log(error.code); // 'connection_failed' | 'invalid_dimension' | etc
    console.log(error.details); // Additional error context
  }
}
```

## 環境変数

必要な環境変数：

- `CLOUDFLARE_ACCOUNT_ID`: あなたのCloudflareアカウントID
- `CLOUDFLARE_API_TOKEN`: Vectorize権限を持つCloudflare APIトークン

## 関連

- [メタデータフィルター](./metadata-filters)
