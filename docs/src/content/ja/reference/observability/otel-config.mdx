---
title: "リファレンス: OtelConfig | Mastra Observability ドキュメント"
description: OpenTelemetry のインストルメンテーション、トレーシング、およびエクスポート動作を設定する OtelConfig オブジェクトのドキュメント。
---

# `OtelConfig`

`OtelConfig` オブジェクトは、アプリケーション内で OpenTelemetry のインストルメンテーション、トレーシング、およびエクスポートの動作を設定するために使用されます。そのプロパティを調整することで、テレメトリーデータ（トレースなど）の収集、サンプリング、エクスポート方法を制御できます。

Mastra で `OtelConfig` を使用するには、Mastra の初期化時に `telemetry` キーの値として渡します。これにより、Mastra はトレーシングとインストルメンテーションのためにカスタムの OpenTelemetry 設定を使用するように構成されます。

```typescript showLineNumbers copy
import { Mastra } from "mastra";

const otelConfig: OtelConfig = {
  serviceName: "my-awesome-service",
  enabled: true,
  sampling: {
    type: "ratio",
    probability: 0.5,
  },
  export: {
    type: "otlp",
    endpoint: "https://otel-collector.example.com/v1/traces",
    headers: {
      Authorization: "Bearer YOUR_TOKEN_HERE",
    },
  },
};
```

### プロパティ

<PropertiesTable
  content={[
    {
      name: "serviceName",
      type: "string",
      isOptional: true,
      default: "default-service",
      description:
        "テレメトリーバックエンドでサービスを識別するために使用される人間が読める名前です。",
    },
    {
      name: "enabled",
      type: "boolean",
      isOptional: true,
      default: "true",
      description: "テレメトリーの収集とエクスポートが有効かどうかを示します。",
    },
    {
      name: "sampling",
      type: "SamplingStrategy",
      isOptional: true,
      description:
        "トレースのサンプリング戦略を定義し、どれだけのデータを収集するかを制御します。",
      properties: [
        {
          name: "type",
          type: `'ratio' | 'always_on' | 'always_off' | 'parent_based'`,
          description: "サンプリング戦略のタイプを指定します。",
        },
        {
          name: "probability",
          type: "number (0.0 to 1.0)",
          isOptional: true,
          description:
            "`ratio` または `parent_based` 戦略の場合、サンプリング確率を定義します。",
        },
        {
          name: "root",
          type: "object",
          isOptional: true,
          description:
            "`parent_based` 戦略の場合、ルートレベルの確率サンプリングを設定します。",
          properties: [
            {
              name: "probability",
              type: "number (0.0 to 1.0)",
              isOptional: true,
              description:
                "`parent_based` 戦略におけるルートトレースのサンプリング確率です。",
            },
          ],
        },
      ],
    },
    {
      name: "export",
      type: "object",
      isOptional: true,
      description: "収集したテレメトリーデータのエクスポート設定です。",
      properties: [
        {
          name: "type",
          type: `'otlp' | 'console'`,
          description:
            "エクスポーターのタイプを指定します。外部エクスポーターには `otlp`、開発用には `console` を使用します。",
        },
        {
          name: "endpoint",
          type: "string",
          isOptional: true,
          description:
            "`otlp` タイプの場合、トレースを送信する OTLP エンドポイントの URL です。",
        },
        {
          name: "headers",
          type: "Record<string, string>",
          isOptional: true,
          description:
            "OTLP リクエストと共に送信する追加ヘッダーです。認証やルーティングに便利です。",
        },
      ],
    },
  ]}
/>
