---
title: "リファレンス: Dash0 統合 | Mastra Observability ドキュメント"
description: Mastra を Open Telementry ネイティブのオブザーバビリティソリューションである Dash0 と統合するためのドキュメント。
---

# Dash0

Dash0は、Open Telementryネイティブのオブザーバビリティソリューションであり、フルスタックのモニタリング機能に加え、PersesやPrometheusなど他のCNCFプロジェクトとの統合も提供します。

## 設定

Dash0 を Mastra で使用するには、以下の環境変数を設定してください：

```env
OTEL_EXPORTER_OTLP_ENDPOINT=https://ingress.<region>.dash0.com
OTEL_EXPORTER_OTLP_HEADERS=Authorization=Bearer <your-auth-token>, Dash0-Dataset=<optional-dataset>
```

## 実装

Mastra を Dash0 で使用するための設定方法は以下の通りです。

```typescript
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "your-service-name",
    enabled: true,
    export: {
      type: "otlp",
    },
  },
});
```

## ダッシュボード

[Dash0](https://www.dash0.com/) であなたのDash0ダッシュボードにアクセスし、[Dash0 Integration Hub](https://www.dash0.com/hub/integrations)でさらに多くの[分散トレーシング](https://www.dash0.com/distributed-tracing)連携方法を見つけましょう。
