---
title: "リファレンス: コンテキスト位置 | メトリクス | Evals | Mastra ドキュメント"
description: Mastra におけるコンテキスト位置メトリクスのドキュメント。クエリおよび出力に対する関連性に基づいて、コンテキストノードの順序を評価します。
---

# ContextPositionMetric

`ContextPositionMetric` クラスは、クエリおよび出力に対する関連性に基づいて、コンテキストノードがどれだけ適切に並べられているかを評価します。位置に重み付けしたスコアリングを用いることで、最も関連性の高いコンテキストがシーケンスの早い段階に現れることの重要性を強調します。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { ContextPositionMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new ContextPositionMetric(model, {
  context: [
    "Photosynthesis is a biological process used by plants to create energy from sunlight.",
    "The process of photosynthesis produces oxygen as a byproduct.",
    "Plants need water and nutrients from the soil to grow.",
  ],
});

const result = await metric.measure(
  "What is photosynthesis?",
  "Photosynthesis is the process by which plants convert sunlight into energy.",
);

console.log(result.score); // Position score from 0-1
console.log(result.info.reason); // Explanation of the score
```

## コンストラクターのパラメーター

<PropertiesTable
  content={[
    {
      name: "model",
      type: "ModelConfig",
      description: "コンテキストの位置評価に使用されるモデルの設定",
      isOptional: false,
    },
    {
      name: "options",
      type: "ContextPositionMetricOptions",
      description: "このメトリックの設定オプション",
      isOptional: false,
    },
  ]}
/>

### ContextPositionMetricOptions

<PropertiesTable
  content={[
    {
      name: "scale",
      type: "number",
      description: "スコアの最大値",
      isOptional: true,
      defaultValue: "1",
    },
    {
      name: "context",
      type: "string[]",
      description: "取得順に並んだコンテキスト要素の配列",
      isOptional: false,
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "元のクエリまたはプロンプト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "評価対象となる生成された応答",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "ポジションスコア（0からスケール、デフォルトは0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "スコアの理由を含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description: "スコアの詳細な説明",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリクスは、バイナリの関連性評価と位置に基づく重み付けを通じて、コンテキストの配置を評価します。

### スコアリングプロセス

1. コンテキストの関連性を評価します:

   - 各要素にバイナリ判定（はい／いいえ）を割り当てる
   - シーケンス内の位置を記録する
   - 関連性の理由を記録する

2. 位置の重みを適用します:
   - 先頭の位置ほど重みが大きい（重み = 1/(位置 + 1)）
   - 関連する要素の重みを合計する
   - 最大可能スコアで正規化する

最終スコア: `(weighted_sum / max_possible_sum) * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0: 最適 - 最も関連性の高いコンテキストが最初
- 0.7-0.9: 良好 - 関連性の高いコンテキストが主に早い段階
- 0.4-0.6: 混在 - 関連性の高いコンテキストが散在
- 0.1-0.3: 最適でない - 関連性の高いコンテキストが主に後半
- 0.0: 不適切な順序 - 関連性の高いコンテキストが最後または欠落

## 分析付きの例

```typescript
import { openai } from "@ai-sdk/openai";
import { ContextPositionMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new ContextPositionMetric(model, {
  context: [
    "A balanced diet is important for health.",
    "Exercise strengthens the heart and improves blood circulation.",
    "Regular physical activity reduces stress and anxiety.",
    "Exercise equipment can be expensive.",
  ],
});

const result = await metric.measure(
  "What are the benefits of exercise?",
  "Regular exercise improves cardiovascular health and mental wellbeing.",
);

// Example output:
// {
//   score: 0.5,
//   info: {
//     reason: "The score is 0.5 because while the second and third contexts are highly
//           relevant to the benefits of exercise, they are not optimally positioned at
//           the beginning of the sequence. The first and last contexts are not relevant
//           to the query, which impacts the position-weighted scoring."
//   }
// }
```

## 関連

- [Context Precision Metric](./context-precision)
- [Answer Relevancy Metric](./answer-relevancy)
- [Completeness Metric](./completeness)

* [Context Relevancy Metric](./context-relevancy)
