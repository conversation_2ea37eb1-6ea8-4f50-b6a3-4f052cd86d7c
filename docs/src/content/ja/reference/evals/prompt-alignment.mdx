---
title: "リファレンス: プロンプトアラインメント | メトリクス | Evals | Mastra ドキュメント"
description: Mastra におけるプロンプトアラインメントメトリクスのドキュメント。LLM の出力が与えられたプロンプト指示にどれだけ従っているかを評価します。
---

# PromptAlignmentMetric

`PromptAlignmentMetric` クラスは、LLM の出力が与えられたプロンプト指示にどれだけ厳密に従っているかを評価します。各指示が正確に守られているかを判定するジャッジベースのシステムを使用し、逸脱があった場合には詳細な理由を提供します。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { PromptAlignmentMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const instructions = [
  "Start sentences with capital letters",
  "End each sentence with a period",
  "Use present tense",
];

const metric = new PromptAlignmentMetric(model, {
  instructions,
  scale: 1,
});

const result = await metric.measure(
  "describe the weather",
  "The sun is shining. Clouds float in the sky. A gentle breeze blows.",
);

console.log(result.score); // Alignment score from 0-1
console.log(result.info.reason); // Explanation of the score
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "LanguageModel",
      description: "命令整合性を評価するために使用されるモデルの設定",
      isOptional: false,
    },
    {
      name: "options",
      type: "PromptAlignmentOptions",
      description: "メトリクスの設定オプション",
      isOptional: false,
    },
  ]}
/>

### PromptAlignmentOptions

<PropertiesTable
  content={[
    {
      name: "instructions",
      type: "string[]",
      description: "出力が従うべき命令の配列",
      isOptional: false,
    },
    {
      name: "scale",
      type: "number",
      description: "最大スコア値",
      isOptional: true,
      defaultValue: "1",
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "元のプロンプトまたはクエリ",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "評価対象となるLLMの応答",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "アラインメントスコア（0からスケールまで、デフォルトは0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "指示遵守に関する詳細なメトリクスを含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description: "スコアおよび指示遵守の詳細な説明",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

この指標は、以下の観点からインストラクションの整合性を評価します。

- 各インストラクションの適用可能性の評価
- 適用可能なインストラクションに対する厳格な遵守評価
- すべての判定に対する詳細な理由付け
- 適用可能なインストラクションに基づく比例配点

### インストラクションの判定

各インストラクションには、次のいずれかの判定が与えられます。

- "yes"：インストラクションが適用可能で、完全に遵守されている
- "no"：インストラクションが適用可能だが、遵守されていない、または部分的にしか遵守されていない
- "n/a"：インストラクションが該当する状況に適用できない

### スコアリングプロセス

1. インストラクションの適用可能性を評価：

   - 各インストラクションが状況に適用できるかを判断
   - 関連性のないインストラクションは "n/a" としてマーク
   - ドメイン固有の要件を考慮

2. 適用可能なインストラクションの遵守状況を評価：

   - 各適用可能なインストラクションを個別に評価
   - "yes" の判定には完全な遵守が必要
   - すべての判定に対して具体的な理由を記録

3. 整合性スコアを算出：
   - 遵守されたインストラクション（"yes" 判定）の数をカウント
   - 適用可能なインストラクションの総数（"n/a" を除く）で割る
   - 設定された範囲にスケーリング

最終スコア：`(followed_instructions / applicable_instructions) * scale`

### 重要な考慮事項

- 出力が空の場合：
  - すべての書式指示は適用可能と見なされる
  - 要件を満たせないため "no" としてマーク
- ドメイン固有のインストラクション：
  - 問い合わせたドメインに関する場合は常に適用可能
  - 遵守されていない場合は "no"、"n/a" にはしない
- "n/a" の判定：
  - 完全に異なるドメインの場合のみ使用
  - 最終スコアの計算には影響しない

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0：すべての適用可能なインストラクションが完全に遵守されている
- 0.7-0.9：ほとんどの適用可能なインストラクションが遵守されている
- 0.4-0.6：適用可能なインストラクションの遵守状況が混在している
- 0.1-0.3：適用可能なインストラクションの遵守が限定的
- 0.0：適用可能なインストラクションがまったく遵守されていない

## 例と分析

```typescript
import { openai } from "@ai-sdk/openai";
import { PromptAlignmentMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new PromptAlignmentMetric(model, {
  instructions: [
    "Use bullet points for each item",
    "Include exactly three examples",
    "End each point with a semicolon"
  ],
  scale: 1
});

const result = await metric.measure(
  "List three fruits",
  "• Apple is red and sweet;
• Banana is yellow and curved;
• Orange is citrus and round."
);

// Example output:
// {
//   score: 1.0,
//   info: {
//     reason: "The score is 1.0 because all instructions were followed exactly:
//           bullet points were used, exactly three examples were provided, and
//           each point ends with a semicolon."
//   }
// }

const result2 = await metric.measure(
  "List three fruits",
  "1. Apple
2. Banana
3. Orange and Grape"
);

// Example output:
// {
//   score: 0.33,
//   info: {
//     reason: "The score is 0.33 because: numbered lists were used instead of bullet points,
//           no semicolons were used, and four fruits were listed instead of exactly three."
//   }
// }
```

## 関連

- [Answer Relevancy Metric](./answer-relevancy)
- [Keyword Coverage Metric](./keyword-coverage)
