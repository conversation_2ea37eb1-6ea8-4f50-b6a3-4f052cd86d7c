---
title: "リファレンス: コンテンツ類似度 | Evals | Mastra ドキュメント"
description: Mastra のコンテンツ類似度メトリクスに関するドキュメント。これは文字列間のテキスト類似度を測定し、マッチングスコアを提供します。
---

# ContentSimilarityMetric

`ContentSimilarityMetric` クラスは、2つの文字列間のテキスト類似度を測定し、それらがどれだけ一致しているかを示すスコアを提供します。大文字と小文字の区別や空白の扱いについて、設定可能なオプションをサポートしています。

## 基本的な使い方

```typescript
import { ContentSimilarityMetric } from "@mastra/evals/nlp";

const metric = new ContentSimilarityMetric({
  ignoreCase: true,
  ignoreWhitespace: true,
});

const result = await metric.measure("Hello, world!", "hello world");

console.log(result.score); // 類似度スコア（0〜1）
console.log(result.info); // 詳細な類似度メトリクス
```

## コンストラクタのパラメーター

<PropertiesTable
  content={[
    {
      name: "options",
      type: "ContentSimilarityOptions",
      description: "類似度比較のための設定オプション",
      isOptional: true,
      defaultValue: "{ ignoreCase: true, ignoreWhitespace: true }",
    },
  ]}
/>

### ContentSimilarityOptions

<PropertiesTable
  content={[
    {
      name: "ignoreCase",
      type: "boolean",
      description: "文字列を比較する際に大文字と小文字の違いを無視するかどうか",
      isOptional: true,
      defaultValue: "true",
    },
    {
      name: "ignoreWhitespace",
      type: "boolean",
      description: "文字列を比較する際に空白を正規化するかどうか",
      isOptional: true,
      defaultValue: "true",
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "比較対象となる参照テキスト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "類似度を評価するテキスト",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "類似度スコア（0～1）。1は完全な類似を示します",
    },
    {
      name: "info",
      type: "object",
      description: "詳細な類似度指標",
      properties: [
        {
          type: "number",
          parameters: [
            {
              name: "similarity",
              type: "number",
              description: "2つのテキスト間の生の類似度スコア",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、文字レベルでの一致と設定可能なテキスト正規化を通じて、テキストの類似性を評価します。

### スコアリングプロセス

1. テキストを正規化します:

   - 大文字・小文字の正規化（ignoreCase: true の場合）
   - 空白の正規化（ignoreWhitespace: true の場合）

2. 処理済みの文字列を文字列類似度アルゴリズムで比較します:
   - 文字の並びを分析
   - 単語の境界を揃える
   - 相対的な位置を考慮
   - 長さの違いを考慮

最終スコア: `similarity_value * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0: 完全一致 - テキストが全く同じ
- 0.7-0.9: 高い類似度 - ほとんど一致する内容
- 0.4-0.6: 中程度の類似度 - 部分的な一致
- 0.1-0.3: 低い類似度 - 一致するパターンが少ない
- 0.0: 類似性なし - 全く異なるテキスト

## 異なるオプションを使った例

```typescript
import { ContentSimilarityMetric } from "@mastra/evals/nlp";

// 大文字・小文字を区別する比較
const caseSensitiveMetric = new ContentSimilarityMetric({
  ignoreCase: false,
  ignoreWhitespace: true,
});

const result1 = await caseSensitiveMetric.measure("Hello World", "hello world"); // 大文字・小文字の違いによりスコアが低くなる

// 出力例:
// {
//   score: 0.75,
//   info: { similarity: 0.75 }
// }

// 空白文字を厳密に比較
const strictWhitespaceMetric = new ContentSimilarityMetric({
  ignoreCase: true,
  ignoreWhitespace: false,
});

const result2 = await strictWhitespaceMetric.measure(
  "Hello   World",
  "Hello World",
); // 空白の違いによりスコアが低くなる

// 出力例:
// {
//   score: 0.85,
//   info: { similarity: 0.85 }
// }
```

## 関連

- [Completeness Metric](./completeness)
- [Textual Difference Metric](./textual-difference)
- [Answer Relevancy Metric](./answer-relevancy)
- [Keyword Coverage Metric](./keyword-coverage)
