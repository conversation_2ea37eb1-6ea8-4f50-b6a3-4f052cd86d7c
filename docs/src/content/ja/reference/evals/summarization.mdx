---
title: "リファレンス: 要約 | メトリクス | Evals | Mastra ドキュメント"
description: Mastra における要約メトリクスのドキュメント。コンテンツと事実の正確性に関して、LLM が生成した要約の品質を評価します。
---

# SummarizationMetric

,
`SummarizationMetric`は、LLMによる要約が元のテキストの内容をどれだけ正確に捉え、事実に基づいているかを評価します。この指標は、アライメント（事実の正確性）とカバレッジ（重要な情報の網羅）の2つの側面を組み合わせており、どちらの品質も優れた要約に不可欠であることを保証するために最小スコアを使用します。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { SummarizationMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new SummarizationMetric(model);

const result = await metric.measure(
  "The company was founded in 1995 by <PERSON>. It started with 10 employees and grew to 500 by 2020. The company is based in Seattle.",
  "Founded in 1995 by <PERSON>, the company grew from 10 to 500 employees by 2020.",
);

console.log(result.score); // Score from 0-1
console.log(result.info); // Object containing detailed metrics about the summary
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "LanguageModel",
      description: "要約を評価するために使用されるモデルの設定",
      isOptional: false,
    },
    {
      name: "options",
      type: "SummarizationMetricOptions",
      description: "メトリクスの設定オプション",
      isOptional: true,
      defaultValue: "{ scale: 1 }",
    },
  ]}
/>

### SummarizationMetricOptions

<PropertiesTable
  content={[
    {
      name: "scale",
      type: "number",
      description: "スコアの最大値",
      isOptional: true,
      defaultValue: "1",
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "要約する元のテキスト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "評価する生成された要約",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "要約スコア（0からスケール、デフォルトは0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "要約に関する詳細な指標を含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description:
                "スコアの詳細な説明（整合性と網羅性の両方の観点を含む）",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "alignmentScore",
              type: "number",
              description: "整合性スコア（0から1）",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "coverageScore",
              type: "number",
              description: "網羅性スコア（0から1）",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、要約を2つの重要な要素で評価します。

1. **アライメントスコア**：事実の正確性を測定

   - 要約から主張を抽出
   - 各主張を元のテキストと照合
   - 「yes」「no」「unsure」の判定を付与

2. **カバレッジスコア**：重要情報の網羅性を測定
   - 元のテキストから重要な質問を生成
   - 要約がこれらの質問に答えているか確認
   - 情報の包含と網羅性を評価

### スコアリングプロセス

1. アライメントスコアを計算：

   - 要約から主張を抽出
   - 元テキストと照合
   - 計算式：`supported_claims / total_claims`

2. カバレッジスコアを決定：
   - 元テキストから質問を生成
   - 要約が回答しているか確認
   - 完全性を評価
   - 計算式：`answerable_questions / total_questions`

最終スコア：`min(alignment_score, coverage_score) * scale`

### スコアの解釈

（0からscale、デフォルトは0-1）

- 1.0：完璧な要約 - 完全に事実に基づき、すべての重要情報を網羅
- 0.7-0.9：強力な要約だが、わずかな抜けや軽微な不正確さあり
- 0.4-0.6：中程度の品質で、重要な抜けや不正確さが目立つ
- 0.1-0.3：大きな抜けや事実誤認がある低品質な要約
- 0.0：無効な要約 - 完全に不正確、または重要な情報が欠落

## 分析付きの例

```typescript
import { openai } from "@ai-sdk/openai";
import { SummarizationMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new SummarizationMetric(model);

const result = await metric.measure(
  "The electric car company Tesla was founded in 2003 by Martin Eberhard and Marc Tarpenning. Elon Musk joined in 2004 as the largest investor and became CEO in 2008. The company's first car, the Roadster, was launched in 2008.",
  "Tesla, founded by Elon Musk in 2003, revolutionized the electric car industry starting with the Roadster in 2008.",
);

// Example output:
// {
//   score: 0.5,
//   info: {
//     reason: "The score is 0.5 because while the coverage is good (0.75) - mentioning the founding year,
//           first car model, and launch date - the alignment score is lower (0.5) due to incorrectly
//           attributing the company's founding to Elon Musk instead of Martin Eberhard and Marc Tarpenning.
//           The final score takes the minimum of these two scores to ensure both factual accuracy and
//           coverage are necessary for a good summary."
//     alignmentScore: 0.5,
//     coverageScore: 0.75,
//   }
// }
```

## 関連

- [Faithfulness Metric](./faithfulness)
- [Completeness Metric](./completeness)
- [Contextual Recall Metric](./contextual-recall)
- [Hallucination Metric](./hallucination)
