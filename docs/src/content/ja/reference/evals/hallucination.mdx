---
title: "リファレンス: ハルシネーション | メトリクス | Evals | Mastra ドキュメント"
description: Mastra におけるハルシネーションメトリクスのドキュメント。提供されたコンテキストと矛盾する箇所を特定することで、LLM出力の事実的正確性を評価します。
---

# HallucinationMetric

`HallucinationMetric`は、LLMが生成した情報が事実に基づいているかどうかを、与えられたコンテキストと出力を比較することで評価します。このメトリクスは、コンテキストと出力の間に直接的な矛盾があるかどうかを特定することで、ハルシネーションを測定します。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { HallucinationMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new HallucinationMetric(model, {
  context: [
    "Tesla was founded in 2003 by <PERSON> and <PERSON> in San Carlos, California.",
  ],
});

const result = await metric.measure(
  "Tell me about <PERSON><PERSON>'s founding.",
  "Tesla was founded in 2004 by <PERSON><PERSON> in California.",
);

console.log(result.score); // Score from 0-1
console.log(result.info.reason); // Explanation of the score

// Example output:
// {
//   score: 0.67,
//   info: {
//     reason: "The score is 0.67 because two out of three statements from the context
//           (founding year and founders) were contradicted by the output, while the
//           location statement was not contradicted."
//   }
// }
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "LanguageModel",
      description: "幻覚を評価するために使用されるモデルの設定",
      isOptional: false,
    },
    {
      name: "options",
      type: "HallucinationMetricOptions",
      description: "メトリックの設定オプション",
      isOptional: false,
    },
  ]}
/>

### HallucinationMetricOptions

<PropertiesTable
  content={[
    {
      name: "scale",
      type: "number",
      description: "スコアの最大値",
      isOptional: true,
      defaultValue: "1",
    },
    {
      name: "context",
      type: "string[]",
      description: "真実の情報源として使用されるコンテキスト要素の配列",
      isOptional: false,
    },
  ]}
/>

## measure() のパラメータ

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "元のクエリまたはプロンプト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "評価対象となるLLMの応答",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "ハルシネーションスコア（0からスケール、デフォルトは0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "スコアの理由を含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description: "スコアの詳細な説明と特定された矛盾点",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、矛盾検出と根拠のない主張の分析を通じてハルシネーションを評価します。

### スコアリングプロセス

1. 事実内容の分析:

   - コンテキストから文を抽出
   - 数値や日付を特定
   - 文同士の関係をマッピング

2. 出力のハルシネーション分析:

   - コンテキストの文と比較
   - 直接的な矛盾をハルシネーションとしてマーク
   - 根拠のない主張をハルシネーションとして特定
   - 数値の正確性を評価
   - 概算のコンテキストを考慮

3. ハルシネーションスコアの計算:
   - ハルシネーションと判定された文（矛盾および根拠のない主張）の数をカウント
   - 総文数で割る
   - 設定された範囲にスケーリング

最終スコア: `(hallucinated_statements / total_statements) * scale`

### 重要な考慮事項

- コンテキストに存在しない主張はハルシネーションとして扱われます
- 主観的な主張は、明確な根拠がない限りハルシネーションとみなされます
- コンテキスト内の事実についての推測的な言い回し（「かもしれない」「おそらく」など）は許容されます
- コンテキスト外の事実についての推測的な言い回しはハルシネーションとみなされます
- 出力が空の場合、ハルシネーションはゼロとなります
- 数値の評価では以下を考慮します:
  - スケールに応じた精度
  - コンテキスト上の概算
  - 明示的な精度の指標

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0: 完全なハルシネーション - すべてのコンテキスト文と矛盾
- 0.75: 高度なハルシネーション - 75%のコンテキスト文と矛盾
- 0.5: 中程度のハルシネーション - 半数のコンテキスト文と矛盾
- 0.25: 低度のハルシネーション - 25%のコンテキスト文と矛盾
- 0.0: ハルシネーションなし - すべてのコンテキスト文と整合

**注:** このスコアはハルシネーションの度合いを示します。スコアが低いほど、提供されたコンテキストとの事実整合性が高いことを意味します。

## 分析付きの例

```typescript
import { openai } from "@ai-sdk/openai";
import { HallucinationMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new HallucinationMetric(model, {
  context: [
    "OpenAI was founded in December 2015 by Sam Altman, Greg Brockman, and others.",
    "The company launched with a $1 billion investment commitment.",
    "Elon Musk was an early supporter but left the board in 2018.",
  ],
});

const result = await metric.measure({
  input: "What are the key details about OpenAI?",
  output:
    "OpenAI was founded in 2015 by Elon Musk and Sam Altman with a $2 billion investment.",
});

// Example output:
// {
//   score: 0.33,
//   info: {
//     reason: "The score is 0.33 because one out of three statements from the context
//           was contradicted (the investment amount was stated as $2 billion instead
//           of $1 billion). The founding date was correct, and while the output's
//           description of founders was incomplete, it wasn't strictly contradictory."
//   }
// }
```

## 関連

- [Faithfulness Metric](./faithfulness)
- [Answer Relevancy Metric](./answer-relevancy)
- [Context Precision Metric](./context-precision)
- [Context Relevancy Metric](./context-relevancy)
