---
title: "リファレンス: 完全性 | メトリクス | Evals | Mastra ドキュメント"
description: Mastraにおける完全性メトリクスのドキュメント。これは、LLMの出力が入力に含まれる主要な要素をどれだけ網羅しているかを評価します。
---

# CompletenessMetric

`CompletenessMetric` クラスは、LLM の出力が入力に含まれる主要な要素をどれだけ網羅しているかを評価します。名詞、動詞、トピック、用語を分析し、カバレッジを判定して詳細な網羅度スコアを提供します。

## 基本的な使い方

```typescript
import { CompletenessMetric } from "@mastra/evals/nlp";

const metric = new CompletenessMetric();

const result = await metric.measure(
  "Explain how photosynthesis works in plants using sunlight, water, and carbon dioxide.",
  "Plants use sunlight to convert water and carbon dioxide into glucose through photosynthesis.",
);

console.log(result.score); // Coverage score from 0-1
console.log(result.info); // Object containing detailed metrics about element coverage
```

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "カバーすべき主要な要素を含む元のテキスト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "完全性を評価するためのLLMの応答",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description:
        "出力でカバーされている入力要素の割合を表す完全性スコア（0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "要素カバレッジに関する詳細な指標を含むオブジェクト",
      properties: [
        {
          type: "string[]",
          parameters: [
            {
              name: "inputElements",
              type: "string[]",
              description: "入力から抽出された主要要素の配列",
            },
          ],
        },
        {
          type: "string[]",
          parameters: [
            {
              name: "outputElements",
              type: "string[]",
              description: "出力で見つかった主要要素の配列",
            },
          ],
        },
        {
          type: "string[]",
          parameters: [
            {
              name: "missingElements",
              type: "string[]",
              description: "出力で見つからなかった入力要素の配列",
            },
          ],
        },
        {
          type: "object",
          parameters: [
            {
              name: "elementCounts",
              type: "object",
              description: "入力および出力内の要素数",
            },
          ],
        },
      ],
    },
  ]}
/>

## 要素抽出の詳細

このメトリックは、いくつかの種類の要素を抽出し分析します：

- 名詞：主要なオブジェクト、概念、エンティティ
- 動詞：動作や状態（不定詞形に変換）
- トピック：主な主題やテーマ
- 用語：個々の重要な単語

抽出プロセスには以下が含まれます：

- テキストの正規化（ダイアクリティクスの除去、小文字化）
- camelCase単語の分割
- 単語境界の処理
- 短い単語（3文字以下）の特別な処理
- 要素の重複排除

## スコアリングの詳細

このメトリックは、言語要素のカバレッジ分析を通じて完全性を評価します。

### スコアリングプロセス

1. 主要な要素を抽出します：

   - 名詞および固有表現
   - 動作動詞
   - トピック固有の用語
   - 正規化された語形

2. 入力要素のカバレッジを計算します：
   - 短い用語（3文字以下）は完全一致
   - 長い用語は大部分の重複（60%以上）

最終スコア：`(covered_elements / total_input_elements) * scale`

### スコアの解釈

（0からscale、デフォルトは0-1）

- 1.0：完全なカバレッジ - すべての入力要素を含む
- 0.7-0.9：高いカバレッジ - 主要な要素のほとんどを含む
- 0.4-0.6：部分的なカバレッジ - 一部の主要な要素を含む
- 0.1-0.3：低いカバレッジ - ほとんどの主要な要素が欠落
- 0.0：カバレッジなし - 出力に入力要素が全く含まれていない

## 分析付きの例

```typescript
import { CompletenessMetric } from "@mastra/evals/nlp";

const metric = new CompletenessMetric();

const result = await metric.measure(
  "The quick brown fox jumps over the lazy dog",
  "A brown fox jumped over a dog",
);

// Example output:
// {
//   score: 0.75,
//   info: {
//     inputElements: ["quick", "brown", "fox", "jump", "lazy", "dog"],
//     outputElements: ["brown", "fox", "jump", "dog"],
//     missingElements: ["quick", "lazy"],
//     elementCounts: { input: 6, output: 4 }
//   }
// }
```

## 関連

- [Answer Relevancy Metric](./answer-relevancy)
- [Content Similarity Metric](./content-similarity)
- [Textual Difference Metric](./textual-difference)
- [Keyword Coverage Metric](./keyword-coverage)
