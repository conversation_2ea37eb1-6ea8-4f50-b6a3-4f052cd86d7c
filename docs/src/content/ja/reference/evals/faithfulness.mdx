---
title: "リファレンス: Faithfulness | メトリクス | Evals | Mastra ドキュメント"
description: Mastra における Faithfulness メトリクスのドキュメント。提供されたコンテキストと比較して、LLM の出力の事実的な正確性を評価します。
---

# FaithfulnessMetric リファレンス

Mastra の `FaithfulnessMetric` は、LLM の出力が提供されたコンテキストと比較してどれだけ事実に忠実であるかを評価します。出力から主張を抽出し、それらをコンテキストと照合することで、RAG パイプラインの応答の信頼性を測定する上で重要な役割を果たします。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { FaithfulnessMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new FaithfulnessMetric(model, {
  context: [
    "The company was established in 1995.",
    "Currently employs around 450-550 people.",
  ],
});

const result = await metric.measure(
  "Tell me about the company.",
  "The company was founded in 1995 and has 500 employees.",
);

console.log(result.score); // 1.0
console.log(result.info.reason); // "All claims are supported by the context."
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "LanguageModel",
      description: "忠実性を評価するために使用されるモデルの設定。",
      isOptional: false,
    },
    {
      name: "options",
      type: "FaithfulnessMetricOptions",
      description: "メトリックを設定するための追加オプション。",
      isOptional: false,
    },
  ]}
/>

### FaithfulnessMetricOptions

<PropertiesTable
  content={[
    {
      name: "scale",
      type: "number",
      description:
        "最大スコア値。最終的なスコアはこのスケールに正規化されます。",
      isOptional: false,
      defaultValue: "1",
    },
    {
      name: "context",
      type: "string[]",
      description: "出力の主張が検証されるコンテキストチャンクの配列。",
      isOptional: false,
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "LLM に与えられた元のクエリまたはプロンプト。",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "忠実性を評価するための LLM の応答。",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description:
        "0から設定されたスケールまでのスコアで、文脈によって裏付けられた主張の割合を表します。",
    },
    {
      name: "info",
      type: "object",
      description: "スコアの理由を含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description:
                "どの主張が裏付けられたか、矛盾していたか、または不明とされたかを含む、スコアの詳細な説明です。",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、提供されたコンテキストに対するクレーム検証を通じて忠実性を評価します。

### スコアリングプロセス

1. クレームとコンテキストを分析します:

   - すべてのクレーム（事実および推測）を抽出
   - 各クレームをコンテキストと照合して検証
   - 3つの判定のいずれかを割り当てる:
     - "yes" - クレームがコンテキストによって支持されている
     - "no" - クレームがコンテキストと矛盾している
     - "unsure" - クレームが検証できない

2. 忠実性スコアを計算:
   - 支持されたクレームの数をカウント
   - 総クレーム数で割る
   - 設定された範囲にスケーリング

最終スコア: `(supported_claims / total_claims) * scale`

### スコアの解釈

(0 から scale、デフォルトは 0-1)

- 1.0: すべてのクレームがコンテキストによって支持されている
- 0.7-0.9: ほとんどのクレームが支持されており、検証できないものは少数
- 0.4-0.6: 支持と矛盾が混在
- 0.1-0.3: 支持は限定的で、多くが矛盾
- 0.0: 支持されたクレームがない

## 応用例

```typescript
import { openai } from "@ai-sdk/openai";
import { FaithfulnessMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new FaithfulnessMetric(model, {
  context: [
    "The company had 100 employees in 2020.",
    "Current employee count is approximately 500.",
  ],
});

// Example with mixed claim types
const result = await metric.measure(
  "What's the company's growth like?",
  "The company has grown from 100 employees in 2020 to 500 now, and might expand to 1000 by next year.",
);

// Example output:
// {
//   score: 0.67,
//   info: {
//     reason: "The score is 0.67 because two claims are supported by the context
//           (initial employee count of 100 in 2020 and current count of 500),
//           while the future expansion claim is marked as unsure as it cannot
//           be verified against the context."
//   }
// }
```

### 関連項目

- [Answer Relevancy Metric](./answer-relevancy)
- [Hallucination Metric](./hallucination)
- [Context Relevancy Metric](./context-relevancy)
