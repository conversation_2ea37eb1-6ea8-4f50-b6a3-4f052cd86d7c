---
title: "リファレンス: コンテクスチュアルリコール | メトリクス | Evals | Mastra ドキュメント"
description: コンテクスチュアルリコールメトリクスのドキュメント。関連するコンテキストを取り入れたLLMの応答の完全性を評価します。
---

# ContextualRecallMetric

`ContextualRecallMetric` クラスは、LLM の応答が提供されたコンテキストからどれだけ効果的に関連情報を取り入れているかを評価します。このクラスは、参照ドキュメントの重要な情報が応答にきちんと含まれているかどうかを測定し、精度よりも網羅性に重点を置いています。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { ContextualRecallMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new ContextualRecallMetric(model, {
  context: [
    "Product features: cloud synchronization capability",
    "Offline mode available for all users",
    "Supports multiple devices simultaneously",
    "End-to-end encryption for all data",
  ],
});

const result = await metric.measure(
  "What are the key features of the product?",
  "The product includes cloud sync, offline mode, and multi-device support.",
);

console.log(result.score); // Score from 0-1
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "LanguageModel",
      description: "コンテキストリコールを評価するために使用されるモデルの設定",
      isOptional: false,
    },
    {
      name: "options",
      type: "ContextualRecallMetricOptions",
      description: "メトリックの設定オプション",
      isOptional: false,
    },
  ]}
/>

### ContextualRecallMetricOptions

<PropertiesTable
  content={[
    {
      name: "scale",
      type: "number",
      description: "最大スコア値",
      isOptional: true,
      defaultValue: "1",
    },
    {
      name: "context",
      type: "string[]",
      description: "照合対象となる参照ドキュメントや情報の配列",
      isOptional: false,
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "元のクエリまたはプロンプト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "評価対象となるLLMの応答",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "リコールスコア（0からスケール、デフォルトは0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "スコアの理由を含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description: "スコアの詳細な説明",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、応答内容と関連するコンテキスト項目を比較することでリコールを評価します。

### スコアリングプロセス

1. 情報のリコールを評価します：

   - コンテキスト内の関連項目を特定
   - 正しくリコールされた情報を追跡
   - リコールの完全性を測定

2. リコールスコアを計算：
   - 正しくリコールされた項目をカウント
   - 総関連項目数と比較
   - カバレッジ比率を算出

最終スコア：`(correctly_recalled_items / total_relevant_items) * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0: 完全なリコール - すべての関連情報が含まれている
- 0.7-0.9: 高いリコール - ほとんどの関連情報が含まれている
- 0.4-0.6: 中程度のリコール - 一部の関連情報が抜けている
- 0.1-0.3: 低いリコール - 重要な情報が多く抜けている
- 0.0: リコールなし - 関連情報がまったく含まれていない

## カスタム設定の例

```typescript
import { openai } from "@ai-sdk/openai";
import { ContextualRecallMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new ContextualRecallMetric(model, {
  scale: 100, // Use 0-100 scale instead of 0-1
  context: [
    "All data is encrypted at rest and in transit",
    "Two-factor authentication (2FA) is mandatory",
    "Regular security audits are performed",
    "Incident response team available 24/7",
  ],
});

const result = await metric.measure(
  "Summarize the company's security measures",
  "The company implements encryption for data protection and requires 2FA for all users.",
);

// Example output:
// {
//   score: 50, // Only half of the security measures were mentioned
//   info: {
//     reason: "The score is 50 because only half of the security measures were mentioned
//           in the response. The response missed the regular security audits and incident
//           response team information."
//   }
// }
```

## 関連

- [コンテキスト関連性メトリクス](./context-relevancy)
- [完全性メトリクス](./completeness)
- [要約メトリクス](./summarization)
