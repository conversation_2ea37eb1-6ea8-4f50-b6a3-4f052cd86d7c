---
title: "リファレンス: テキスト差分 | Evals | Mastra ドキュメント"
description: Mastra におけるテキスト差分メトリクスのドキュメント。これはシーケンスマッチングを用いて文字列間のテキスト差分を測定します。
---

# TextualDifferenceMetric

`TextualDifferenceMetric` クラスは、シーケンスマッチングを使用して2つの文字列間のテキスト差分を測定します。このクラスは、一方のテキストをもう一方に変換するために必要な操作数を含む、変更に関する詳細な情報を提供します。

## 基本的な使い方

```typescript
import { TextualDifferenceMetric } from "@mastra/evals/nlp";

const metric = new TextualDifferenceMetric();

const result = await metric.measure(
  "The quick brown fox",
  "The fast brown fox",
);

console.log(result.score); // 0〜1の類似度比率
console.log(result.info); // 詳細な変更メトリクス
```

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "比較対象となる元のテキスト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "差分を評価するテキスト",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description:
        "類似度比率（0～1）。1はテキストが完全に一致していることを示します",
    },
    {
      name: "info",
      description: "差分に関する詳細な指標",
      properties: [
        {
          type: "number",
          parameters: [
            {
              name: "confidence",
              type: "number",
              description: "テキスト間の長さの違いに基づく信頼度スコア（0～1）",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "ratio",
              type: "number",
              description: "テキスト間の生の類似度比率",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "changes",
              type: "number",
              description: "変更操作の回数（挿入、削除、置換）",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "lengthDiff",
              type: "number",
              description: "入力と出力の長さの正規化された差分（0～1）",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、いくつかの指標を計算します：

- **類似度比**：テキスト間のシーケンスマッチングに基づく（0-1）
- **変更数**：一致しない操作の回数
- **長さの差**：テキスト長の正規化された差
- **信頼度**：長さの差に反比例

### スコアリングプロセス

1. テキストの違いを分析します：

   - 入力と出力の間でシーケンスマッチングを実行
   - 必要な変更操作の数をカウント
   - 長さの違いを測定

2. 指標を計算します：
   - 類似度比を算出
   - 信頼度スコアを決定
   - 重み付けされたスコアに統合

最終スコア：`(similarity_ratio * confidence) * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0：完全に同一のテキスト - 違いなし
- 0.7-0.9：わずかな違い - 少しの変更のみ必要
- 0.4-0.6：中程度の違い - かなりの変更が必要
- 0.1-0.3：大きな違い - 大幅な変更が必要
- 0.0：全く異なるテキスト

## 分析付きの例

```typescript
import { TextualDifferenceMetric } from "@mastra/evals/nlp";

const metric = new TextualDifferenceMetric();

const result = await metric.measure(
  "Hello world! How are you?",
  "Hello there! How is it going?",
);

// Example output:
// {
//   score: 0.65,
//   info: {
//     confidence: 0.95,
//     ratio: 0.65,
//     changes: 2,
//     lengthDiff: 0.05
//   }
// }
```

## 関連

- [コンテンツ類似度メトリック](./content-similarity)
- [完全性メトリック](./completeness)
- [キーワードカバレッジメトリック](./keyword-coverage)
