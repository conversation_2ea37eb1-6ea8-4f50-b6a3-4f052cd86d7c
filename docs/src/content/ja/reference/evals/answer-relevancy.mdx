---
title: "リファレンス: 回答の関連性 | メトリクス | 評価 | Mastra ドキュメント"
description: Mastra における回答の関連性メトリクスのドキュメント。LLM の出力が入力クエリにどれだけ適切に対応しているかを評価します。
---

# AnswerRelevancyMetric

`AnswerRelevancyMetric` クラスは、LLM の出力が入力クエリにどれだけ適切に回答または対応しているかを評価します。判定者ベースのシステムを用いて関連性を判断し、詳細なスコアリングと理由付けを提供します。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { AnswerRelevancyMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new AnswerRelevancyMetric(model, {
  uncertaintyWeight: 0.3,
  scale: 1,
});

const result = await metric.measure(
  "What is the capital of France?",
  "Paris is the capital of France.",
);

console.log(result.score); // Score from 0-1
console.log(result.info.reason); // Explanation of the score
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "LanguageModel",
      description: "関連性を評価するために使用されるモデルの設定",
      isOptional: false,
    },
    {
      name: "options",
      type: "AnswerRelevancyMetricOptions",
      description: "このメトリクスの設定オプション",
      isOptional: true,
      defaultValue: "{ uncertaintyWeight: 0.3, scale: 1 }",
    },
  ]}
/>

### AnswerRelevancyMetricOptions

<PropertiesTable
  content={[
    {
      name: "uncertaintyWeight",
      type: "number",
      description: "スコアリングにおいて「不確か」な判定に与える重み（0-1）",
      isOptional: true,
      defaultValue: "0.3",
    },
    {
      name: "scale",
      type: "number",
      description: "スコアの最大値",
      isOptional: true,
      defaultValue: "1",
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "元のクエリまたはプロンプト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "評価対象となるLLMの応答",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "関連性スコア（0からスケール、デフォルトは0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "スコアの理由を含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description: "スコアの説明",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

この指標は、クエリと回答の整合性を通じて関連性を評価し、完全性、正確性、詳細レベルを考慮します。

### スコアリングプロセス

1. ステートメント分析:

   - 出力を意味のあるステートメントに分割し、文脈を保持する
   - 各ステートメントをクエリの要件と照らし合わせて評価する

2. 各ステートメントの関連性を評価:
   - "yes": 直接一致の場合は全加重
   - "unsure": おおよその一致には部分加重（デフォルト: 0.3）
   - "no": 無関係な内容には加重なし

最終スコア: `((direct + uncertainty * partial) / total_statements) * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0: 完全な関連性 - 完全かつ正確
- 0.7-0.9: 高い関連性 - わずかな抜けや不正確さ
- 0.4-0.6: 中程度の関連性 - 重要な抜け
- 0.1-0.3: 低い関連性 - 重大な問題
- 0.0: 関連性なし - 誤りまたは的外れ

## カスタム設定の例

```typescript
import { openai } from "@ai-sdk/openai";
import { AnswerRelevancyMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new AnswerRelevancyMetric(model, {
  uncertaintyWeight: 0.5, // Higher weight for uncertain verdicts
  scale: 5, // Use 0-5 scale instead of 0-1
});

const result = await metric.measure(
  "What are the benefits of exercise?",
  "Regular exercise improves cardiovascular health, builds strength, and boosts mental wellbeing.",
);

// Example output:
// {
//   score: 4.5,
//   info: {
//     reason: "The score is 4.5 out of 5 because the response directly addresses the query
//           with specific, accurate benefits of exercise. It covers multiple aspects
//           (cardiovascular, muscular, and mental health) in a clear and concise manner.
//           The answer is highly relevant and provides appropriate detail without
//           including unnecessary information."
//   }
// }
```

## 関連

- [プロンプト整合性メトリクス](./prompt-alignment)
- [コンテキスト精度メトリクス](./context-precision)
- [忠実性メトリクス](./faithfulness)
