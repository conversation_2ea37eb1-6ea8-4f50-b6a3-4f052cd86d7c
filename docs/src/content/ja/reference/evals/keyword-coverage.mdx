---
title: "リファレンス: キーワードカバレッジ | メトリクス | Evals | Mastra ドキュメント"
description: Mastra におけるキーワードカバレッジメトリクスのドキュメント。入力から重要なキーワードが LLM の出力でどれだけカバーされているかを評価します。
---

# KeywordCoverageMetric

`KeywordCoverageMetric` クラスは、LLM の出力が入力の重要なキーワードをどれだけ網羅しているかを評価します。一般的な単語やストップワードを無視しながら、キーワードの存在と一致を分析します。

## 基本的な使い方

```typescript
import { KeywordCoverageMetric } from "@mastra/evals/nlp";

const metric = new KeywordCoverageMetric();

const result = await metric.measure(
  "What are the key features of Python programming language?",
  "Python is a high-level programming language known for its simple syntax and extensive libraries.",
);

console.log(result.score); // Coverage score from 0-1
console.log(result.info); // Object containing detailed metrics about keyword coverage
```

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "一致させるキーワードを含む元のテキスト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "キーワードカバレッジを評価するテキスト",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "一致したキーワードの割合を表すカバレッジスコア（0～1）",
    },
    {
      name: "info",
      type: "object",
      description: "キーワードカバレッジに関する詳細な指標を含むオブジェクト",
      properties: [
        {
          type: "number",
          parameters: [
            {
              name: "matchedKeywords",
              type: "number",
              description: "出力内で見つかったキーワードの数",
            },
          ],
        },
        {
          type: "number",
          parameters: [
            {
              name: "totalKeywords",
              type: "number",
              description: "入力からのキーワードの総数",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

この指標は、以下の特徴を持つキーワードとのマッチングによってキーワードカバレッジを評価します。

- 一般的な単語やストップワードのフィルタリング（例：「the」「a」「and」など）
- 大文字・小文字を区別しないマッチング
- 単語形のバリエーションへの対応
- 技術用語や複合語の特別な処理

### スコアリングプロセス

1. 入力と出力からキーワードを処理します：

   - 一般的な単語やストップワードを除外
   - 大文字・小文字や単語形を正規化
   - 特別な用語や複合語を処理

2. キーワードカバレッジを計算します：
   - テキスト間でキーワードをマッチング
   - 成功したマッチ数をカウント
   - カバレッジ比率を算出

最終スコア：`(matched_keywords / total_keywords) * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0: 完全なキーワードカバレッジ
- 0.7-0.9: ほとんどのキーワードが含まれている良好なカバレッジ
- 0.4-0.6: 一部のキーワードが欠落している中程度のカバレッジ
- 0.1-0.3: 多くのキーワードが欠落している低いカバレッジ
- 0.0: キーワードの一致なし

## 例と分析

```typescript
import { KeywordCoverageMetric } from "@mastra/evals/nlp";

const metric = new KeywordCoverageMetric();

// Perfect coverage example
const result1 = await metric.measure(
  "The quick brown fox jumps over the lazy dog",
  "A quick brown fox jumped over a lazy dog",
);
// {
//   score: 1.0,
//   info: {
//     matchedKeywords: 6,
//     totalKeywords: 6
//   }
// }

// Partial coverage example
const result2 = await metric.measure(
  "Python features include easy syntax, dynamic typing, and extensive libraries",
  "Python has simple syntax and many libraries",
);
// {
//   score: 0.67,
//   info: {
//     matchedKeywords: 4,
//     totalKeywords: 6
//   }
// }

// Technical terms example
const result3 = await metric.measure(
  "Discuss React.js component lifecycle and state management",
  "React components have lifecycle methods and manage state",
);
// {
//   score: 1.0,
//   info: {
//     matchedKeywords: 4,
//     totalKeywords: 4
//   }
// }
```

## 特殊なケース

このメトリクスはいくつかの特殊なケースに対応しています：

- 入力／出力が空の場合：両方が空ならスコアは1.0、どちらか一方のみ空なら0.0を返します
- 単語が1つの場合：1つのキーワードとして扱います
- 技術用語：複合的な技術用語（例: "React.js", "machine learning"）を保持します
- 大文字・小文字の違い："JavaScript"は"javascript"と一致します
- 一般的な単語：意味のあるキーワードに集中するため、スコア計算時に無視されます

## 関連

- [Completeness Metric](./completeness)
- [Content Similarity Metric](./content-similarity)
- [Answer Relevancy Metric](./answer-relevancy)
- [Textual Difference Metric](./textual-difference)
- [Context Relevancy Metric](./context-relevancy)
