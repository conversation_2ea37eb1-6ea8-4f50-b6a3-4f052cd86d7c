---
title: "リファレンス: トーンの一貫性 | メトリクス | 評価 | Mastra ドキュメント"
description: Mastra におけるトーンの一貫性メトリクスのドキュメント。テキスト内の感情的なトーンや感情の一貫性を評価します。
---

# ToneConsistencyMetric

`ToneConsistencyMetric` クラスは、テキストの感情的なトーンと感情の一貫性を評価します。このクラスは、入力と出力のペア間でトーンを比較するモードと、単一のテキスト内でトーンの安定性を分析するモードの2つのモードで動作します。

## 基本的な使い方

```typescript
import { ToneConsistencyMetric } from "@mastra/evals/nlp";

const metric = new ToneConsistencyMetric();

// Compare tone between input and output
const result1 = await metric.measure(
  "I love this amazing product!",
  "This product is wonderful and fantastic!",
);

// Analyze tone stability in a single text
const result2 = await metric.measure(
  "The service is excellent. The staff is friendly. The atmosphere is perfect.",
  "", // Empty string for single-text analysis
);

console.log(result1.score); // Tone consistency score from 0-1
console.log(result2.score); // Tone stability score from 0-1
```

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "トーンを分析するためのテキスト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description:
        "トーン比較のための参照テキスト（安定性分析の場合は空文字列）",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "トーンの一貫性／安定性スコア（0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "詳細なトーン情報",
    },
  ]}
/>

### info オブジェクト（トーン比較）

<PropertiesTable
  content={[
    {
      name: "responseSentiment",
      type: "number",
      description: "入力テキストのセンチメントスコア",
    },
    {
      name: "referenceSentiment",
      type: "number",
      description: "出力テキストのセンチメントスコア",
    },
    {
      name: "difference",
      type: "number",
      description: "センチメントスコア間の絶対差",
    },
  ]}
/>

### info オブジェクト（トーン安定性）

<PropertiesTable
  content={[
    {
      name: "avgSentiment",
      type: "number",
      description: "文ごとの平均センチメントスコア",
    },
    {
      name: "sentimentVariance",
      type: "number",
      description: "文間のセンチメントの分散",
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、トーンパターンの分析とモード別スコアリングを通じて感情の一貫性を評価します。

### スコアリングプロセス

1. トーンパターンを分析：

   - 感情の特徴を抽出
   - 感情スコアを算出
   - トーンの変動を測定

2. モード別スコアを計算：
   **トーンの一貫性**（入力と出力）：

   - テキスト間の感情を比較
   - 感情の差分を計算
   - スコア = 1 - (感情の差分 / 最大差分)

   **トーンの安定性**（単一入力）：

   - 文ごとの感情を分析
   - 感情の分散を計算
   - スコア = 1 - (感情の分散 / 最大分散)

最終スコア：`mode_specific_score * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0: 完全なトーンの一貫性／安定性
- 0.7-0.9: 軽微な変動を伴う強い一貫性
- 0.4-0.6: 目立つ変化を伴う中程度の一貫性
- 0.1-0.3: 大きなトーン変化を伴う低い一貫性
- 0.0: 一貫性なし ― 完全に異なるトーン

## 両方のモードの例

```typescript
import { ToneConsistencyMetric } from "@mastra/evals/nlp";

const metric = new ToneConsistencyMetric();

// Tone Consistency Mode
const consistencyResult = await metric.measure(
  "This product is fantastic and amazing!",
  "The product is excellent and wonderful!",
);
// Example output:
// {
//   score: 0.95,
//   info: {
//     responseSentiment: 0.8,
//     referenceSentiment: 0.75,
//     difference: 0.05
//   }
// }

// Tone Stability Mode
const stabilityResult = await metric.measure(
  "Great service! Friendly staff. Perfect atmosphere.",
  "",
);
// Example output:
// {
//   score: 0.9,
//   info: {
//     avgSentiment: 0.6,
//     sentimentVariance: 0.1
//   }
// }
```

## 関連

- [コンテンツ類似度メトリック](./content-similarity)
- [有害性メトリック](./toxicity)
