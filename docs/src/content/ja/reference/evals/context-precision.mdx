---
title: "リファレンス: コンテキスト精度 | メトリクス | Evals | Mastra ドキュメント"
description: Mastra におけるコンテキスト精度メトリクスのドキュメント。期待される出力を生成するために取得されたコンテキストノードの関連性と精度を評価します。
---

# ContextPrecisionMetric

`ContextPrecisionMetric` クラスは、取得されたコンテキストノードが期待される出力を生成するためにどれだけ関連性が高く、正確であるかを評価します。判定者ベースのシステムを用いて各コンテキスト要素の貢献度を分析し、位置に基づいた重み付きスコアを提供します。

## 基本的な使い方

```typescript
import { openai } from "@ai-sdk/openai";
import { ContextPrecisionMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new ContextPrecisionMetric(model, {
  context: [
    "Photosynthesis is a biological process used by plants to create energy from sunlight.",
    "Plants need water and nutrients from the soil to grow.",
    "The process of photosynthesis produces oxygen as a byproduct.",
  ],
});

const result = await metric.measure(
  "What is photosynthesis?",
  "Photosynthesis is the process by which plants convert sunlight into energy.",
);

console.log(result.score); // Precision score from 0-1
console.log(result.info.reason); // Explanation of the score
```

## コンストラクタのパラメータ

<PropertiesTable
  content={[
    {
      name: "model",
      type: "LanguageModel",
      description: "コンテキストの関連性を評価するために使用されるモデルの設定",
      isOptional: false,
    },
    {
      name: "options",
      type: "ContextPrecisionMetricOptions",
      description: "このメトリックの設定オプション",
      isOptional: false,
    },
  ]}
/>

### ContextPrecisionMetricOptions

<PropertiesTable
  content={[
    {
      name: "scale",
      type: "number",
      description: "スコアの最大値",
      isOptional: true,
      defaultValue: "1",
    },
    {
      name: "context",
      type: "string[]",
      description: "取得順に並んだコンテキスト要素の配列",
      isOptional: false,
    },
  ]}
/>

## measure() のパラメーター

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string",
      description: "元のクエリまたはプロンプト",
      isOptional: false,
    },
    {
      name: "output",
      type: "string",
      description: "評価対象となる生成された応答",
      isOptional: false,
    },
  ]}
/>

## 戻り値

<PropertiesTable
  content={[
    {
      name: "score",
      type: "number",
      description: "適合率スコア（0からスケール、デフォルトは0-1）",
    },
    {
      name: "info",
      type: "object",
      description: "スコアの理由を含むオブジェクト",
      properties: [
        {
          type: "string",
          parameters: [
            {
              name: "reason",
              type: "string",
              description: "スコアの詳細な説明",
            },
          ],
        },
      ],
    },
  ]}
/>

## スコアリングの詳細

このメトリックは、バイナリ関連性評価と平均適合率（MAP）スコアリングを通じてコンテキストの精度を評価します。

### スコアリングプロセス

1. バイナリ関連性スコアを割り当てます：

   - 関連するコンテキスト：1
   - 関連しないコンテキスト：0

2. 平均適合率を計算します：
   - 各位置での適合率を算出
   - 先頭の位置により大きな重みを付与
   - 設定されたスケールに正規化

最終スコア：`Mean Average Precision * scale`

### スコアの解釈

（0 から scale、デフォルトは 0-1）

- 1.0：すべての関連コンテキストが最適な順序で並んでいる
- 0.7-0.9：ほとんどが関連するコンテキストで順序も良好
- 0.4-0.6：関連性が混在、または順序が最適でない
- 0.1-0.3：関連性が限定的、または順序が悪い
- 0.0：関連するコンテキストがない

## 分析付きの例

```typescript
import { openai } from "@ai-sdk/openai";
import { ContextPrecisionMetric } from "@mastra/evals/llm";

// Configure the model for evaluation
const model = openai("gpt-4o-mini");

const metric = new ContextPrecisionMetric(model, {
  context: [
    "Exercise strengthens the heart and improves blood circulation.",
    "A balanced diet is important for health.",
    "Regular physical activity reduces stress and anxiety.",
    "Exercise equipment can be expensive.",
  ],
});

const result = await metric.measure(
  "What are the benefits of exercise?",
  "Regular exercise improves cardiovascular health and mental wellbeing.",
);

// Example output:
// {
//   score: 0.75,
//   info: {
//     reason: "The score is 0.75 because the first and third contexts are highly relevant
//           to the benefits mentioned in the output, while the second and fourth contexts
//           are not directly related to exercise benefits. The relevant contexts are well-positioned
//           at the beginning and middle of the sequence."
//   }
// }
```

## 関連

- [Answer Relevancy Metric](./answer-relevancy)
- [Context Position Metric](./context-position)
- [Completeness Metric](./completeness)
- [Context Relevancy Metric](./context-relevancy)
