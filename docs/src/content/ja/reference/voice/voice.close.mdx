---
title: "リファレンス: voice.close() | Voice Providers | Mastra ドキュメント"
description: "voice プロバイダーで利用可能な close() メソッドのドキュメント。リアルタイム音声サービスから切断します。"
---

# voice.close()

`close()` メソッドは、リアルタイム音声サービスから切断し、リソースをクリーンアップします。これは、音声セッションを正しく終了し、リソースリークを防ぐために重要です。

## 使用例

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { getMicrophoneStream } from "@mastra/node-audio";

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o-mini-realtime",
    apiKey: process.env.OPENAI_API_KEY,
  },
});

// Connect to the real-time service
await voice.connect();

// Start a conversation
voice.speak("Hello, I'm your AI assistant!");

// Stream audio from a microphone
const microphoneStream = getMicrophoneStream();
voice.send(microphoneStream);

// When the conversation is complete
setTimeout(() => {
  // Close the connection and clean up resources
  voice.close();
  console.log("Voice session ended");
}, 60000); // End after 1 minute
```

## パラメーター

このメソッドはパラメーターを受け取りません。

## 戻り値

このメソッドは値を返しません。

## 注意事項

- リアルタイム音声セッションが終了したら、必ず `close()` を呼び出してリソースを解放してください
- `close()` を呼び出した後、新しいセッションを開始したい場合は再度 `connect()` を呼び出す必要があります
- このメソッドは、主に持続的な接続を維持するリアルタイム音声プロバイダーと一緒に使用されます
- リアルタイム接続をサポートしていない音声プロバイダーで呼び出した場合、警告が記録され、何も行われません
- 接続を閉じ忘れると、リソースリークや音声サービスプロバイダーでの課金問題につながる可能性があります
