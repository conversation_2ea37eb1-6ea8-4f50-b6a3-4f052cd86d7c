---
title: "リファレンス: voice.speak() | 音声プロバイダー | Mastra ドキュメント"
description: "すべてのMastra音声プロバイダーで利用可能なspeak()メソッドのドキュメント。テキストを音声に変換します。"
---

# voice.speak()

`speak()` メソッドは、すべてのMastra音声プロバイダーで利用可能な中核機能で、テキストを音声に変換します。テキスト入力を受け取り、再生または保存できるオーディオストリームを返します。

## 使用例

```typescript
import { OpenAIVoice } from "@mastra/voice-openai";
// Initialize a voice provider
const voice = new OpenAIVoice({
  speaker: "alloy", // Default voice
});
// Basic usage with default settings
const audioStream = await voice.speak("Hello, world!");
// Using a different voice for this specific request
const audioStreamWithDifferentVoice = await voice.speak("Hello again!", {
  speaker: "nova",
});
// Using provider-specific options
const audioStreamWithOptions = await voice.speak("Hello with options!", {
  speaker: "echo",
  speed: 1.2, // OpenAI-specific option
});
// Using a text stream as input
import { Readable } from "stream";
const textStream = Readable.from(["Hello", " from", " a", " stream!"]);
const audioStreamFromTextStream = await voice.speak(textStream);
```

## パラメータ

<PropertiesTable
  content={[
    {
      name: "input",
      type: "string | NodeJS.ReadableStream",
      description:
        "音声に変換するテキスト。文字列またはテキストの読み取り可能なストリームです。",
      isOptional: false,
    },
    {
      name: "options",
      type: "object",
      description: "音声合成のオプション",
      isOptional: true,
    },
    {
      name: "options.speaker",
      type: "string",
      description:
        "この特定のリクエストに使用する音声ID。コンストラクタで設定されたデフォルトのスピーカーを上書きします。",
      isOptional: true,
    },
  ]}
/>

## 戻り値

`Promise<NodeJS.ReadableStream | void>` を返します。内容は以下の通りです：

- `NodeJS.ReadableStream`: 再生または保存が可能な音声データのストリーム
- `void`: 音声を直接返すのではなく、イベントを通じてリアルタイムで音声を出力するプロバイダーを使用する場合

## プロバイダー固有のオプション

各音声プロバイダーは、それぞれの実装に特有の追加オプションをサポートしている場合があります。以下にいくつかの例を示します。

### OpenAI

<PropertiesTable
  content={[
    {
      name: "options.speed",
      type: "number",
      description:
        "音声の速度倍率。0.25から4.0までの値がサポートされています。",
      isOptional: true,
      defaultValue: "1.0",
    },
  ]}
/>

### ElevenLabs

<PropertiesTable
  content={[
    {
      name: "options.stability",
      type: "number",
      description:
        "音声の安定性。値が高いほど、より安定し、抑揚の少ない音声になります。",
      isOptional: true,
      defaultValue: "0.5",
    },
    {
      name: "options.similarity_boost",
      type: "number",
      description: "音声の明瞭さと元の声への類似度。",
      isOptional: true,
      defaultValue: "0.75",
    },
  ]}
/>

### Google

<PropertiesTable
  content={[
    {
      name: "options.languageCode",
      type: "string",
      description: "音声の言語コード（例: 'en-US'）。",
      isOptional: true,
    },
    {
      name: "options.audioConfig",
      type: "object",
      description: "Google Cloud Text-to-Speech API の音声設定オプション。",
      isOptional: true,
      defaultValue: "{ audioEncoding: 'LINEAR16' }",
    },
  ]}
/>

### Murf

<PropertiesTable
  content={[
    {
      name: "options.properties.rate",
      type: "number",
      description: "音声の速度倍率。",
      isOptional: true,
    },
    {
      name: "options.properties.pitch",
      type: "number",
      description: "音声のピッチ調整。",
      isOptional: true,
    },
    {
      name: "options.properties.format",
      type: "'MP3' | 'WAV' | 'FLAC' | 'ALAW' | 'ULAW'",
      description: "出力音声のフォーマット。",
      isOptional: true,
    },
  ]}
/>

## リアルタイム音声プロバイダー

`OpenAIRealtimeVoice`のようなリアルタイム音声プロバイダーを使用する場合、`speak()`メソッドは異なる動作をします：

- オーディオストリームを返す代わりに、オーディオデータを含む「speaking」イベントを発行します
- オーディオチャンクを受信するためにイベントリスナーを登録する必要があります

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import Speaker from "@mastra/node-speaker";

const speaker = new Speaker({
  sampleRate: 24100, // オーディオサンプルレート（Hz）- MacBook Proの高品質オーディオの標準
  channels: 1, // モノラルオーディオ出力（ステレオの場合は2）
  bitDepth: 16, // オーディオ品質のビット深度 - CD品質標準（16ビット解像度）
});

const voice = new OpenAIRealtimeVoice();
await voice.connect();
// オーディオチャンク用のイベントリスナーを登録
voice.on("speaker", (stream) => {
  // オーディオチャンクを処理（例：再生または保存）
  stream.pipe(speaker);
});
// これはストリームを返す代わりに「speaking」イベントを発行します
await voice.speak("Hello, this is realtime speech!");
```

## CompositeVoiceでの使用

`CompositeVoice`を使用する場合、`speak()`メソッドは設定された音声提供プロバイダーに処理を委任します：

```typescript
import { CompositeVoice } from "@mastra/core/voice";
import { OpenAIVoice } from "@mastra/voice-openai";
import { PlayAIVoice } from "@mastra/voice-playai";
const voice = new CompositeVoice({
  speakProvider: new PlayAIVoice(),
  listenProvider: new OpenAIVoice(),
});
// これはPlayAIVoiceプロバイダーを使用します
const audioStream = await voice.speak("Hello, world!");
```

## 注意事項

- `speak()` の動作はプロバイダーによって若干異なる場合がありますが、すべての実装は同じ基本インターフェースに従います。
- リアルタイム音声プロバイダーを使用する場合、このメソッドは音声ストリームを直接返さず、代わりに「speaking」イベントを発行することがあります。
- 入力としてテキストストリームが提供された場合、プロバイダーは通常それを文字列に変換してから処理します。
- 返されるストリームの音声フォーマットはプロバイダーによって異なります。一般的なフォーマットには MP3、WAV、OGG などがあります。
- 最良のパフォーマンスのために、使用後は音声ストリームを閉じるか終了することを検討してください。
