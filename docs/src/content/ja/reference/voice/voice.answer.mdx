---
title: "リファレンス: voice.answer() | Voice Providers | Mastra ドキュメント"
description: "リアルタイム音声プロバイダーで利用可能な answer() メソッドのドキュメント。このメソッドは音声プロバイダーに応答の生成を指示します。"
---

# voice.answer()

`answer()` メソッドは、リアルタイム音声プロバイダーでAIに応答を生成させるために使用されます。このメソッドは、ユーザー入力を受け取った後にAIに明示的に応答を促す必要がある音声対音声の会話で特に便利です。

## 使用例

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { getMicrophoneStream } from "@mastra/node-audio";
import Speaker from "@mastra/node-speaker";

const speaker = new Speaker({
  sampleRate: 24100, // Audio sample rate in Hz - standard for high-quality audio on MacBook Pro
  channels: 1, // Mono audio output (as opposed to stereo which would be 2)
  bitDepth: 16, // Bit depth for audio quality - CD quality standard (16-bit resolution)
});

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o",
    apiKey: process.env.OPENAI_API_KEY,
  },
  speaker: "alloy", // Default voice
});
// Connect to the real-time service
await voice.connect();
// Register event listener for responses
voice.on("speaker", (stream) => {
  // Handle audio response
  stream.pipe(speaker);
});
// Send user audio input
const microphoneStream = getMicrophoneStream();
await voice.send(microphoneStream);
// Trigger the AI to respond
await voice.answer();
```

## パラメーター

<br />
<PropertiesTable
  content={[
    {
      name: "options",
      type: "Record<string, unknown>",
      description: "レスポンスのプロバイダー固有のオプション",
      isOptional: true,
    },
  ]}
/>

## 戻り値

レスポンスがトリガーされたときに解決される `Promise<void>` を返します。

## 注意事項

- このメソッドは、音声から音声への機能をサポートするリアルタイム音声プロバイダーでのみ実装されています
- この機能をサポートしていない音声プロバイダーで呼び出した場合、警告が記録され、即座に解決されます
- 応答音声は通常、直接返されるのではなく、'speaking' イベントを通じて出力されます
- サポートしているプロバイダーでは、このメソッドを使ってAIが生成するのではなく、特定の応答を送信できます
- このメソッドは、会話の流れを作るために `send()` と組み合わせてよく使われます
