---
title: "リファレンス: voice.connect() | 音声プロバイダー | Mastra Docs"
description: "リアルタイム音声プロバイダーで利用可能なconnect()メソッドのドキュメント。音声対音声通信の接続を確立します。"
---

# voice.connect()

`connect()` メソッドは、リアルタイムの音声対音声通信のためのWebSocketまたはWebRTC接続を確立します。このメソッドは、`send()`や`answer()`などの他のリアルタイム機能を使用する前に呼び出す必要があります。

## 使用例

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import Speaker from "@mastra/node-speaker";

const speaker = new Speaker({
  sampleRate: 24100, // Audio sample rate in Hz - standard for high-quality audio on MacBook Pro
  channels: 1, // Mono audio output (as opposed to stereo which would be 2)
  bitDepth: 16, // Bit depth for audio quality - CD quality standard (16-bit resolution)
});

// Initialize a real-time voice provider
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o-mini-realtime",
    apiKey: process.env.OPENAI_API_KEY,
    options: {
      sessionConfig: {
        turn_detection: {
          type: "server_vad",
          threshold: 0.6,
          silence_duration_ms: 1200,
        },
      },
    },
  },
  speaker: "alloy", // Default voice
});
// Connect to the real-time service
await voice.connect();
// Now you can use real-time features
voice.on("speaker", (stream) => {
  stream.pipe(speaker);
});
// With connection options
await voice.connect({
  timeout: 10000, // 10 seconds timeout
  reconnect: true,
});
```

## パラメータ

<PropertiesTable
  content={[
    {
      name: "options",
      type: "Record<string, unknown>",
      description: "プロバイダー固有の接続オプション",
      isOptional: true,
    },
  ]}
/>

## 戻り値

接続が正常に確立されると解決する`Promise<void>`を返します。

## プロバイダー固有のオプション

各リアルタイム音声プロバイダーは、`connect()`メソッドに対して異なるオプションをサポートしている場合があります：

### OpenAI リアルタイム

<PropertiesTable
  content={[
    {
      name: "options.timeout",
      type: "number",
      description: "接続タイムアウト（ミリ秒）",
      isOptional: true,
      defaultValue: "30000",
    },
    {
      name: "options.reconnect",
      type: "boolean",
      description: "接続が切れた場合に自動的に再接続するかどうか",
      isOptional: true,
      defaultValue: "false",
    },
  ]}
/>

## CompositeVoiceでの使用

`CompositeVoice`を使用する場合、`connect()`メソッドは設定されたリアルタイムプロバイダーに委譲されます：

```typescript
import { CompositeVoice } from "@mastra/core/voice";
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
const realtimeVoice = new OpenAIRealtimeVoice();
const voice = new CompositeVoice({
  realtimeProvider: realtimeVoice,
});
// これはOpenAIRealtimeVoiceプロバイダーを使用します
await voice.connect();
```

## 注意事項

- このメソッドは、音声対音声機能をサポートするリアルタイム音声プロバイダーでのみ実装されています
- この機能をサポートしていない音声プロバイダーで呼び出された場合、警告をログに記録して即座に解決します
- `send()` や `answer()` などの他のリアルタイムメソッドを使用する前に、接続を確立する必要があります
- 音声インスタンスの使用が終わったら、リソースを適切にクリーンアップするために `close()` を呼び出してください
- 一部のプロバイダーは、その実装によって、接続が失われた場合に自動的に再接続することがあります
- 接続エラーは通常、キャッチして処理すべき例外としてスローされます

## 関連メソッド

- [voice.send()](./voice.send) - 音声データを音声プロバイダーに送信します
- [voice.answer()](./voice.answer) - 音声プロバイダーに応答を促します
- [voice.close()](./voice.close) - リアルタイムサービスから切断します
- [voice.on()](./voice.on) - 音声イベントのイベントリスナーを登録します
