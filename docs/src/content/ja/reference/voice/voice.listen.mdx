---
title: "リファレンス: voice.listen() | 音声プロバイダー | Mastra ドキュメント"
description: "すべてのMastra音声プロバイダーで利用可能な、音声をテキストに変換するlisten()メソッドのドキュメント。"
---

# voice.listen()

`listen()` メソッドは、すべてのMastra音声プロバイダーで利用可能な、音声をテキストに変換するコア機能です。音声ストリームを入力として受け取り、文字起こしされたテキストを返します。

## 使用例

```typescript
import { OpenAIVoice } from "@mastra/voice-openai";
import { getMicrophoneStream } from "@mastra/node-audio";
import { createReadStream } from "fs";
import path from "path";

// Initialize a voice provider
const voice = new OpenAIVoice({
  listeningModel: {
    name: "whisper-1",
    apiKey: process.env.OPENAI_API_KEY,
  },
});

// Basic usage with a file stream
const audioFilePath = path.join(process.cwd(), "audio.mp3");
const audioStream = createReadStream(audioFilePath);
const transcript = await voice.listen(audioStream, {
  filetype: "mp3",
});
console.log("Transcribed text:", transcript);

// Using a microphone stream
const microphoneStream = getMicrophoneStream(); // Assume this function gets audio input
const transcription = await voice.listen(microphoneStream);

// With provider-specific options
const transcriptWithOptions = await voice.listen(audioStream, {
  language: "en",
  prompt: "This is a conversation about artificial intelligence.",
});
```

## パラメータ

<PropertiesTable
  content={[
    {
      name: "audioStream",
      type: "NodeJS.ReadableStream",
      description:
        "文字起こしするオーディオストリーム。これはファイルストリームまたはマイクストリームです。",
      isOptional: false,
    },
    {
      name: "options",
      type: "object",
      description: "音声認識のためのプロバイダー固有のオプション",
      isOptional: true,
    },
  ]}
/>

## 戻り値

以下のいずれかを返します：

- `Promise<string>`: 文字起こしされたテキストを解決するプロミス
- `Promise<NodeJS.ReadableStream>`: 文字起こしされたテキストのストリームを解決するプロミス（ストリーミング文字起こし用）
- `Promise<void>`: テキストを直接返す代わりに「writing」イベントを発行するリアルタイムプロバイダー用

## プロバイダー固有のオプション

各音声プロバイダーは、それぞれの実装に特有の追加オプションをサポートしている場合があります。以下にいくつかの例を示します：

### OpenAI

<PropertiesTable
  content={[
    {
      name: "options.filetype",
      type: "string",
      description: "音声ファイル形式（例：'mp3'、'wav'、'm4a'）",
      isOptional: true,
      defaultValue: "'mp3'",
    },
    {
      name: "options.prompt",
      type: "string",
      description: "モデルの文字起こしを導くテキスト",
      isOptional: true,
    },
    {
      name: "options.language",
      type: "string",
      description: "言語コード（例：'en'、'fr'、'de'）",
      isOptional: true,
    },
  ]}
/>

### Google

<PropertiesTable
  content={[
    {
      name: "options.stream",
      type: "boolean",
      description: "ストリーミング認識を使用するかどうか",
      isOptional: true,
      defaultValue: "false",
    },
    {
      name: "options.config",
      type: "object",
      description: "Google Cloud Speech-to-Text APIからの認識設定",
      isOptional: true,
      defaultValue: "{ encoding: 'LINEAR16', languageCode: 'en-US' }",
    },
  ]}
/>

### Deepgram

<PropertiesTable
  content={[
    {
      name: "options.model",
      type: "string",
      description: "文字起こしに使用するDeepgramモデル",
      isOptional: true,
      defaultValue: "'nova-2'",
    },
    {
      name: "options.language",
      type: "string",
      description: "文字起こしの言語コード",
      isOptional: true,
      defaultValue: "'en'",
    },
  ]}
/>

## リアルタイム音声プロバイダー

`OpenAIRealtimeVoice`のようなリアルタイム音声プロバイダーを使用する場合、`listen()`メソッドは異なる動作をします：

- 文字起こしされたテキストを返す代わりに、文字起こしされたテキストを含む「writing」イベントを発行します
- 文字起こしを受け取るためにイベントリスナーを登録する必要があります

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { getMicrophoneStream } from "@mastra/node-audio";

const voice = new OpenAIRealtimeVoice();
await voice.connect();

// Register event listener for transcription
voice.on("writing", ({ text, role }) => {
  console.log(`${role}: ${text}`);
});

// This will emit 'writing' events instead of returning text
const microphoneStream = getMicrophoneStream();
await voice.listen(microphoneStream);
```

## CompositeVoiceでの使用

`CompositeVoice`を使用する場合、`listen()`メソッドは設定されたリスニングプロバイダーに処理を委譲します：

```typescript
import { CompositeVoice } from "@mastra/core/voice";
import { OpenAIVoice } from "@mastra/voice-openai";
import { PlayAIVoice } from "@mastra/voice-playai";

const voice = new CompositeVoice({
  listenProvider: new OpenAIVoice(),
  speakProvider: new PlayAIVoice(),
});

// これはOpenAIVoiceプロバイダーを使用します
const transcript = await voice.listen(audioStream);
```

## 注意事項

- すべての音声プロバイダーが音声認識機能をサポートしているわけではありません（例：PlayAI、Speechify）
- `listen()`の動作はプロバイダーによって若干異なる場合がありますが、すべての実装は同じ基本的なインターフェースに従っています
- リアルタイム音声プロバイダーを使用する場合、このメソッドは直接テキストを返さず、代わりに「writing」イベントを発生させることがあります
- サポートされる音声フォーマットはプロバイダーによって異なります。一般的なフォーマットにはMP3、WAV、M4Aなどがあります
- 一部のプロバイダーは、テキストが文字起こしされるとすぐに返される、ストリーミング文字起こしをサポートしています
- 最高のパフォーマンスを得るには、使用が終わったら音声ストリームを閉じるか終了することを検討してください

## 関連メソッド

- [voice.speak()](./voice.speak) - テキストを音声に変換
- [voice.send()](./voice.send) - 音声データをリアルタイムで音声プロバイダーに送信
- [voice.on()](./voice.on) - 音声イベントのイベントリスナーを登録
