---
title: "リファレンス: voice.send() | 音声プロバイダー | Mastra ドキュメント"
description: "リアルタイム音声プロバイダーで利用可能なsend()メソッドのドキュメント。連続処理のための音声データをストリーミングします。"
---

# voice.send()

`send()` メソッドは、音声データをリアルタイムで音声プロバイダーにストリーミングし、継続的な処理を行います。このメソッドは、リアルタイムの音声対音声会話に不可欠であり、マイク入力を直接AIサービスに送信することができます。

## 使用例

```typescript
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import Speaker from "@mastra/node-speaker";
import { getMicrophoneStream } from "@mastra/node-audio";

const speaker = new Speaker({
  sampleRate: 24100, // オーディオのサンプルレート（Hz）- MacBook Proの高品質オーディオの標準
  channels: 1, // モノラルオーディオ出力（ステレオの場合は2）
  bitDepth: 16, // オーディオ品質のビット深度 - CD品質の標準（16ビット解像度）
});

// リアルタイム音声プロバイダーを初期化
const voice = new OpenAIRealtimeVoice({
  realtimeConfig: {
    model: "gpt-4o-mini-realtime",
    apiKey: process.env.OPENAI_API_KEY,
  },
});

// リアルタイムサービスに接続
await voice.connect();

// レスポンスのイベントリスナーを設定
voice.on("writing", ({ text, role }) => {
  console.log(`${role}: ${text}`);
});

voice.on("speaker", (stream) => {
  stream.pipe(speaker);
});

// マイクストリームを取得（実装は環境によって異なります）
const microphoneStream = getMicrophoneStream();

// 音声データを音声プロバイダーに送信
await voice.send(microphoneStream);

// Int16Arrayとして音声データを送信することもできます
const audioBuffer = getAudioBuffer(); // これはInt16Arrayを返すと仮定
await voice.send(audioBuffer);
```

## パラメータ

<br />
<PropertiesTable
  content={[
    {
      name: "audioData",
      type: "NodeJS.ReadableStream | Int16Array",
      description:
        "音声プロバイダーに送信する音声データ。読み取り可能なストリーム（マイクストリームなど）または音声サンプルのInt16Arrayを指定できます。",
      isOptional: false,
    },
  ]}
/>

## 戻り値

音声データが音声プロバイダーによって受け入れられたときに解決する`Promise<void>`を返します。

## 注意事項

- このメソッドは音声対音声機能をサポートするリアルタイム音声プロバイダーでのみ実装されています
- この機能をサポートしていない音声プロバイダーで呼び出された場合、警告をログに記録して即座に解決します
- WebSocket接続を確立するために、`send()`を使用する前に`connect()`を呼び出す必要があります
- 音声フォーマットの要件は、特定の音声プロバイダーによって異なります
- 継続的な会話では、通常ユーザーの音声を送信するために`send()`を呼び出し、その後AIの応答をトリガーするために`answer()`を呼び出します
- プロバイダーは通常、音声を処理する際に文字起こしされたテキストを含む「writing」イベントを発行します
- AIが応答すると、プロバイダーは音声応答を含む「speaking」イベントを発行します
