---
title: "ライセンス"
description: "Mastra ライセンス"
---

# ライセンス

## Elastic License 2.0 (ELv2)

Mastraは、オープンソースの原則と持続可能なビジネス慣行のバランスを取るために設計された現代的なライセンスであるElastic License 2.0 (ELv2)の下でライセンスされています。

### Elastic License 2.0とは？

Elastic License 2.0は、プロジェクトの持続可能性を保護するための特定の制限を含みながら、ソフトウェアを使用、変更、配布する広範な権利をユーザーに付与するソース利用可能なライセンスです。これにより以下が可能です：

- ほとんどの目的での無料使用
- ソースコードの閲覧、変更、再配布
- 派生作品の作成と配布
- 組織内での商業利用

主な制限は、Mastraをホストまたは管理されたサービスとして提供し、ソフトウェアの実質的な機能にユーザーがアクセスできるようにすることはできないということです。

### なぜElastic License 2.0を選んだのか

私たちはいくつかの重要な理由からElastic License 2.0を選びました：

1. **持続可能性**: 開放性と長期的な開発を維持する能力の間で健全なバランスを保つことができます。

2. **イノベーションの保護**: 私たちの作業が競合するサービスとして再パッケージされることを心配せずに、イノベーションへの投資を続けることができます。

3. **コミュニティ重視**: ユーザーが私たちのコードを閲覧、変更、学ぶことを可能にしながら、コミュニティをサポートする能力を保護することで、オープンソースの精神を維持します。

4. **ビジネスの明確さ**: Mastraが商業的な文脈でどのように使用できるかについて明確なガイドラインを提供します。

### Mastraでビジネスを構築する

ライセンスの制限にもかかわらず、Mastraを使用して成功したビジネスを構築する方法は多数あります：

#### 許可されたビジネスモデル

- **アプリケーションの構築**: Mastraを使用してアプリケーションを作成し販売する
- **コンサルティングサービスの提供**: 専門知識、実装、カスタマイズサービスを提供する
- **カスタムソリューションの開発**: クライアント向けにMastraを使用して特注のAIソリューションを構築する
- **アドオンと拡張機能の作成**: Mastraの機能を拡張する補完的なツールを開発し販売する
- **トレーニングと教育**: Mastraの効果的な使用に関するコースや教育資料を提供する

#### 準拠した使用例

- ある会社がMastraを使用してAI駆動のカスタマーサービスアプリケーションを構築し、クライアントに販売する
- コンサルティング会社がMastraの実装とカスタマイズサービスを提供する
- 開発者がMastraを使用して特殊なエージェントやツールを作成し、他の企業にライセンス供与する
- スタートアップがMastraを活用した特定の業界向けソリューション（例：医療AIアシスタント）を構築する

#### 避けるべきこと

主な制限は、Mastra自体をホストされたサービスとして提供し、そのコア機能にユーザーがアクセスできるようにすることはできないということです。つまり：

- Mastraをほとんど変更せずにSaaSプラットフォームを作成しないでください
- 主にMastraの機能を使用するために顧客が支払う管理されたMastraサービスを提供しないでください

### ライセンスに関する質問？

Elastic License 2.0があなたの使用ケースにどのように適用されるかについて具体的な質問がある場合は、[Discordでお問い合わせください](https://discord.gg/BTYqqHKUrf)。プロジェクトの持続可能性を保護しながら、正当なビジネス使用ケースをサポートすることをお約束します。
