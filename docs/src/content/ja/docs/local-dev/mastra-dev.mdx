---
title: "`mastra dev`でエージェントを検査する | Mastra ローカル開発ドキュメント"
description: MastraアプリケーションのためのMastraローカル開発環境のドキュメント。
---

import YouTube from "@/components/youtube";

# ローカル開発環境

Mastraはローカルで開発しながらエージェント、ワークフロー、ツールをテストできるローカル開発環境を提供しています。

<YouTube id="spGlcTEjuXY" />

## 開発サーバーの起動

Mastra CLIを使用してMastra開発環境を起動するには、次のコマンドを実行します：

```bash
mastra dev
```

デフォルトでは、サーバーはlocalhostのhttp://localhost:4111で実行されます。カスタムポートとホストはmastraサーバー設定で構成できます。

```typescript
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  server: {
    port: "4111",
    host: "0.0.0.0",
  },
});
```

## 開発プレイグラウンド

`mastra dev`はエージェント、ワークフロー、ツールを操作するためのプレイグラウンドUIを提供します。このプレイグラウンドは、開発中にMastraアプリケーションの各コンポーネントをテストするための専用インターフェースを提供します。

### エージェントプレイグラウンド

エージェントプレイグラウンドは、開発中にエージェントをテストしデバッグできるインタラクティブなチャットインターフェースを提供します。主な機能は以下の通りです：

- **チャットインターフェース**：エージェントと直接対話して、その応答と動作をテストできます。
- **プロンプトCMS**：エージェントの異なるシステム指示を試すことができます：
  - 異なるプロンプトバージョンのA/Bテスト。
  - 各バリアントのパフォーマンス指標の追跡。
  - 最も効果的なプロンプトバージョンの選択と展開。
- **エージェントトレース**：エージェントがリクエストを処理する方法を理解するための詳細な実行トレースを表示します：
  - プロンプト構築。
  - ツールの使用。
  - 意思決定のステップ。
  - レスポンス生成。
- **エージェント評価**：[エージェント評価指標](/docs/evals/overview)を設定している場合：
  - プレイグラウンドから直接評価を実行できます。
  - 評価結果と指標を表示できます。
  - 異なるテストケースでのエージェントのパフォーマンスを比較できます。

### ワークフロープレイグラウンド

ワークフロープレイグラウンドは、ワークフロー実装の視覚化とテストに役立ちます：

- **ワークフロー視覚化**：ワークフローグラフの視覚化。

- **ワークフローの実行**：

  - カスタム入力データでテストワークフロー実行をトリガーします。
  - ワークフローロジックと条件をデバッグします。
  - 異なる実行パスをシミュレートします。
  - 各ステップの詳細な実行ログを表示します。

- **ワークフロートレース**：以下を示す詳細な実行トレースを調査します：
  - ステップバイステップのワークフロー進行。
  - 状態遷移とデータフロー。
  - ツール呼び出しとその結果。
  - 決定ポイントと分岐ロジック。
  - エラー処理と回復パス。

### ツールプレイグラウンド

ツールプレイグラウンドでは、カスタムツールを単独でテストできます：

- 完全なエージェントやワークフローを実行せずに個々のツールをテストします。
- テストデータを入力してツールの応答を確認します。
- ツールの実装とエラー処理をデバッグします。
- ツールの入力/出力スキーマを検証します。
- ツールのパフォーマンスと実行時間を監視します。

## REST API エンドポイント

`mastra dev` は、ローカルの [Mastra Server](/docs/deployment/server) を通じて、エージェントとワークフローのための REST API ルートも起動します。これにより、デプロイ前に API エンドポイントをテストすることができます。すべてのエンドポイントの詳細については、[Mastra Dev リファレンス](/reference/cli/dev#routes) をご覧ください。

その後、[Mastra Client](/docs/deployment/client) SDK を活用して、提供された REST API ルートとシームレスにやり取りすることができます。

## OpenAPI 仕様

`mastra dev` は http://localhost:4111/openapi.json で OpenAPI 仕様を提供します

Mastra インスタンスで OpenAPI ドキュメントを有効にするには、以下の設定を追加してください：

```typescript
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  server: {
    build: {
      openAPIDocs: true, // Enable OpenAPI documentation
      // ... other build config options
    },
  },
});
```

## Swagger UI

Swagger UIは、APIエンドポイントをテストするためのインタラクティブなインターフェースを提供します。`mastra dev`はhttp://localhost:4111/swagger-uiでOpenAPI仕様を提供します。
MastraインスタンスでSwagger UIを有効にするには、以下の設定を追加してください：

```typescript
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  server: {
    build: {
      openAPIDocs: true, // Enable OpenAPI documentation
      swaggerUI: true, // Enable Swagger UI
      // ... other build config options
    },
  },
});
```

## ローカル開発アーキテクチャ

ローカル開発サーバーは、外部依存関係やコンテナ化なしで実行できるように設計されています。これは以下によって実現されています：

- **開発サーバー**: [Hono](https://hono.dev)を基盤フレームワークとして使用し、[Mastra Server](/docs/deployment/server)を動作させます。

- **インメモリストレージ**: [LibSQL](https://libsql.org/)メモリアダプターを使用して以下を実現：

  - エージェントメモリ管理。
  - トレースストレージ。
  - 評価ストレージ。
  - ワークフロースナップショット。

- **ベクトルストレージ**: [FastEmbed](https://github.com/qdrant/fastembed)を使用して以下を実現：
  - デフォルトの埋め込み生成。
  - ベクトルの保存と取得。
  - セマンティック検索機能。

このアーキテクチャにより、データベースやベクトルストアをセットアップすることなく、すぐに開発を開始できると同時に、ローカル環境でも本番環境に近い動作を維持することができます。

### モデル設定

ローカル開発サーバーでは、概要 > モデル設定でモデル設定を構成することもできます。

以下の設定を構成できます：

- **Temperature**: モデル出力のランダム性を制御します。高い値（0-2）でより創造的な応答が生成され、低い値ではより焦点を絞った決定論的な出力になります。
- **Top P**: トークンサンプリングの累積確率しきい値を設定します。低い値（0-1）では、最も可能性の高いトークンのみを考慮することで、より焦点を絞った出力になります。
- **Top K**: 各生成ステップで考慮されるトークンの数を制限します。低い値では、より少ないオプションからサンプリングすることで、より焦点を絞った出力が生成されます。
- **Frequency Penalty**: 以前のテキストでの頻度に基づいてトークンにペナルティを与えることで、繰り返しを減らします。高い値（0-2）は一般的なトークンの再利用を抑制します。
- **Presence Penalty**: 以前のテキストに出現したトークンにペナルティを与えることで、繰り返しを減らします。高い値（0-2）はモデルに新しいトピックについて議論するよう促します。
- **Max Tokens**: モデルの応答で許可される最大トークン数。高い値ではより長い出力が可能になりますが、レイテンシーが増加する可能性があります。
- **Max Steps**: ワークフローやエージェントが停止する前に実行できる最大ステップ数。無限ループや暴走プロセスを防止します。
- **Max Retries**: 失敗したAPI呼び出しやモデルリクエストを諦める前に再試行する回数。一時的な障害を適切に処理するのに役立ちます。

## 概要

`mastra dev` は、本番環境にデプロイする前に、自己完結型の環境でAIロジックを開発、デバッグ、反復することを容易にします。

- [Mastra Dev リファレンス](../../reference/cli/dev.mdx)
