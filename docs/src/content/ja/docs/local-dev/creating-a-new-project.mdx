---
title: "新しいプロジェクトの作成 | Mastra ローカル開発ドキュメント"
description: "CLI を使って新しい Mastra プロジェクトを作成したり、既存の Node.js アプリケーションに Mastra を追加したりします"
---

# 新しいプロジェクトの作成

`create-mastra`パッケージを使用して新しいプロジェクトを作成できます：

```bash npm2yarn copy
npm create mastra@latest
```

`mastra` CLIを直接使用して新しいプロジェクトを作成することもできます：

```bash npm2yarn copy
npm install -g mastra@latest
mastra create
```

## インタラクティブセットアップ

引数なしでコマンドを実行すると、CLIプロンプトが開始され、以下の項目が設定されます：

1. プロジェクト名
1. コンポーネントの選択
1. LLMプロバイダーの設定
1. APIキーのセットアップ
1. サンプルコードの追加

## 非対話型セットアップ

非対話型モードでmastraを初期化するには、以下のコマンド引数を使用してください。

```bash
Arguments:
  --components     Specify components: agents, tools, workflows
  --llm-provider   LLM provider: openai, anthropic, groq, google, or cerebras
  --add-example    Include example implementation
  --llm-api-key    Provider API key
  --project-name   Project name that will be used in package.json and as the project directory name
```

生成されるプロジェクト構成:

```
my-project/
├── src/
│   └── mastra/
│       └── index.ts    # Mastra entry point
├── package.json
└── tsconfig.json
```
