---
title: Mastra におけるテキスト読み上げ（TTS） | Mastra ドキュメント
description: Mastra のテキスト読み上げ機能の概要、設定方法、使用方法、音声プロバイダーとの連携について説明します。
---

# Text-to-Speech (TTS)

MastraのText-to-Speech (TTS)は、さまざまなプロバイダーを利用してテキストから音声を合成するための統一APIを提供します。
TTSをアプリケーションに組み込むことで、自然な音声によるインタラクションでユーザー体験を向上させたり、視覚障害のあるユーザーのアクセシビリティを改善したり、より魅力的なマルチモーダルインターフェースを作成したりできます。

TTSは、あらゆる音声アプリケーションの中核となるコンポーネントです。STT（Speech-to-Text）と組み合わせることで、音声インタラクションシステムの基盤を形成します。新しいモデルではSTS（[Speech-to-Speech](./speech-to-speech)）もサポートされており、リアルタイムのインタラクションに利用できますが、コストが高い（$）という特徴があります。

## 設定

MastraでTTSを使用するには、ボイスプロバイダーを初期化する際に`speechModel`を指定する必要があります。これには以下のようなパラメータが含まれます：

- **`name`**: 使用する特定のTTSモデル。
- **`apiKey`**: 認証用のAPIキー。
- **プロバイダー固有のオプション**: 特定のボイスプロバイダーで必要またはサポートされている追加オプション。

**`speaker`**オプションを使用すると、音声合成のために異なる声を選択できます。各プロバイダーは、**音声の多様性**、**品質**、**声の個性**、**多言語対応**など、さまざまな特徴を持つボイスオプションを提供しています。

**注意**: これらのパラメータはすべて任意です。使用しているボイスプロバイダーによって異なりますが、プロバイダーが提供するデフォルト設定を利用することもできます。

```typescript
const voice = new OpenAIVoice({
  speechModel: {
    name: "tts-1-hd",
    apiKey: process.env.OPENAI_API_KEY,
  },
  speaker: "alloy",
});

// If using default settings the configuration can be simplified to:
const voice = new OpenAIVoice();
```

## 利用可能なプロバイダー

Mastra は幅広い Text-to-Speech プロバイダーに対応しており、それぞれ独自の機能や音声オプションを提供しています。アプリケーションのニーズに最適なプロバイダーを選択できます。

- [**OpenAI**](/reference/voice/openai/) - 自然なイントネーションと表現力を持つ高品質な音声
- [**Azure**](/reference/voice/azure/) - Microsoft の音声サービスで、多様な音声と言語に対応
- [**ElevenLabs**](/reference/voice/elevenlabs/) - 感情表現や細かなコントロールが可能な超リアルな音声
- [**PlayAI**](/reference/voice/playai/) - 様々なスタイルの自然な音声に特化
- [**Google**](/reference/voice/google/) - Google の多言語対応音声合成
- [**Cloudflare**](/reference/voice/cloudflare/) - 低遅延アプリケーション向けのエッジ最適化音声合成
- [**Deepgram**](/reference/voice/deepgram/) - 高精度な AI 音声技術
- [**Speechify**](/reference/voice/speechify/) - 読みやすさとアクセシビリティに最適化された Text-to-Speech
- [**Sarvam**](/reference/voice/sarvam/) - インド系言語やアクセントに特化
- [**Murf**](/reference/voice/murf/) - パラメータをカスタマイズ可能なスタジオ品質のナレーション

各プロバイダーは、必要に応じてインストールできる個別のパッケージとして実装されています。

```bash
pnpm add @mastra/voice-openai  # Example for OpenAI
```

## speak メソッドの使用

TTS の主なメソッドは `speak()` メソッドであり、テキストを音声に変換します。このメソッドは、話者やその他のプロバイダー固有のオプションを指定できるオプションを受け取ることができます。使い方は以下の通りです：

```typescript
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { OpenAIVoice } from "@mastra/voice-openai";

const voice = new OpenAIVoice();

const agent = new Agent({
  name: "Voice Agent",
  instructions:
    "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice,
});

const { text } = await agent.generate("What color is the sky?");

// Convert text to speech to an Audio Stream
const readableStream = await voice.speak(text, {
  speaker: "default", // Optional: specify a speaker
  properties: {
    speed: 1.0, // Optional: adjust speech speed
    pitch: "default", // Optional: specify pitch if supported
  },
});
```

TTS をエージェントで使用する方法については、[Adding Voice to Agents](../agents/adding-voice.mdx) のドキュメントもご覧ください。
