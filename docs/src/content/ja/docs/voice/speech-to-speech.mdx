---
title: Mastraにおける音声対音声機能 | Mastra ドキュメント
description: Mastraの音声対音声機能の概要。リアルタイムの対話やイベント駆動型アーキテクチャについて説明します。
---

# Mastraの音声対音声機能

## はじめに

MastraのSpeech-to-Speech（STS）は、複数のプロバイダー間でリアルタイムなやり取りを可能にする標準化されたインターフェースを提供します。  
STSは、Realtimeモデルからのイベントをリッスンすることで、継続的な双方向音声通信を実現します。個別のTTSやSTT操作とは異なり、STSは両方向の音声を継続的に処理するオープンな接続を維持します。

## 設定

- **`apiKey`**: あなたのOpenAI APIキー。`OPENAI_API_KEY`環境変数にフォールバックします。
- **`model`**: リアルタイム音声対話に使用するモデルID（例：`gpt-4o-mini-realtime`）。
- **`speaker`**: 音声合成のデフォルトボイスID。音声出力に使用する声を指定できます。

```typescript
const voice = new OpenAIRealtimeVoice({
  apiKey: "your-openai-api-key",
  model: "gpt-4o-mini-realtime",
  speaker: "alloy", // Default voice
});

// If using default settings the configuration can be simplified to:
const voice = new OpenAIRealtimeVoice();
```

## STSの使用

```typescript
import { Agent } from "@mastra/core/agent";
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { playAudio, getMicrophoneStream } from "@mastra/node-audio";

const agent = new Agent({
  name: "Agent",
  instructions: `You are a helpful assistant with real-time voice capabilities.`,
  model: openai("gpt-4o"),
  voice: new OpenAIRealtimeVoice(),
});

// Connect to the voice service
await agent.voice.connect();

// Listen for agent audio responses
agent.voice.on("speaker", ({ audio }) => {
  playAudio(audio);
});

// Initiate the conversation
await agent.voice.speak("How can I help you today?");

// Send continuous audio from the microphone
const micStream = getMicrophoneStream();
await agent.voice.send(micStream);
```

音声対音声機能をエージェントに統合するには、[エージェントに音声を追加する](../agents/adding-voice.mdx)のドキュメントを参照してください。
