---
title: Mastra における音声認識（STT） | Mastra ドキュメント
description: Mastra の音声認識機能の概要、設定方法、使用方法、音声プロバイダーとの連携について説明します。
---

# 音声認識（STT）

Mastraの音声認識（STT）は、複数のサービスプロバイダー間で音声入力をテキストに変換するための標準化されたインターフェースを提供します。
STTは、人間の音声に応答できる音声対応アプリケーションの作成を支援し、ハンズフリーでの操作、障害を持つユーザーのためのアクセシビリティ、そしてより自然な人間とコンピューターのインターフェースを可能にします。

## 設定

MastraでSTTを使用するには、ボイスプロバイダーを初期化する際に`listeningModel`を指定する必要があります。これには以下のようなパラメータが含まれます：

- **`name`**: 使用する特定のSTTモデル。
- **`apiKey`**: 認証用のAPIキー。
- **プロバイダー固有のオプション**: 特定のボイスプロバイダーで必要またはサポートされている追加オプション。

**注意**: これらのパラメータはすべて省略可能です。使用しているボイスプロバイダーによって異なりますが、プロバイダーが提供するデフォルト設定を利用することもできます。

```typescript
const voice = new OpenAIVoice({
  listeningModel: {
    name: "whisper-1",
    apiKey: process.env.OPENAI_API_KEY,
  },
});

// If using default settings the configuration can be simplified to:
const voice = new OpenAIVoice();
```

## 利用可能なプロバイダー

Mastra は複数の Speech-to-Text プロバイダーをサポートしており、それぞれ独自の機能と強みがあります。

- [**OpenAI**](/reference/voice/openai/) - Whisper モデルによる高精度な文字起こし
- [**Azure**](/reference/voice/azure/) - Microsoft のエンタープライズレベルの信頼性を持つ音声認識
- [**ElevenLabs**](/reference/voice/elevenlabs/) - 複数言語対応の高度な音声認識
- [**Google**](/reference/voice/google/) - 幅広い言語サポートを持つ Google の音声認識
- [**Cloudflare**](/reference/voice/cloudflare/) - 低遅延アプリケーション向けのエッジ最適化音声認識
- [**Deepgram**](/reference/voice/deepgram/) - 様々なアクセントに対応した高精度の AI 音声認識
- [**Sarvam**](/reference/voice/sarvam/) - インド系言語とアクセントに特化

各プロバイダーは、必要に応じてインストールできる個別のパッケージとして実装されています。

```bash
pnpm add @mastra/voice-openai  # Example for OpenAI
```

## Listen メソッドの使用方法

STT の主なメソッドは `listen()` メソッドであり、話された音声をテキストに変換します。使い方は以下の通りです。

```typescript
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { OpenAIVoice } from "@mastra/voice-openai";
import { getMicrophoneStream } from "@mastra/node-audio";

const voice = new OpenAIVoice();

const agent = new Agent({
  name: "Voice Agent",
  instructions:
    "You are a voice assistant that provides recommendations based on user input.",
  model: openai("gpt-4o"),
  voice,
});

const audioStream = getMicrophoneStream(); // Assume this function gets audio input

const transcript = await agent.voice.listen(audioStream, {
  filetype: "m4a", // Optional: specify the audio file type
});

console.log(`User said: ${transcript}`);

const { text } = await agent.generate(
  `Based on what the user said, provide them a recommendation: ${transcript}`,
);

console.log(`Recommendation: ${text}`);
```

STT をエージェントで使用する方法については、[エージェントに音声を追加する](../agents/adding-voice.mdx) ドキュメントもご覧ください。
