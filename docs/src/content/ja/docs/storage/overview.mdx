---
title: Mastraのストレージ | Mastraドキュメント
description: Mastraのストレージシステムとデータ永続化機能の概要。
---

import { Tabs } from "nextra/components";

import { PropertiesTable } from "@/components/properties-table";
import { SchemaTable } from "@/components/schema-table";
import { StorageOverviewImage } from "@/components/storage-overview-image";

# MastraStorage

`MastraStorage`は以下を管理するための統一インターフェースを提供します：

- **一時停止されたワークフロー**：一時停止されたワークフローのシリアル化された状態（後で再開できるように）
- **メモリ**：アプリケーション内の`resourceId`ごとのスレッドとメッセージ
- **トレース**：MastraのすべてのコンポーネントからのOpenTelemetryトレース
- **評価データセット**：評価実行からのスコアと採点理由

<br />

<br />

<StorageOverviewImage />

Mastraは異なるストレージプロバイダーを提供していますが、それらを互換性のあるものとして扱うことができます。例えば、開発環境ではlibsqlを使用し、本番環境ではpostgresを使用することができ、コードは両方の環境で同じように動作します。

## 設定

Mastraはデフォルトのストレージオプションで設定できます：

```typescript copy
import { Mastra } from "@mastra/core/mastra";
import { LibSQLStore } from "@mastra/libsql";

const mastra = new Mastra({
  storage: new LibSQLStore({
    url: "file:./mastra.db",
  }),
});
```

`storage`設定を指定しない場合、Mastraは自動的にローカルの`LibSQLStore`（`file:mastra.db`）にフォールバックします。この**デフォルトストレージ**は開発と簡単なプロトタイプに最適です。データはページの更新後も保持されますが、サーバープロセスが再起動したり、一時的な環境にデプロイしたりすると失われます。ほとんどの実際のアプリケーションでは、明示的なストレージインスタンスを提供する必要があります。エージェントに設定した`Memory`は、`Mastra`に提供するストレージを使用します。両方の場所で省略した場合は、上記で説明したデフォルトのストレージが使用されます。

## データスキーマ

<Tabs items={['Messages', 'Threads', 'Workflows', 'Eval Datasets', 'Traces']}>
  <Tabs.Tab>
    会話メッセージとそのメタデータを保存します。各メッセージはスレッドに属し、実際のコンテンツと送信者の役割やメッセージタイプに関するメタデータを含んでいます。

    <br />

    <SchemaTable
      columns={[

{
name: "id",
type: "uuidv4",
description: "メッセージの一意の識別子（形式：`xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`）",
constraints: [
{ type: "primaryKey" },
{ type: "nullable", value: false }
]
},
{
name: "thread_id",
type: "uuidv4",
description: "親スレッドの参照",
constraints: [
{ type: "foreignKey", value: "threads.id" },
{ type: "nullable", value: false }
]
},
{
name: "content",
type: "text",
constraints: [{ type: "nullable", value: false }]
},
{
name: "role",
type: "text",
description: "`system | user | assistant | tool`の列挙型",
constraints: [{ type: "nullable", value: false }]
},
{
name: "type",
type: "text",
description: "`text | tool-call | tool-result`の列挙型",
constraints: [{ type: "nullable", value: false }]
},
{
name: "createdAt",
type: "timestamp",
description: "スレッドメッセージの順序付けに使用",
constraints: [{ type: "nullable", value: false }]
}
]}
/>

  </Tabs.Tab>

  <Tabs.Tab>
    関連するメッセージをグループ化し、リソースに関連付けます。会話に関するメタデータを含みます。

    <br />

    <SchemaTable
      columns={[

{
name: "id",
type: "uuidv4",
description: "スレッドの一意の識別子（形式：`xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`）",
constraints: [
{ type: "primaryKey" },
{ type: "nullable", value: false }
]
},
{
name: "resourceId",
type: "text",
description: "このスレッドが関連付けられている外部リソースの主要識別子。関連するスレッドのグループ化と取得に使用されます。",
constraints: [{ type: "nullable", value: false }]
},
{
name: "title",
type: "text",
description: "会話スレッドのタイトル",
constraints: [{ type: "nullable", value: false }]
},
{
name: "metadata",
type: "text",
description: "文字列化されたJSONとしてのカスタムスレッドメタデータ。例：",
example: {
category: "support",
priority: 1
}
},
{
name: "createdAt",
type: "timestamp",
constraints: [{ type: "nullable", value: false }]
},
{
name: "updatedAt",
type: "timestamp",
description: "スレッド履歴の順序付けに使用",
constraints: [{ type: "nullable", value: false }]
}
]}
/>

  </Tabs.Tab>

  <Tabs.Tab>
    ワークフローで`suspend`が呼び出されると、その状態は以下の形式で保存されます。`resume`が呼び出されると、その状態が再構築されます。

    <br />

    <SchemaTable
      columns={[

{
name: "workflow_name",
type: "text",
description: "ワークフローの名前",
constraints: [{ type: "nullable", value: false }]
},
{
name: "run_id",
type: "uuidv4",
description: "ワークフロー実行の一意の識別子。suspend/resumeサイクル間で状態を追跡するために使用（形式：`xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`）",
constraints: [{ type: "nullable", value: false }]
},
{
name: "snapshot",
type: "text",
description: "JSONとしてシリアル化されたワークフロー状態。例：",
example: {
value: { currentState: 'running' },
context: {
stepResults: {},
attempts: {},
triggerData: {}
},
activePaths: [],
runId: '550e8400-e29b-41d4-a716-************',
timestamp: 1648176000000
},
constraints: [{ type: "nullable", value: false }]
},
{
name: "createdAt",
type: "timestamp",
constraints: [{ type: "nullable", value: false }]
},
{
name: "updatedAt",
type: "timestamp",
description: "最終更新時間、ワークフロー実行中の状態変化を追跡するために使用",
constraints: [{ type: "nullable", value: false }]
}
]}
/>

  </Tabs.Tab>

  <Tabs.Tab>
    エージェント出力に対してメトリクスを実行した評価結果を保存します。

    <br />

    <SchemaTable
      columns={[

{
name: "input",
type: "text",
description: "エージェントに提供された入力",
constraints: [{ type: "nullable", value: false }]
},
{
name: "output",
type: "text",
description: "エージェントによって生成された出力",
constraints: [{ type: "nullable", value: false }]
},
{
name: "result",
type: "jsonb",
description: "スコアと詳細を含む評価結果データ。例：",
example: {
score: 0.95,
details: {
reason: "応答はソース資料を正確に反映しています",
citations: ["1ページ目", "3ページ目"]
}
},
constraints: [{ type: "nullable", value: false }]
},
{
name: "agent_name",
type: "text",
constraints: [{ type: "nullable", value: false }]
},
{
name: "metric_name",
type: "text",
description: "例：忠実性、幻覚など",
constraints: [{ type: "nullable", value: false }]
},
{
name: "instructions",
type: "text",
description: "エージェントのシステムプロンプトまたは指示",
constraints: [{ type: "nullable", value: false }]
},
{
name: "test_info",
type: "jsonb",
description: "追加のテストメタデータと設定",
constraints: [{ type: "nullable", value: false }]
},
{
name: "global_run_id",
type: "uuidv4",
description: "関連する評価実行をグループ化します（例：CI実行内のすべての単体テスト）",
constraints: [{ type: "nullable", value: false }]
},
{
name: "run_id",
type: "uuidv4",
description: "評価される実行の一意の識別子（形式：`xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`）",
constraints: [{ type: "nullable", value: false }]
},
{
name: "created_at",
type: "timestamp",
constraints: [{ type: "nullable", value: false }]
}
]}
/>

  </Tabs.Tab>

  <Tabs.Tab>
    モニタリングとデバッグのためのOpenTelemetryトレースを取得します。

    <br />

    <SchemaTable
      columns={[

{
name: "id",
type: "text",
description: "一意のトレース識別子",
constraints: [
{ type: "nullable", value: false },
{ type: "primaryKey" }
]
},
{
name: "parentSpanId",
type: "text",
description: "親スパンのID。スパンが最上位の場合はNull",
},
{
name: "name",
type: "text",
description: "階層的な操作名（例：`workflow.myWorkflow.execute`、`http.request`、`database.query`）",
constraints: [{ type: "nullable", value: false }],
},
{
name: "traceId",
type: "text",
description: "関連するスパンをグループ化するルートトレース識別子",
constraints: [{ type: "nullable", value: false }]
},
{
name: "scope",
type: "text",
description: "スパンを作成したライブラリ/パッケージ/サービス（例：`@mastra/core`、`express`、`pg`）",
constraints: [{ type: "nullable", value: false }]
},
{
name: "kind",
type: "integer",
description: "`INTERNAL`（0、プロセス内）、`CLIENT`（1、発信呼び出し）、`SERVER`（2、着信呼び出し）、`PRODUCER`（3、非同期ジョブ作成）、`CONSUMER`（4、非同期ジョブ処理）",
constraints: [{ type: "nullable", value: false }]
},
{
name: "attributes",
type: "jsonb",
description: "スパンのメタデータを含むユーザー定義のキーと値のペア",
},
{
name: "status",
type: "jsonb",
description: "`code`（UNSET=0、ERROR=1、OK=2）とオプションの`message`を持つJSONオブジェクト。例：",
example: {
code: 1,
message: "HTTP request failed with status 500"
}
},
{
name: "events",
type: "jsonb",
description: "スパン中に発生したタイムスタンプ付きイベント",
},
{
name: "links",
type: "jsonb",
description: "他の関連するスパンへのリンク",
},
{
name: "other",
type: "text",
description: "文字列化されたJSONとしての追加のOpenTelemetryスパンフィールド。例：",
example: {
droppedAttributesCount: 2,
droppedEventsCount: 1,
instrumentationLibrary: "@opentelemetry/instrumentation-http"
}
},
{
name: "startTime",
type: "bigint",
description: "スパンが開始されたUnixエポックからのナノ秒",
constraints: [{ type: "nullable", value: false }]
},
{
name: "endTime",
type: "bigint",
description: "スパンが終了したUnixエポックからのナノ秒",
constraints: [{ type: "nullable", value: false }]
},
{
name: "createdAt",
type: "timestamp",
constraints: [{ type: "nullable", value: false }]
}
]}
/>

  </Tabs.Tab>
</Tabs>

## ストレージプロバイダー

Mastraは以下のプロバイダーをサポートしています：

- ローカル開発には、[LibSQL Storage](../../reference/storage/libsql.mdx)をご確認ください
- 本番環境には、[PostgreSQL Storage](../../reference/storage/postgresql.mdx)をご確認ください
- サーバーレスデプロイメントには、[Upstash Storage](../../reference/storage/upstash.mdx)をご確認ください
