---
title: "ベクトルデータベースへのエンベディングの保存 | Mastra Docs"
description: Mastraにおけるベクトルストレージオプションのガイド。類似性検索のための組み込みおよび専用ベクトルデータベースを含みます。
---

import { Tabs } from "nextra/components";

## ベクトルデータベースへの埋め込みの保存

埋め込みを生成した後、ベクトル類似性検索をサポートするデータベースに保存する必要があります。Ma<PERSON>は、さまざまなベクトルデータベース間で埋め込みを保存およびクエリするための一貫したインターフェースを提供します。

## サポートされているデータベース

<Tabs items={['Pg Vector', 'Pinecone', 'Qdrant', 'Chroma', 'Astra', 'LibSQL', 'Upstash', 'Cloudflare', 'MongoDB', 'OpenSearch']}>
  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
      import { PgVector } from '@mastra/pg';

    const store = new PgVector({ connectionString: process.env.POSTGRES_CONNECTION_STRING })
    await store.createIndex({
    indexName: "myCollection",
    dimension: 1536,
    });
    await store.upsert({
    indexName: "myCollection",
    vectors: embeddings,
    metadata: chunks.map(chunk => ({ text: chunk.text })),
    });

    ```

    ### pgvectorを使用したPostgreSQLの利用

    pgvector拡張機能を備えたPostgreSQLは、すでにPostgreSQLを使用しているチームがインフラの複雑さを最小限に抑えたい場合に適したソリューションです。
    詳細なセットアップ手順とベストプラクティスについては、[pgvectorの公式リポジトリ](https://github.com/pgvector/pgvector)を参照してください。

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy

      import { PineconeVector } from '@mastra/pinecone'

      const store = new PineconeVector({
        apiKey: process.env.PINECONE_API_KEY,
      })
      await store.createIndex({
        indexName: "myCollection",
        dimension: 1536,
      });
      await store.upsert({
        indexName: "myCollection",
        vectors: embeddings,
        metadata: chunks.map(chunk => ({ text: chunk.text })),
      });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
      import { QdrantVector } from '@mastra/qdrant'

    const store = new QdrantVector({
    url: process.env.QDRANT_URL,
    apiKey: process.env.QDRANT_API_KEY
    })
    await store.createIndex({
    indexName: "myCollection",
    dimension: 1536,
    });
    await store.upsert({
    indexName: "myCollection",
    vectors: embeddings,
    metadata: chunks.map(chunk => ({ text: chunk.text })),
    });

    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
    import { ChromaVector } from '@mastra/chroma'

    const store = new ChromaVector()
    await store.createIndex({
      indexName: "myCollection",
      dimension: 1536,
    });
    await store.upsert({
      indexName: "myCollection",
      vectors: embeddings,
      metadata: chunks.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
      import { AstraVector } from '@mastra/astra'

    const store = new AstraVector({
    token: process.env.ASTRA_DB_TOKEN,
    endpoint: process.env.ASTRA_DB_ENDPOINT,
    keyspace: process.env.ASTRA_DB_KEYSPACE
    })
    await store.createIndex({
    indexName: "myCollection",
    dimension: 1536,
    });
    await store.upsert({
    indexName: "myCollection",
    vectors: embeddings,
    metadata: chunks.map(chunk => ({ text: chunk.text })),
    });

    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
    import { LibSQLVector } from "@mastra/core/vector/libsql";

    const store = new LibSQLVector({
      connectionUrl: process.env.DATABASE_URL,
      authToken: process.env.DATABASE_AUTH_TOKEN // Optional: for Turso cloud databases
    })
    await store.createIndex({
      indexName: "myCollection",
      dimension: 1536,
    });
    await store.upsert({
      indexName: "myCollection",
      vectors: embeddings,
      metadata: chunks.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
      import { UpstashVector } from '@mastra/upstash'

    const store = new UpstashVector({
    url: process.env.UPSTASH_URL,
    token: process.env.UPSTASH_TOKEN
    })
    await store.createIndex({
    indexName: "myCollection",
    dimension: 1536,
    });
    await store.upsert({
    indexName: "myCollection",
    vectors: embeddings,
    metadata: chunks.map(chunk => ({ text: chunk.text })),
    });

    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
    import { CloudflareVector } from '@mastra/vectorize'

    const store = new CloudflareVector({
      accountId: process.env.CF_ACCOUNT_ID,
      apiToken: process.env.CF_API_TOKEN
    })
    await store.createIndex({
      indexName: "myCollection",
      dimension: 1536,
    });
    await store.upsert({
      indexName: "myCollection",
      vectors: embeddings,
      metadata: chunks.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
      import { MongoDBVector } from '@mastra/mongodb'

    const store = new MongoDBVector({
    url: process.env.MONGODB_URL,
    database: process.env.MONGODB_DATABASE
    })
    await store.createIndex({
    indexName: "myCollection",
    dimension: 1536,
    });
    await store.upsert({
    indexName: "myCollection",
    vectors: embeddings,
    metadata: chunks.map(chunk => ({ text: chunk.text })),
    });

    ```

  </Tabs.Tab>

  <Tabs.Tab>
    ```ts filename="vector-store.ts" showLineNumbers copy
    import { OpenSearchVector } from '@mastra/opensearch'

    const store = new OpenSearchVector(process.env.OPENSEARCH_URL)
    await store.createIndex({
      indexName: "my-collection",
      dimension: 1536,
    });
    await store.upsert({
      indexName: "my-collection",
      vectors: embeddings,
      metadata: chunks.map(chunk => ({ text: chunk.text })),
    });
    ```

  </Tabs.Tab>
</Tabs>

## ベクトルストレージの使用

初期化後、すべてのベクトルストアは、インデックスの作成、埋め込みのアップサート、およびクエリのための同じインターフェースを共有します。

### インデックスの作成

埋め込みを保存する前に、埋め込みモデルに適切な次元サイズでインデックスを作成する必要があります：

```ts filename="store-embeddings.ts" showLineNumbers copy
// Create an index with dimension 1536 (for text-embedding-3-small)
await store.createIndex({
  indexName: "myCollection",
  dimension: 1536,
});
```

次元サイズは、選択した埋め込みモデルの出力次元と一致する必要があります。一般的な次元サイズは以下の通りです：

- OpenAI text-embedding-3-small: 1536次元（またはカスタム、例えば256）
- Cohere embed-multilingual-v3: 1024次元
- Google `text-embedding-004`: 768次元（またはカスタム）

> **重要**: インデックスの次元は作成後に変更できません。異なるモデルを使用するには、インデックスを削除して新しい次元サイズで再作成してください。

### データベースの命名規則

各ベクトルデータベースは、互換性を確保し競合を防ぐために、インデックスとコレクションに特定の命名規則を適用しています。

<Tabs items={['Pg Vector', 'Pinecone', 'Qdrant', 'Chroma', 'Astra', 'LibSQL', 'Upstash', 'Cloudflare', 'MongoDB', 'OpenSearch']}>
  <Tabs.Tab>
    インデックス名は以下の条件を満たす必要があります：
    - 文字またはアンダースコアで始まる
    - 文字、数字、アンダースコアのみを含む
    - 例：`my_index_123` は有効
    - 例：`my-index` は無効（ハイフンを含む）
  </Tabs.Tab>
  <Tabs.Tab>
    インデックス名は以下の条件を満たす必要があります：
    - 小文字、数字、ダッシュのみを使用
    - ドット（DNS ルーティングに使用）を含まない
    - 非ラテン文字や絵文字を使用しない
    - プロジェクトIDと合わせた長さが52文字未満
      - 例：`my-index-123` は有効
      - 例：`my.index` は無効（ドットを含む）
  </Tabs.Tab>
  <Tabs.Tab>
    コレクション名は以下の条件を満たす必要があります：
    - 1〜255文字の長さ
    - 以下の特殊文字を含まない：
      - `< > : " / \ | ? *`
      - ヌル文字（`\0`）
      - ユニットセパレータ（`\u{1F}`）
    - 例：`my_collection_123` は有効
    - 例：`my/collection` は無効（スラッシュを含む）
  </Tabs.Tab>
  <Tabs.Tab>
    コレクション名は以下の条件を満たす必要があります：
    - 3〜63文字の長さ
    - 文字または数字で始まり、終わる
    - 文字、数字、アンダースコア、ハイフンのみを含む
    - 連続したピリオド（..）を含まない
    - 有効なIPv4アドレスでない
    - 例：`my-collection-123` は有効
    - 例：`my..collection` は無効（連続したピリオド）
  </Tabs.Tab>
  <Tabs.Tab>
    コレクション名は以下の条件を満たす必要があります：
    - 空でない
    - 48文字以下
    - 文字、数字、アンダースコアのみを含む
    - 例：`my_collection_123` は有効
    - 例：`my-collection` は無効（ハイフンを含む）
  </Tabs.Tab>
  <Tabs.Tab>
    インデックス名は以下の条件を満たす必要があります：
    - 文字またはアンダースコアで始まる
    - 文字、数字、アンダースコアのみを含む
    - 例：`my_index_123` は有効
    - 例：`my-index` は無効（ハイフンを含む）
  </Tabs.Tab>
  <Tabs.Tab>
    名前空間名は以下の条件を満たす必要があります：
    - 2〜100文字の長さ
    - 以下のみを含む：
      - 英数字（a-z、A-Z、0-9）
      - アンダースコア、ハイフン、ドット
    - 特殊文字（_、-、.）で始まらない、または終わらない
    - 大文字と小文字は区別される
    - 例：`MyNamespace123` は有効
    - 例：`_namespace` は無効（アンダースコアで始まる）
  </Tabs.Tab>
  <Tabs.Tab>
    インデックス名は以下の条件を満たす必要があります：
    - 文字で始まる
    - 32文字未満
    - 小文字のASCII文字、数字、ダッシュのみを含む
    - スペースの代わりにダッシュを使用
    - 例：`my-index-123` は有効
    - 例：`My_Index` は無効（大文字とアンダースコア）
  </Tabs.Tab>
  <Tabs.Tab>
    コレクション（インデックス）名は以下の条件を満たす必要があります：
    - 文字またはアンダースコアで始まる
    - 最大120バイトの長さ
    - 文字、数字、アンダースコア、ドットのみを含む
    - `$`やヌル文字を含まない
    - 例：`my_collection.123` は有効
    - 例：`my-index` は無効（ハイフンを含む）
    - 例：`My$Collection` は無効（`$`を含む）
  </Tabs.Tab>
  <Tabs.Tab>
    インデックス名は以下の条件を満たす必要があります：
    - 小文字のみを使用
    - アンダースコアやハイフンで始まらない
    - スペース、カンマを含まない
    - 特殊文字（例：`:`、`"`、`*`、`+`、`/`、`\`、`|`、`?`、`#`、`>`、`<`）を含まない
    - 例：`my-index-123` は有効
    - 例：`My_Index` は無効（大文字を含む）
    - 例：`_myindex` は無効（アンダースコアで始まる）
  </Tabs.Tab>
</Tabs>

### 埋め込みのアップサート

インデックスを作成した後、埋め込みとその基本的なメタデータを保存できます：

```ts filename="store-embeddings.ts" showLineNumbers copy
// Store embeddings with their corresponding metadata
await store.upsert({
  indexName: "myCollection", // index name
  vectors: embeddings, // array of embedding vectors
  metadata: chunks.map((chunk) => ({
    text: chunk.text, // The original text content
    id: chunk.id, // Optional unique identifier
  })),
});
```

upsert操作は以下を行います：

- 埋め込みベクトルとそれに対応するメタデータの配列を受け取ります
- 同じIDを持つ既存のベクトルを更新します
- 存在しないベクトルを新規作成します
- 大規模なデータセットのバッチ処理を自動的に処理します

異なるベクトルストアでの埋め込みのupsertの完全な例については、[埋め込みのUpsert](../../examples/rag/upsert/upsert-embeddings.mdx)ガイドを参照してください。

## メタデータの追加

ベクトルストアはフィルタリングと整理のためのリッチなメタデータ（JSONシリアライズ可能なフィールド）をサポートしています。メタデータは固定されたスキーマなしで保存されるため、予期しないクエリ結果を避けるために一貫したフィールド命名を使用してください。

**重要**：メタデータはベクトルストレージにとって非常に重要です - これがなければ、数値的な埋め込みだけが残り、元のテキストを返したり結果をフィルタリングしたりする方法がなくなります。少なくとも元のテキストをメタデータとして常に保存してください。

```ts showLineNumbers copy
// Store embeddings with rich metadata for better organization and filtering
await store.upsert({
  indexName: "myCollection",
  vectors: embeddings,
  metadata: chunks.map((chunk) => ({
    // Basic content
    text: chunk.text,
    id: chunk.id,

    // Document organization
    source: chunk.source,
    category: chunk.category,

    // Temporal metadata
    createdAt: new Date().toISOString(),
    version: "1.0",

    // Custom fields
    language: chunk.language,
    author: chunk.author,
    confidenceScore: chunk.score,
  })),
});
```

メタデータに関する重要な考慮事項：

- フィールド命名に厳格であること - 「category」と「Category」のような不一致はクエリに影響します
- フィルタリングやソートに使用する予定のフィールドのみを含める - 余分なフィールドはオーバーヘッドを追加します
- コンテンツの鮮度を追跡するためのタイムスタンプ（例：「createdAt」、「lastUpdated」）を追加する

## ベストプラクティス

- 大量挿入の前にインデックスを作成する
- 大量挿入にはバッチ操作を使用する（upsertメソッドは自動的にバッチ処理を行います）
- クエリを実行する予定のメタデータのみを保存する
- 埋め込みの次元数をモデルに合わせる（例：`text-embedding-3-small`の場合は1536）
