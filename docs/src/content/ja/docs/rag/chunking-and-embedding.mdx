---
title: 文書のチャンキングと埋め込み | RAG | Mastra ドキュメント
description: Mastraでの効率的な処理と検索のための文書のチャンキングと埋め込みに関するガイド。
---

## ドキュメントのチャンク化と埋め込み

処理を行う前に、コンテンツからMDocumentインスタンスを作成します。様々な形式から初期化することができます：

```ts showLineNumbers copy
const docFromText = MDocument.fromText("Your plain text content...");
const docFromHTML = MDocument.fromHTML("<html>Your HTML content...</html>");
const docFromMarkdown = MDocument.fromMarkdown("# Your Markdown content...");
const docFromJSON = MDocument.fromJSON(`{ "key": "value" }`);
```

## ステップ1：ドキュメント処理

`chunk`を使用してドキュメントを管理しやすい部分に分割します。Mastraは異なるドキュメントタイプに最適化された複数のチャンキング戦略をサポートしています：

- `recursive`：コンテンツ構造に基づくスマートな分割
- `character`：シンプルな文字ベースの分割
- `token`：トークンを考慮した分割
- `markdown`：Markdownを考慮した分割
- `html`：HTML構造を考慮した分割
- `json`：JSON構造を考慮した分割
- `latex`：LaTeX構造を考慮した分割

以下は`recursive`戦略を使用する例です：

```ts showLineNumbers copy
const chunks = await doc.chunk({
  strategy: "recursive",
  size: 512,
  overlap: 50,
  separator: "\n",
  extract: {
    metadata: true, // オプションでメタデータを抽出
  },
});
```

**注意：** メタデータ抽出にはLLM呼び出しが使用される場合があるため、APIキーが設定されていることを確認してください。

チャンキング戦略についての詳細は[チャンクドキュメント](/reference/rag/chunk.mdx)で説明しています。

## ステップ 2: 埋め込み生成

チャンクをお好みのプロバイダーを使って埋め込みに変換します。Mastra は OpenAI や Cohere など、多くの埋め込みプロバイダーをサポートしています。

### OpenAI を使う場合

```ts showLineNumbers copy
import { openai } from "@ai-sdk/openai";
import { embedMany } from "ai";

const { embeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: chunks.map((chunk) => chunk.text),
});
```

### Cohere を使う場合

```ts showLineNumbers copy
import { cohere } from "@ai-sdk/cohere";
import { embedMany } from "ai";

const { embeddings } = await embedMany({
  model: cohere.embedding("embed-english-v3.0"),
  values: chunks.map((chunk) => chunk.text),
});
```

埋め込み関数は、テキストの意味的な内容を表す数値の配列（ベクトル）を返します。これらはベクトルデータベースでの類似検索にすぐに利用できます。

### 埋め込み次元数の設定

埋め込みモデルは通常、固定された次元数（例: OpenAI の `text-embedding-3-small` なら 1536）のベクトルを出力します。
一部のモデルではこの次元数を減らすことができ、以下のような利点があります:

- ベクトルデータベースでの保存容量を削減できる
- 類似検索時の計算コストを抑えられる

以下はサポートされているモデルの例です:

OpenAI（text-embedding-3 モデル）:

```ts
const { embeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small", {
    dimensions: 256, // text-embedding-3 以降でのみサポート
  }),
  values: chunks.map((chunk) => chunk.text),
});
```

Google（text-embedding-004）:

```ts
const { embeddings } = await embedMany({
  model: google.textEmbeddingModel("text-embedding-004", {
    outputDimensionality: 256, // 末尾から余分な値を切り捨てます
  }),
  values: chunks.map((chunk) => chunk.text),
});
```

### ベクトルデータベースの互換性

埋め込みを保存する際は、ベクトルデータベースのインデックスが埋め込みモデルの出力サイズと一致するように設定する必要があります。次元数が一致しない場合、エラーやデータの破損が発生することがあります。

## 例：完全なパイプライン

以下は、両方のプロバイダーを使用したドキュメント処理と埋め込み生成の例です：

```ts showLineNumbers copy
import { embedMany } from "ai";
import { openai } from "@ai-sdk/openai";
import { cohere } from "@ai-sdk/cohere";

import { MDocument } from "@mastra/rag";

// Initialize document
const doc = MDocument.fromText(`
  Climate change poses significant challenges to global agriculture.
  Rising temperatures and changing precipitation patterns affect crop yields.
`);

// Create chunks
const chunks = await doc.chunk({
  strategy: "recursive",
  size: 256,
  overlap: 50,
});

// Generate embeddings with OpenAI
const { embeddings: openAIEmbeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: chunks.map((chunk) => chunk.text),
});

// OR

// Generate embeddings with Cohere
const { embeddings: cohereEmbeddings } = await embedMany({
  model: cohere.embedding("embed-english-v3.0"),
  values: chunks.map((chunk) => chunk.text),
});

// Store embeddings in your vector database
await vectorStore.upsert({
  indexName: "embeddings",
  vectors: embeddings,
});
```

##

異なるチャンキング戦略と埋め込み設定の例については、以下を参照してください：

- [チャンクサイズの調整](/reference/rag/chunk.mdx#adjust-chunk-size)
- [チャンク区切り文字の調整](/reference/rag/chunk.mdx#adjust-chunk-delimiters)
- [Cohereを使用したテキストの埋め込み](/reference/rag/embeddings.mdx#using-cohere)

ベクトルデータベースと埋め込みの詳細については、以下を参照してください：

- [ベクトルデータベース](./vector-databases.mdx)
- [埋め込みAPIリファレンス](/reference/rag/embeddings.mdx)
