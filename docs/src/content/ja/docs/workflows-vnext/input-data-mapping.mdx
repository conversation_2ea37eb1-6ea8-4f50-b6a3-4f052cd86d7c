---
title: "ワークフロー（vNext）での入力データマッピング | Mastra Docs"
description: "Mastraワークフロー（vNext）でより動的なデータフローを作成するためのワークフロー入力マッピングの使用方法を学びましょう。"
---

# 入力データマッピング

入力データマッピングにより、次のステップの入力に対する値を明示的にマッピングすることができます。これらの値は以下のようなさまざまなソースから取得できます：

- 前のステップの出力
- ランタイムコンテキスト
- 定数値
- ワークフローの初期入力

```typescript
myWorkflow
  .then(step1)
  .map({
    transformedValue: {
      step: step1,
      path: "nestedValue",
    },
    runtimeContextValue: {
      runtimeContextPath: "runtimeContextValue",
      schema: z.number(),
    },
    constantValue: {
      value: 42,
      schema: z.number(),
    },
    initDataValue: {
      initData: myWorkflow,
      path: "startValue",
    },
  })
  .then(step2)
  .commit();
```

`.map()`は、出力を入力に一致させるために役立つケースが多くあります。出力の名前を変更して入力に一致させる場合や、複雑なデータ構造や他の前のステップの出力をマッピングする場合などです。

## 出力の名前変更

入力マッピングのユースケースの1つは、出力の名前を入力に合わせて変更することです：

```typescript
import { Mastra } from "@mastra/core";
import { createWorkflow, createStep } from "@mastra/core/workflows/vNext";
import { z } from "zod";

const step1 = createStep({
  id: "step1",
  inputSchema: z.object({
    inputValue: z.string(),
  }),
  outputSchema: z.object({
    outputValue: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    mastra.getLogger()?.debug(`Step 1 received: ${inputData.inputValue}`);
    return { outputValue: `${inputData.inputValue}` };
  },
});

const step2 = createStep({
  id: "step2",
  inputSchema: z.object({
    unexpectedName: z.string(),
  }),
  outputSchema: z.string(),
  execute: async ({ inputData, mastra }) => {
    mastra.getLogger()?.debug(`Step 2 received: ${inputData.unexpectedName}`);
    return `${inputData.unexpectedName}`;
  },
});

const myWorkflow = createWorkflow({
  id: "my-workflow",
  inputSchema: z.object({
    inputValue: z.string(),
  }),
  outputSchema: z.string(),
  steps: [step1, step2],
})
  .then(step1)
  // mapping output from step1 "outputValue"
  // to input for step2 "unexpectedName"
  .map({
    unexpectedName: {
      step: step1,
      path: "outputValue",
    },
  })
  .then(step2)
  .commit();

const mastra = new Mastra({
  vnext_workflows: {
    myWorkflow,
  },
});

const run = mastra.vnext_getWorkflow("myWorkflow").createRun();
const res = await run.start({
  inputData: { inputValue: "Hello world" },
});
if (res.status === "success") {
  console.log(res.result);
}
```

## ワークフローの入力を後のステップの入力として使用する

```typescript
import { Mastra } from "@mastra/core";
import { createWorkflow, createStep } from "@mastra/core/workflows/vNext";
import { z } from "zod";

const step1 = createStep({
  id: "step1",
  inputSchema: z.object({
    inputValue: z.string(),
  }),
  outputSchema: z.object({
    outputValue: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    mastra.getLogger()?.debug(`Step 1 received: ${inputData.inputValue}`);
    return { outputValue: `Processed: ${inputData.inputValue}` };
  },
});

const step2 = createStep({
  id: "step2",
  inputSchema: z.object({
    outputValue: z.string(),
    initialValue: z.string(),
  }),
  outputSchema: z.object({
    result: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    mastra
      .getLogger()
      ?.debug(
        `Step 2 received: ${inputData.outputValue} and original: ${inputData.initialValue}`,
      );
    return {
      result: `Combined: ${inputData.outputValue} (original: ${inputData.initialValue})`,
    };
  },
});

const myWorkflow = createWorkflow({
  id: "my-workflow",
  inputSchema: z.object({
    inputValue: z.string(),
  }),
  outputSchema: z.object({
    result: z.string(),
  }),
  steps: [step1, step2],
});

myWorkflow
  .then(step1)
  .map({
    outputValue: {
      step: step1,
      path: "outputValue",
    },
    initialValue: {
      initData: myWorkflow,
      path: "inputValue",
    },
  })
  .then(step2)
  .commit();

// Create Mastra instance with all workflows
const mastra = new Mastra({
  vnext_workflows: {
    myWorkflow,
  },
});

const run = mastra.vnext_getWorkflow("myWorkflow").createRun();
const res = await run.start({
  inputData: { inputValue: "Original input" },
});
if (res.status === "success") {
  console.log("Result:", res.result);
}
```

## 前のステップの複数の出力を使用する

```typescript
import { Mastra } from "@mastra/core";
import { createWorkflow, createStep } from "@mastra/core/workflows/vNext";
import { z } from "zod";

const step1 = createStep({
  id: "step1",
  inputSchema: z.object({
    inputValue: z.string(),
  }),
  outputSchema: z.object({
    intermediateValue: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    mastra.getLogger()?.debug(`Step 1 received: ${inputData.inputValue}`);
    return { intermediateValue: `Step 1: ${inputData.inputValue}` };
  },
});

const step2 = createStep({
  id: "step2",
  inputSchema: z.object({
    intermediateValue: z.string(),
  }),
  outputSchema: z.object({
    currentResult: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    mastra
      .getLogger()
      ?.debug(`Step 2 received: ${inputData.intermediateValue}`);
    return { currentResult: `Step 2: ${inputData.intermediateValue}` };
  },
});

const step3 = createStep({
  id: "step3",
  inputSchema: z.object({
    currentResult: z.string(), // From step2
    intermediateValue: z.string(), // From step1
    initialValue: z.string(), // From workflow input
  }),
  outputSchema: z.object({
    result: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    mastra.getLogger()?.debug(`Step 3 combining all previous data`);
    return {
      result: `Combined result:
      - Initial input: ${inputData.initialValue}
      - Step 1 output: ${inputData.intermediateValue}
      - Step 2 output: ${inputData.currentResult}`,
    };
  },
});

const myWorkflow = createWorkflow({
  id: "my-workflow",
  inputSchema: z.object({
    inputValue: z.string(),
  }),
  outputSchema: z.object({
    result: z.string(),
  }),
  steps: [step1, step2, step3],
});

myWorkflow
  .then(step1)
  .then(step2)
  .map({
    // Map values from different sources to step3's inputs
    initialValue: {
      initData: myWorkflow,
      path: "inputValue",
    },
    currentResult: {
      step: step2,
      path: "currentResult",
    },
    intermediateValue: {
      step: step1,
      path: "intermediateValue",
    },
  })
  .then(step3)
  .commit();

// Create Mastra instance with all workflows
const mastra = new Mastra({
  vnext_workflows: {
    myWorkflow,
  },
});

const run = mastra.vnext_getWorkflow("myWorkflow").createRun();
const res = await run.start({
  inputData: { inputValue: "Starting data" },
});
if (res.status === "success") {
  console.log("Result:", res.result);
}
```
