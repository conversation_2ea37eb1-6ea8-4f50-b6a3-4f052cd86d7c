import YouTube from "@/components/youtube";

# ワーキングメモリ

[会話履歴](/docs/memory/overview#conversation-history)や[セマンティック検索](./semantic-recall.mdx)がエージェントが会話を記憶するのに役立つ一方、ワーキングメモリはエージェントがスレッド内の対話全体でユーザーに関する永続的な情報を維持することを可能にします。

これはエージェントのアクティブな思考やメモ帳のようなものと考えてください - ユーザーやタスクについて利用可能な状態に保つ重要な情報です。これは、人が会話中に自然に相手の名前、好み、または重要な詳細を覚えておくのと似ています。

これは、常に関連性があり、エージェントが常に利用できるべき継続的な状態を維持するのに役立ちます。

## クイックスタート

以下は、ワーキングメモリを設定したエージェントの最小限の例です：

```typescript {12-15}
import { Agent } from "@mastra/core/agent";
import { Memory } from "@mastra/memory";
import { openai } from "@ai-sdk/openai";

// Create agent with working memory enabled
const agent = new Agent({
  name: "PersonalAssistant",
  instructions: "You are a helpful personal assistant.",
  model: openai("gpt-4o"),
  memory: new Memory({
    options: {
      workingMemory: {
        enabled: true,
        use: "tool-call", // Will be the only option in a future breaking change release
      },
    },
  }),
});
```

## 仕組み

ワーキングメモリは、エージェントが継続的に関連する情報を保存するために時間の経過とともに更新できるMarkdownテキストのブロックです：

<YouTube id="ik-ld_XA96s" />

## カスタムテンプレート

テンプレートは、エージェントがワーキングメモリで追跡および更新する情報を指示します。テンプレートが提供されない場合はデフォルトのテンプレートが使用されますが、通常はエージェントの特定のユースケースに合わせたカスタムテンプレートを定義して、最も関連性の高い情報を確実に記憶させたいでしょう。

以下はカスタムテンプレートの例です。この例では、ユーザーが情報を含むメッセージを送信するとすぐに、エージェントはユーザーの名前、場所、タイムゾーンなどを保存します：

```typescript {5-28}
const memory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      template: `
# User Profile
 
## Personal Info
 
- Name:
- Location:
- Timezone:
 
## Preferences
 
- Communication Style: [e.g., Formal, Casual]
- Project Goal:
- Key Deadlines:
  - [Deadline 1]: [Date]
  - [Deadline 2]: [Date]
 
## Session State
 
- Last Task Discussed:
- Open Questions:
  - [Question 1]
  - [Question 2]
`,
    },
  },
});
```

## 効果的なテンプレートの設計

適切に構造化されたテンプレートは、エージェントが情報を解析して更新しやすくします。テンプレートを、アシスタントが最新の状態を保つべき簡潔なフォームとして扱いましょう。

- **短く、焦点を絞ったラベル。** 段落や非常に長い見出しは避けてください。ラベルは簡潔に（例えば `## Personal Info` や `- Name:`）して、更新が読みやすく、切り捨てられる可能性を減らします。
- **一貫した大文字小文字の使用。** 大文字小文字の不一致（`Timezone:` と `timezone:`）は、更新を乱雑にする可能性があります。見出しや箇条書きのラベルには、タイトルケースまたは小文字を一貫して使用してください。
- **プレースホルダーテキストはシンプルに。** `[e.g., Formal]` や `[Date]` などのヒントを使用して、LLMが正しい場所に入力できるようにします。
- **非常に長い値は省略する。** 短い形式だけが必要な場合は、`- Name: [First name or nickname]` や `- Address (short):` のようなガイダンスを含めて、完全な法的テキストではなく簡潔にします。
- **更新ルールは `instructions` で言及する。** テンプレートの一部をいつどのように埋めるか、またはクリアするかの指示を、エージェントの `instructions` フィールドに直接記述できます。

### 代替テンプレートスタイル

少数の項目だけが必要な場合は、より短い単一ブロックを使用します：

```typescript
const basicMemory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      template: `User Facts:\n- Name:\n- Favorite Color:\n- Current Topic:`,
    },
  },
});
```

より物語的なスタイルを好む場合は、重要な事実を短い段落形式で保存することもできます：

```typescript
const paragraphMemory = new Memory({
  options: {
    workingMemory: {
      enabled: true,
      template: `Important Details:\n\nKeep a short paragraph capturing the user's important facts (name, main goal, current task).`,
    },
  },
});
```

## 例：複数ステップの保持

以下は、短いユーザー会話を通じて`User Profile`テンプレートがどのように更新されるかを簡略化して示したものです：

```nohighlight
# User Profile

## Personal Info

- Name:
- Location:
- Timezone:

--- ユーザーが「私の名前は**Sam**で、**ベルリン**出身です」と言った後 ---

# User Profile
- Name: Sam
- Location: Berlin
- Timezone:

--- ユーザーが「ちなみに普段は**CET**にいます」と追加した後 ---

# User Profile
- Name: Sam
- Location: Berlin
- Timezone: CET
```

エージェントは情報を再度要求することなく、後の応答で`Sam`や`Berlin`を参照できるようになります。これは、その情報がワーキングメモリに保存されているためです。

エージェントが期待通りにワーキングメモリを更新していない場合は、エージェントの`instructions`設定でこのテンプレートを*どのように*、*いつ*使用するかについてのシステム指示を追加することができます。

## 例

- [ストリーミングワーキングメモリ](/examples/memory/streaming-working-memory)
- [ワーキングメモリテンプレートの使用](/examples/memory/streaming-working-memory-advanced)
