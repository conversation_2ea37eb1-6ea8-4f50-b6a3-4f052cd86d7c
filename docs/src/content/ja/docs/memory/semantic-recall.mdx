# セマンティック リコール

友人に先週末何をしたか尋ねると、彼らは「先週末」に関連する出来事を記憶の中から検索し、それから何をしたかを教えてくれます。これはMastraにおけるセマンティックリコールの仕組みに少し似ています。

## セマンティックリコールの仕組み

セマンティックリコールはRAGベースの検索であり、メッセージが[最近の会話履歴](./overview.mdx#conversation-history)に含まれなくなった場合でも、エージェントが長期間の対話にわたってコンテキストを維持するのに役立ちます。

メッセージのベクトル埋め込みを使用して類似性検索を行い、さまざまなベクトルストアと統合し、取得されたメッセージの周囲のコンテキストウィンドウを設定可能です。

<br />
<img
  src="/image/semantic-recall.png"
  alt="Mastra Memoryのセマンティックリコールを示す図"
  width={800}
/>

有効にすると、新しいメッセージを使用してベクトルDBに意味的に類似したメッセージを問い合わせます。

LLMからの応答を受け取った後、すべての新しいメッセージ（ユーザー、アシスタント、ツールコール/結果）がベクトルDBに挿入され、後の対話で呼び出されるようになります。

## クイックスタート

セマンティックリコールはデフォルトで有効になっているため、エージェントにメモリを与えると、それが含まれます：

```typescript {9}
import { Agent } from "@mastra/core/agent";
import { Memory } from "@mastra/memory";
import { openai } from "@ai-sdk/openai";

const agent = new Agent({
  name: "SupportAgent",
  instructions: "You are a helpful support agent.",
  model: openai("gpt-4o"),
  memory: new Memory(),
});
```

## リコール設定

セマンティックリコールの動作を制御する主な2つのパラメータは次のとおりです：

1. **topK**: 意味的に類似したメッセージを何件取得するか
2. **messageRange**: 各一致に対してどれだけの周囲のコンテキストを含めるか

```typescript {5-6}
const agent = new Agent({
  memory: new Memory({
    options: {
      semanticRecall: {
        topK: 3, // 最も類似した3つのメッセージを取得
        messageRange: 2, // 各一致の前後2つのメッセージを含める
      },
    },
  }),
});
```

### ストレージ設定

セマンティックリコールは、メッセージとそれらの埋め込みを保存するために[ストレージとベクトルDB](/reference/memory/Memory#parameters)に依存しています。

```ts {8-17}
import { Memory } from "@mastra/memory";
import { Agent } from "@mastra/core/agent";
import { LibSQLStore, LibSQLVector } from "@mastra/libsql";

const agent = new Agent({
  memory: new Memory({
    // これは省略した場合のデフォルトストレージDBです
    storage: new LibSQLStore({
      url: "file:./local.db",
    }),
    // これは省略した場合のデフォルトベクトルDBです
    vector: new LibSQLVector({
      connectionUrl: "file:./local.db",
    }),
  }),
});
```

**ストレージ/ベクトルのコード例**:

- [LibSQL](/examples/memory/memory-with-libsql)
- [Postgres](/examples/memory/memory-with-pg)
- [Upstash](/examples/memory/memory-with-upstash)

### エンベッダー設定

セマンティックリコールは、メッセージを埋め込みに変換するために[埋め込みモデル](/reference/memory/Memory#embedder)に依存しています。AI SDKと互換性のある任意の[埋め込みモデル](https://sdk.vercel.ai/docs/ai-sdk-core/embeddings)を指定できます。

FastEmbed（ローカル埋め込みモデル）を使用するには、`@mastra/fastembed`をインストールします：

```bash npm2yarn copy
npm install @mastra/fastembed
```

そして、メモリで次のように設定します：

```ts {3,8}
import { Memory } from "@mastra/memory";
import { Agent } from "@mastra/core/agent";
import { fastembed } from "@mastra/fastembed";

const agent = new Agent({
  memory: new Memory({
    // ... その他のメモリオプション
    embedder: fastembed,
  }),
});
```

あるいは、OpenAIのような別のプロバイダーを使用することもできます：

```ts {3,8}
import { Memory } from "@mastra/memory";
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

const agent = new Agent({
  memory: new Memory({
    // ... その他のメモリオプション
    embedder: openai.embedding("text-embedding-3-small"),
  }),
});
```

### 無効化

セマンティックリコールを使用するとパフォーマンスへの影響があります。新しいメッセージは埋め込みに変換され、LLMに送信される前にベクトルデータベースへのクエリに使用されます。

セマンティックリコールはデフォルトで有効になっていますが、必要ない場合は無効にできます：

```typescript {4}
const agent = new Agent({
  memory: new Memory({
    options: {
      semanticRecall: false,
    },
  }),
});
```

以下のようなシナリオでセマンティックリコールを無効にすることを検討するかもしれません：

- 会話履歴が現在の会話に十分なコンテキストを提供している場合。
- リアルタイムの双方向音声のようなパフォーマンスに敏感なアプリケーションで、埋め込みの作成とベクトルクエリの実行による追加の遅延が目立つ場合。
