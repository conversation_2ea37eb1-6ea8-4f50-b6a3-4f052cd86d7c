# メモリプロセッサ

メモリプロセッサを使用すると、メモリから取得されたメッセージのリストを、エージェントのコンテキストウィンドウに追加されLLMに送信される*前に*変更することができます。これはコンテキストサイズの管理、コンテンツのフィルタリング、パフォーマンスの最適化に役立ちます。

プロセッサは、メモリ設定（例：`lastMessages`、`semanticRecall`）に基づいて取得されたメッセージに対して動作します。新しく入ってくるユーザーメッセージには**影響しません**。

## 組み込みプロセッサ

Mastra には組み込みプロセッサが用意されています。

### `TokenLimiter`

このプロセッサは、LLM のコンテキストウィンドウの上限を超えることによるエラーを防ぐために使用されます。取得したメモリメッセージ内のトークン数をカウントし、合計が指定された `limit` 未満になるまで最も古いメッセージを削除します。

```typescript copy showLineNumbers {9-12}
import { Memory } from "@mastra/memory";
import { TokenLimiter } from "@mastra/memory/processors";
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

const agent = new Agent({
  model: openai("gpt-4o"),
  memory: new Memory({
    processors: [
      // Ensure the total tokens from memory don't exceed ~127k
      new TokenLimiter(127000),
    ],
  }),
});
```

`TokenLimiter` はデフォルトで `o200k_base` エンコーディング（GPT-4o に適しています）を使用します。必要に応じて、他のモデル向けに別のエンコーディングを指定することもできます。

```typescript copy showLineNumbers {6-9}
// Import the encoding you need (e.g., for older OpenAI models)
import cl100k_base from "js-tiktoken/ranks/cl100k_base";

const memoryForOlderModel = new Memory({
  processors: [
    new TokenLimiter({
      limit: 16000, // Example limit for a 16k context model
      encoding: cl100k_base,
    }),
  ],
});
```

エンコーディングの詳細については、[OpenAI cookbook](https://cookbook.openai.com/examples/how_to_count_tokens_with_tiktoken#encodings) や [`js-tiktoken` リポジトリ](https://github.com/dqbd/tiktoken) を参照してください。

### `ToolCallFilter`

このプロセッサは、LLM に送信されるメモリメッセージからツールコールを削除します。これにより、コンテキストから冗長になりがちなツールのやり取りを除外してトークンを節約できます。今後のやり取りで詳細が不要な場合に便利です。また、常に特定のツールを再度呼び出したい場合や、メモリ内の以前のツール結果に依存させたくない場合にも有用です。

```typescript copy showLineNumbers {5-14}
import { Memory } from "@mastra/memory";
import { ToolCallFilter, TokenLimiter } from "@mastra/memory/processors";

const memoryFilteringTools = new Memory({
  processors: [
    // Example 1: Remove all tool calls/results
    new ToolCallFilter(),

    // Example 2: Remove only noisy image generation tool calls/results
    new ToolCallFilter({ exclude: ["generateImageTool"] }),

    // Always place TokenLimiter last
    new TokenLimiter(127000),
  ],
});
```

## 複数のプロセッサの適用

複数のプロセッサを連結して使用することができます。これらは `processors` 配列に記載された順番で実行されます。あるプロセッサの出力が、次のプロセッサの入力となります。

**順序は重要です！** 一般的には、`TokenLimiter` をチェーンの**最後**に配置するのがベストプラクティスです。これにより、他のフィルタリングが行われた後の最終的なメッセージセットに対して動作し、最も正確なトークン制限の適用が可能になります。

```typescript copy showLineNumbers {7-14}
import { Memory } from "@mastra/memory";
import { ToolCallFilter, TokenLimiter } from "@mastra/memory/processors";
// Assume a hypothetical 'PIIFilter' custom processor exists
// import { PIIFilter } from './custom-processors';

const memoryWithMultipleProcessors = new Memory({
  processors: [
    // 1. Filter specific tool calls first
    new ToolCallFilter({ exclude: ["verboseDebugTool"] }),
    // 2. Apply custom filtering (e.g., remove hypothetical PII - use with caution)
    // new PIIFilter(),
    // 3. Apply token limiting as the final step
    new TokenLimiter(127000),
  ],
});
```

## カスタムプロセッサの作成

`MemoryProcessor` 基底クラスを拡張することで、カスタムロジックを作成できます。

```typescript copy showLineNumbers {4-19,23-26}
import { Memory, CoreMessage } from "@mastra/memory";
import { MemoryProcessor, MemoryProcessorOpts } from "@mastra/core/memory";

class ConversationOnlyFilter extends MemoryProcessor {
  constructor() {
    // Provide a name for easier debugging if needed
    super({ name: "ConversationOnlyFilter" });
  }

  process(
    messages: CoreMessage[],
    _opts: MemoryProcessorOpts = {}, // Options passed during memory retrieval, rarely needed here
  ): CoreMessage[] {
    // Filter messages based on role
    return messages.filter(
      (msg) => msg.role === "user" || msg.role === "assistant",
    );
  }
}

// Use the custom processor
const memoryWithCustomFilter = new Memory({
  processors: [
    new ConversationOnlyFilter(),
    new TokenLimiter(127000), // Still apply token limiting
  ],
});
```

カスタムプロセッサを作成する際は、入力として渡される `messages` 配列やそのオブジェクトを直接変更しないようにしてください。
