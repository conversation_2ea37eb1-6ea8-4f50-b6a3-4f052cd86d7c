---
title: "ライセンス"
description: "Mastraライセンス"
---

# ライセンス

## Elastic License 2.0 (ELv2)

Mastraは、オープンソースの原則と持続可能なビジネス慣行のバランスを取るために設計された現代的なライセンスであるElastic License 2.0（ELv2）の下でライセンスされています。

### Elastic License 2.0とは？

Elastic License 2.0は、ソースアベイラブルライセンスであり、プロジェクトの持続可能性を保護するための特定の制限を含みながら、ユーザーにソフトウェアの使用、修正、配布に関する広範な権利を付与します。以下が許可されています：

- ほとんどの目的での無料使用
- ソースコードの閲覧、修正、再配布
- 派生作品の作成と配布
- 組織内での商用利用

主な制限は、ユーザーにソフトウェアの実質的な機能へのアクセスを提供するホスト型または管理型サービスとしてMastraを提供することはできないということです。

### なぜElastic License 2.0を選んだのか

私たちがElastic License 2.0を選んだ重要な理由はいくつかあります：

1. **持続可能性**：オープン性と長期的な開発を維持する能力とのバランスを健全に保つことができます。

2. **イノベーション保護**：私たちの作業が競合するサービスとして再パッケージ化されることを懸念せずに、イノベーションへの投資を継続できることを保証します。

3. **コミュニティ重視**：コミュニティをサポートする能力を保護しながら、ユーザーが私たちのコードを閲覧、修正、学習することを可能にすることで、オープンソースの精神を維持します。

4. **ビジネスの明確性**：Mastraが商業的な文脈でどのように使用できるかについての明確なガイドラインを提供します。

### Mastraでビジネスを構築する

ライセンスの制限にもかかわらず、Mastraを使用して成功するビジネスを構築する方法は数多くあります：

#### 許可されているビジネスモデル

- **アプリケーションの構築**：Mastraで構築されたアプリケーションを作成して販売する
- **コンサルティングサービスの提供**：専門知識、実装、カスタマイズサービスを提供する
- **カスタムソリューションの開発**：Mastraを使用してクライアント向けのカスタムAIソリューションを構築する
- **アドオンと拡張機能の作成**：Mastraの機能を拡張する補完的なツールを開発して販売する
- **トレーニングと教育**：Mastraを効果的に使用するためのコースや教育資料を提供する

#### 準拠した使用例

- 企業がMastraを使用してAI駆動のカスタマーサービスアプリケーションを構築し、クライアントに販売する
- コンサルティング会社がMastraの実装とカスタマイズサービスを提供する
- 開発者がMastraで特殊なエージェントとツールを作成し、他のビジネスにライセンス供与する
- スタートアップがMastraを活用した特定の業界向けソリューション（例：ヘルスケアAIアシスタント）を構築する

#### 避けるべきこと

主な制限は、ユーザーがその中核機能にアクセスできるホスト型サービスとしてMastra自体を提供することはできないということです。これは以下を意味します：

- 最小限の修正を加えただけの実質的にMastraであるSaaSプラットフォームを作成しないでください
- 顧客が主にMastraの機能を使用するために支払う管理型Mastraサービスを提供しないでください

### ライセンスに関する質問がありますか？

Elastic License 2.0があなたのユースケースにどのように適用されるかについて具体的な質問がある場合は、明確化のために[Discordでお問い合わせください](https://discord.gg/BTYqqHKUrf)。私たちは、プロジェクトの持続可能性を保護しながら、正当なビジネスユースケースをサポートすることに取り組んでいます。
