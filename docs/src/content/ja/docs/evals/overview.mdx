---
title: "概要"
description: "Mastra evalsを使用してAIエージェントの品質を評価および測定する方法を理解する。"
---

# エバリュエーションによるエージェントのテスト

従来のソフトウェアテストには明確な合格/不合格の条件がありますが、AI出力は非決定的です — 同じ入力でも結果が異なる場合があります。エバリュエーション（評価）は、エージェントの品質を測定するための定量的な指標を提供することで、このギャップを埋めるのに役立ちます。

エバリュエーションは、モデル評価、ルールベース、および統計的手法を使用してエージェントの出力を評価する自動テストです。各エバリュエーションは0〜1の間の正規化されたスコアを返し、記録して比較することができます。エバリュエーションは独自のプロンプトやスコアリング関数でカスタマイズすることができます。

エバリュエーションはクラウドで実行でき、リアルタイムの結果を取得できます。また、CI/CDパイプラインの一部としてエバリュエーションを実行することで、時間の経過とともにエージェントをテストおよび監視することも可能です。

## 評価の種類

評価にはさまざまな種類があり、それぞれ特定の目的を果たします。以下は一般的な種類です：

1. **テキスト評価**: エージェントの応答の正確性、信頼性、文脈理解を評価
2. **分類評価**: 定義済みのカテゴリに基づいてデータを分類する際の正確性を測定
3. **ツール使用評価**: エージェントが外部ツールやAPIをどれだけ効果的に使用するかを評価
4. **プロンプトエンジニアリング評価**: 異なる指示や入力形式の影響を探る

## はじめに

Evalsはエージェントに追加する必要があります。以下は要約、コンテンツの類似性、およびトーンの一貫性のメトリクスを使用した例です：

```typescript copy showLineNumbers filename="src/mastra/agents/index.ts"
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { SummarizationMetric } from "@mastra/evals/llm";
import {
  ContentSimilarityMetric,
  ToneConsistencyMetric,
} from "@mastra/evals/nlp";

const model = openai("gpt-4o");

export const myAgent = new Agent({
  name: "ContentWriter",
  instructions: "You are a content writer that creates accurate summaries",
  model,
  evals: {
    summarization: new SummarizationMetric(model),
    contentSimilarity: new ContentSimilarityMetric(),
    tone: new ToneConsistencyMetric(),
  },
});
```

`mastra dev`を使用すると、Mastraダッシュボードでeval結果を表示できます。

## 自動テストを超えて

自動評価は価値がありますが、高パフォーマンスのAIチームはしばしばそれらを以下と組み合わせています：

1. **A/Bテスト**：実際のユーザーで異なるバージョンを比較
2. **人間によるレビュー**：本番データとトレースの定期的なレビュー
3. **継続的なモニタリング**：時間の経過とともに評価指標を追跡し、性能低下を検出

## 評価結果の理解

各評価指標はエージェントの出力の特定の側面を測定します。結果の解釈と改善方法は以下の通りです：

### スコアの理解

どの指標についても：

1. 指標のドキュメントを確認して、採点プロセスを理解する
2. スコアが変化するパターンを探す
3. 異なる入力やコンテキスト間でスコアを比較する
4. 時間の経過に伴う変化を追跡して傾向を把握する

### 結果の改善

スコアが目標に達していない場合：

1. 指示を確認する - 明確ですか？より具体的にしてみましょう
2. コンテキストを確認する - エージェントに必要な情報を提供していますか？
3. プロンプトを簡素化する - 複雑なタスクを小さなステップに分ける
4. ガードレールを追加する - 難しいケースに対する具体的なルールを含める

### 品質の維持

目標を達成したら：

1. 安定性を監視する - スコアは一貫していますか？
2. うまくいったことを文書化する - 成功したアプローチについてメモを残す
3. エッジケースをテストする - 珍しいシナリオをカバーする例を追加する
4. 微調整する - 効率を向上させる方法を探す

評価ができることの詳細については、[テキスト評価](/docs/evals/textual-evals)を参照してください。

独自の評価を作成する方法の詳細については、[カスタム評価](/docs/evals/custom-eval)ガイドを参照してください。

CIパイプラインで評価を実行する方法については、[CIでの実行](/docs/evals/running-in-ci)ガイドを参照してください。
