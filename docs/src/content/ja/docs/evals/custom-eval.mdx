---
title: "独自のEvalを作成する"
description: "Mastraを使用すると、独自のevalを作成できます。方法はこちらです。"
---

# 独自のEvalを作成する

独自のevalを作成することは、新しい関数を作成するのと同じくらい簡単です。単に`Metric`クラスを拡張するクラスを作成し、`measure`メソッドを実装します。

## 基本的な例

出力に特定の単語が含まれているかを確認するカスタムメトリックを作成する簡単な例については、[Word Inclusion example](/examples/evals/word-inclusion)をご覧ください。

## カスタム LLM-Judge の作成

カスタム LLM ジャッジは、AI の応答の特定の側面を評価するのに役立ちます。特定のユースケースに対する専門家のレビュアーがいるようなものです：

- 医療 Q&A → ジャッジは医療の正確性と安全性をチェック
- カスタマーサービス → ジャッジはトーンと有用性を評価
- コード生成 → ジャッジはコードの正確性とスタイルを確認

実用的な例として、[Chef Michel's](/docs/guides/chef-michel) のレシピを [Gluten Checker example](/examples/evals/custom-eval) でグルテン含有量を評価する方法をご覧ください。
