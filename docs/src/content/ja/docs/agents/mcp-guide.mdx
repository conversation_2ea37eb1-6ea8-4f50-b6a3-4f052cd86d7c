---
title: "MCPをMastraで使用する | エージェント | Mastraドキュメント"
description: "MastraでMCPを使用して、AIエージェントにサードパーティのツールやリソースを統合します。"
---

# Mastraでのモデルコンテキストプロトコル（MCP）の使用

[モデルコンテキストプロトコル（MCP）](https://modelcontextprotocol.io/introduction)は、AIモデルが外部ツールやリソースを発見し、相互作用するための標準化された方法です。

## 概要

MastraのMCPは、ツールサーバーに接続するための標準化された方法を提供します。StdioとHTTP（Streamable HTTPをサポートし、SSEにフォールバック）をサポートしています。

## インストール

pnpmを使用する場合：

```bash
pnpm add @mastra/mcp@latest
```

npmを使用する場合：

```bash copy
npm install @mastra/mcp@latest
```

## コード内でMCPを使用する

`MCPClient`クラスは、複数のMCPクライアントを管理することなく、Mastraアプリケーションで複数のツールサーバーを管理する方法を提供します。`MCPConfiguration`内部で使用される`MastraMCPClient`は、サーバー設定に基づいてトランスポートタイプを自動的に検出するようになりました：

- `command`を提供する場合、Stdioトランスポートを使用します。
- `url`を提供する場合、最初にStreamable HTTPトランスポートを試み、初期接続が失敗した場合はレガシーSSEトランスポートにフォールバックします。

以下は設定例です：

```typescript
import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

const mcp = new MCPClient({
  servers: {
    // Stdio例
    sequential: {
      command: "npx",
      args: ["-y", "@modelcontextprotocol/server-sequential-thinking"],
    },
    // HTTP例（最初にStreamable HTTPを試み、その後SSEにフォールバック）
    weather: {
      url: new URL("http://localhost:8080/mcp"), // Streamable HTTPのベースURLを使用
      requestInit: {
        headers: {
          Authorization: "Bearer your-token",
        },
      },
    },
  },
});
```

### ツールとツールセット

`MCPClient`クラスはMCPツールにアクセスするための2つの方法を提供しており、それぞれ異なるユースケースに適しています：

#### ツールの使用（`getTools()`）

以下の場合にこのアプローチを使用します：

- 単一のMCP接続がある場合
- ツールが単一のユーザー/コンテキストによって使用される場合
- ツール設定（APIキー、認証情報）が一定の場合
- 固定されたツールセットでAgentを初期化したい場合

```typescript
const agent = new Agent({
  name: "CLI Assistant",
  instructions: "You help users with CLI tasks",
  model: openai("gpt-4o-mini"),
  tools: await mcp.getTools(), // ツールはエージェント作成時に固定される
});
```

#### ツールセットの使用（`getToolsets()`）

以下の場合にこのアプローチを使用します：

- リクエストごとのツール設定が必要な場合
- ツールがユーザーごとに異なる認証情報を必要とする場合
- マルチユーザー環境（Webアプリ、APIなど）で実行する場合
- ツール設定を動的に変更する必要がある場合

```typescript
const mcp = new MCPClient({
  servers: {
    example: {
      command: "npx",
      args: ["-y", "@example/fakemcp"],
      env: {
        API_KEY: "your-api-key",
      },
    },
  },
});

// このユーザー用に設定された現在のツールセットを取得
const toolsets = await mcp.getToolsets();

// ユーザー固有のツール設定でエージェントを使用
const response = await agent.stream(
  "Mastraの新機能は何ですか？また、天気はどうですか？",
  {
    toolsets,
  },
);
```

## MCP レジストリ

MCPサーバーは、キュレーションされたツールコレクションを提供するレジストリを通じてアクセスできます。
私たちは、最適なMCPサーバーの調達先を見つけるのに役立つ[MCP レジストリ レジストリ](https://mastra.ai/mcp-registry-registry)をキュレーションしていますが、以下では私たちのお気に入りのいくつかからツールを使用する方法を説明します：

### mcp.run レジストリ

[mcp.run](https://www.mcp.run/)を使用すると、事前認証された安全なMCPサーバーを簡単に呼び出すことができます。mcp.runのツールは無料で、完全に管理されているため、エージェントはSSE URLだけを必要とし、ユーザーがインストールしたどのツールでも使用できます。MCPサーバーは[プロファイル](https://docs.mcp.run/user-guide/manage-profiles)にグループ化され、固有のSSE URLでアクセスされます。

各プロファイルについて、署名付きの固有のURLをコピー/ペーストして、次のように`MCPClient`に入力できます：

```typescript
const mcp = new MCPClient({
  servers: {
    marketing: {
      url: new URL(process.env.MCP_RUN_SSE_URL!),
    },
  },
});
```

> 重要：[mcp.run](https://mcp.run)の各SSE URLには、パスワードのように扱うべき固有の署名が含まれています。SSE URLを環境変数として読み込み、アプリケーションコードの外部で管理するのがベストです。

```bash filename=".env" copy
MCP_RUN_SSE_URL=https://www.mcp.run/api/mcp/sse?nonce=...
```

### Composio.dev レジストリ

[Composio.dev](https://composio.dev)は、Mastraと簡単に統合できる[SSEベースのMCPサーバー](https://mcp.composio.dev)のレジストリを提供しています。Cursor用に生成されるSSE URLはMastraと互換性があり、設定で直接使用できます：

```typescript
const mcp = new MCPClient({
  servers: {
    googleSheets: {
      url: new URL("https://mcp.composio.dev/googlesheets/[private-url-path]"),
    },
    gmail: {
      url: new URL("https://mcp.composio.dev/gmail/[private-url-path]"),
    },
  },
});
```

Composio提供のツールを使用する場合、エージェントとの会話を通じて直接サービス（Google SheetsやGmailなど）で認証できます。ツールには認証機能が含まれており、チャット中にプロセスをガイドします。

注意：Composio.devの統合は、SSE URLがあなたのアカウントに紐づけられており、複数のユーザーには使用できないため、個人的な自動化などの単一ユーザーシナリオに最適です。各URLは単一アカウントの認証コンテキストを表します。

### Smithery.ai レジストリ

[Smithery.ai](https://smithery.ai)はMastraで簡単に使用できるMCPサーバーのレジストリを提供しています：

```typescript
// Unix/Mac
const mcp = new MCPClient({
  servers: {
    sequentialThinking: {
      command: "npx",
      args: [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@smithery-ai/server-sequential-thinking",
        "--config",
        "{}",
      ],
    },
  },
});

// Windows
const mcp = new MCPClient({
  servers: {
    sequentialThinking: {
      command: "cmd",
      args: [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@smithery-ai/server-sequential-thinking",
        "--config",
        "{}",
      ],
    },
  },
});
```

この例は、Smitheryドキュメントのクロード統合例から適応されています。

## MCPサーバーでツールを除外する

MCPサーバーからのツールは、`await mcp.getTools()`または`await mcp.getToolsets()`を呼び出すと、プレーンなJavaScriptオブジェクトとして返されます。これにより、エージェントに渡す前にツールを簡単にフィルタリングしたり除外したりすることができます。

### ツール除外の例

MCPサーバーが3つのツール（`weather`、`stockPrice`、`news`）を公開しているが、エージェントから`news`ツールを除外したい場合を考えてみましょう。ツールオブジェクトをフィルタリングすることでこれを実現できます：

```typescript
import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

const mcp = new MCPClient({
  servers: {
    myServer: {
      command: "npx",
      args: ["tsx", "my-mcp-server.ts"],
    },
  },
});

// MCPサーバーからすべてのツールを取得
const allTools = await mcp.getTools();

// 'news'ツールを除外（ツール名は'serverName_toolName'の形式で名前空間が設定されています）
const filteredTools = Object.fromEntries(
  Object.entries(allTools).filter(([toolName]) => toolName !== "myServer_news"),
);

// フィルタリングされたツールのみをエージェントに渡す
const agent = new Agent({
  name: "Selective Agent",
  instructions: "You can access only selected tools.",
  model: openai("gpt-4"),
  tools: filteredTools,
});
```

`.filter()`関数内で任意のロジックを使用して、名前、タイプ、またはその他のプロパティによってツールを除外できます。このアプローチは、リクエストごとにツールセットを動的にフィルタリングしたい場合の`getToolsets()`でも同様に機能します。

もう一つのアプローチは、使用したいツールを分割代入するか、使用したくないツールを除外することです。

```typescript
// 分割代入したオブジェクトから不要なツールを削除
const { myServer_news, ...filteredTools } = await mcp.getTools();

// フィルタリングされたツールのみをエージェントに渡す
const agent = new Agent({
  name: "Selective Agent",
  instructions: "You can access only selected tools.",
  model: openai("gpt-4"),
  tools: filteredTools,
});
```

```typescript
// 分割代入したオブジェクトから必要なツールを選択
const { myServer_weather, myServer_stockPrice } = await mcp.getTools();

// 選択したツールのみをエージェントに渡す
const agent = new Agent({
  name: "Selective Agent",
  instructions: "You can access only selected tools.",
  model: openai("gpt-4"),
  tools: { myServer_weather, myServer_stockPrice },
});
```

この例は、Smitheryドキュメントのクロード統合例から適応されています。

```typescript
// Unix/Mac
const mcp = new MCPConfiguration({
  servers: {
    sequentialThinking: {
      command: "npx",
      args: [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@smithery-ai/server-sequential-thinking",
        "--config",
        "{}",
      ],
    },
  },
});

// Windows
const mcp = new MCPConfiguration({
  servers: {
    sequentialThinking: {
      command: "cmd",
      args: [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@smithery-ai/server-sequential-thinking",
        "--config",
        "{}",
      ],
    },
  },
});
```

この例は、Smitheryドキュメントのクロード統合例から適応されています。
