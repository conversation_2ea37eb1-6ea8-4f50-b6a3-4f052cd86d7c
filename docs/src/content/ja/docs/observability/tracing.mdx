---
title: "トレーシング | Mastra 可観測性ドキュメント"
description: "Mastraアプリケーション用のOpenTelemetryトレーシングをセットアップする"
---

import Image from "next/image";

# トレーシング

MastraはアプリケーションのトレーシングとモニタリングのためにOpenTelemetryプロトコル（OTLP）をサポートしています。テレメトリが有効になっている場合、Mastraはエージェント操作、LLM連携、ツール実行、インテグレーション呼び出し、ワークフロー実行、データベース操作などのすべてのコアプリミティブを自動的にトレースします。テレメトリデータは任意のOTELコレクターにエクスポートできます。

### 基本設定

テレメトリを有効にする簡単な例を示します：

```ts filename="mastra.config.ts" showLineNumbers copy
export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "my-app",
    enabled: true,
    sampling: {
      type: "always_on",
    },
    export: {
      type: "otlp",
      endpoint: "http://localhost:4318", // SigNoz local endpoint
    },
  },
});
```

### 設定オプション

テレメトリ設定は以下のプロパティを受け付けます：

```ts
type OtelConfig = {
  // トレースでサービスを識別する名前（オプション）
  serviceName?: string;

  // テレメトリの有効/無効（デフォルトはtrue）
  enabled?: boolean;

  // サンプリングするトレースの数を制御
  sampling?: {
    type: "ratio" | "always_on" | "always_off" | "parent_based";
    probability?: number; // 比率サンプリング用
    root?: {
      probability: number; // 親ベースのサンプリング用
    };
  };

  // テレメトリデータの送信先
  export?: {
    type: "otlp" | "console";
    endpoint?: string;
    headers?: Record<string, string>;
  };
};
```

詳細については[OtelConfig リファレンスドキュメント](../../reference/observability/otel-config.mdx)を参照してください。

### 環境変数

環境変数を通じてOTLPエンドポイントとヘッダーを設定できます：

```env filename=".env" copy
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
OTEL_EXPORTER_OTLP_HEADERS=x-api-key=your-api-key
```

そして設定では：

```ts filename="mastra.config.ts" showLineNumbers copy
export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "my-app",
    enabled: true,
    export: {
      type: "otlp",
      // エンドポイントとヘッダーは環境変数から取得されます
    },
  },
});
```

### 例：SigNoz連携

[SigNoz](https://signoz.io)でのトレースされたエージェントインタラクションの例です：

<img
  src="/image/signoz-telemetry-demo.png"
  alt="スパン、LLM呼び出し、ツール実行を示すエージェントインタラクショントレース"
  style={{ maxWidth: "800px", width: "100%", margin: "8px 0" }}
  className="nextra-image rounded-md"
  data-zoom
  width={800}
  height={400}
/>

### その他のサポートされているプロバイダー

サポートされている監視プロバイダーの完全なリストとその設定の詳細については、[監視プロバイダーリファレンス](../../reference/observability/providers/)を参照してください。

### Next.js固有のトレーシング手順

Next.jsを使用している場合は、3つの追加設定手順があります：

1. `next.config.ts`で計測フックを有効にする
2. Mastraテレメトリ設定を構成する
3. OpenTelemetryエクスポーターをセットアップする

実装の詳細については、[Next.jsトレーシング](./nextjs-tracing)ガイドを参照してください。
