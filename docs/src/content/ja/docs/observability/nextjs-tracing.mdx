---
title: "Next.js トレーシング | Mastra Observability ドキュメント"
description: "Next.js アプリケーションのための OpenTelemetry トレーシングの設定"
---

# Next.js トレーシング

Next.js で OpenTelemetry トレーシングを有効にするには、追加の設定が必要です。

### ステップ 1: Next.js の設定

まず、Next.js の設定ファイルでインストゥルメンテーションフックを有効にします。

```ts filename="next.config.ts" showLineNumbers copy
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    instrumentationHook: true, // Not required in Next.js 15+
  },
};

export default nextConfig;
```

### ステップ 2: Mastra の設定

Mastra インスタンスを設定します。

```typescript filename="mastra.config.ts" copy
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  // ... other config
  telemetry: {
    serviceName: "your-project-name",
    enabled: true,
  },
});
```

### ステップ 3: プロバイダーの設定

Next.js を使用している場合、OpenTelemetry インストゥルメンテーションのセットアップ方法は2つあります。

#### オプション 1: カスタムエクスポーターの使用

すべてのプロバイダーで動作するデフォルトの方法は、カスタムエクスポーターを設定することです。

1. 必要な依存関係をインストールします（例: Langfuse を使用）。

```bash copy
npm install @opentelemetry/api langfuse-vercel
```

2. インストゥルメンテーションファイルを作成します。

```ts filename="instrumentation.ts" copy
import {
  NodeSDK,
  ATTR_SERVICE_NAME,
  resourceFromAttributes,
} from "@mastra/core/telemetry/otel-vendor";
import { LangfuseExporter } from "langfuse-vercel";

export function register() {
  const exporter = new LangfuseExporter({
    // ... Langfuse config
  });

  const sdk = new NodeSDK({
    resource: resourceFromAttributes({
      [ATTR_SERVICE_NAME]: "ai",
    }),
    traceExporter: exporter,
  });

  sdk.start();
}
```

#### オプション 2: Vercel の Otel セットアップの使用

Vercel へデプロイする場合は、Vercel の OpenTelemetry セットアップを利用できます。

1. 必要な依存関係をインストールします。

```bash copy
npm install @opentelemetry/api @vercel/otel
```

2. プロジェクトのルート（または src フォルダ）にインストゥルメンテーションファイルを作成します。

```ts filename="instrumentation.ts" copy
import { registerOTel } from "@vercel/otel";

export function register() {
  registerOTel({ serviceName: "your-project-name" });
}
```

### まとめ

このセットアップにより、Next.js アプリケーションおよび Mastra の操作で OpenTelemetry トレーシングが有効になります。

詳細については、以下のドキュメントをご覧ください。

- [Next.js Instrumentation](https://nextjs.org/docs/app/building-your-application/optimizing/instrumentation)
- [Vercel OpenTelemetry](https://vercel.com/docs/observability/otel-overview/quickstart)
