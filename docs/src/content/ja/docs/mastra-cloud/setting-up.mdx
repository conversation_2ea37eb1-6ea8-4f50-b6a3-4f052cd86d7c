---
title: プロジェクトのセットアップ
description: Mastra Cloudプロジェクトの設定手順
---

# Mastra Cloudプロジェクトの設定

> **ベータ版のお知らせ**
> Mastra Cloudは現在**ベータ版**です。

このページでは、GitHub連携を使用してMastra Cloudでプロジェクトを設定する手順について説明します。

## 前提条件

- Mastra Cloudアカウント
- GitHubアカウント
- Mastraアプリケーションを含むGitHubリポジトリ

## プロジェクト作成プロセス

1. **Mastra Cloudにサインイン**

   - Mastra Cloudダッシュボード（https://cloud.mastra.ai）に移動します
   - アカウント認証情報でサインインします

2. **新しいプロジェクトを追加**

   - 「すべてのプロジェクト」ビューから、右上の「新規追加」ボタンをクリックします
   - GitHubリポジトリのインポートダイアログが開きます

   ![Mastra Cloudプロジェクトダッシュボード](/image/cloud-agents.png)

3. **Gitリポジトリをインポート**

   - リポジトリを検索するか、利用可能なGitHubリポジトリのリストから選択します
   - デプロイしたいリポジトリの横にある「インポート」ボタンをクリックします

4. **デプロイメント詳細の設定**
   デプロイメント設定ページには以下が含まれます：
   - **リポジトリ名**：GitHubリポジトリ名（読み取り専用）
   - **プロジェクト名**：プロジェクト名をカスタマイズ（デフォルトはリポジトリ名）
   - **ブランチ**：デプロイするブランチを選択（ドロップダウン、デフォルトは`main`）
   - **プロジェクトルート**：プロジェクトのルートディレクトリを設定（デフォルトは`/`）
   - **Mastraディレクトリ**：Mastraファイルの場所を指定（デフォルトは`src/mastra`）
   - **ビルドコマンド**：ビルドプロセス中に実行するオプションコマンド
   - **ストア設定**：データストレージオプションを設定
   - **環境変数**：設定用のキーと値のペアを追加（例：APIキー）

## プロジェクト構造の要件

Mastra CloudはGitHubリポジトリを以下の項目についてスキャンします：

- **エージェント**：モデルとツールを備えたエージェント定義（例：天気エージェント）
- **ワークフロー**：ワークフローステップ定義（例：weather-workflow）
- **環境変数**：必要なAPIキーと設定変数

リポジトリは、適切な検出とデプロイメントのために標準的なMastraプロジェクト構造を含んでいる必要があります。

## ダッシュボードの理解

プロジェクトを作成すると、ダッシュボードには以下が表示されます：

### プロジェクト概要

- **作成日**: プロジェクトが作成された日時
- **ドメイン**: デプロイされたアプリケーションにアクセスするためのURL
  - 形式: `https://[project-name].mastra.cloud`
  - 形式: `https://[random-id].mastra.cloud`
- **ステータス**: 現在のデプロイメントステータス（成功またはアーカイブ済み）
- **ブランチ**: デプロイされたブランチ（通常は`main`）
- **環境変数**: 設定されたAPIキーと設定
- **ワークフロー**: 検出されたワークフローとステップ数のリスト
- **エージェント**: 検出されたエージェントとそのモデルおよびツールのリスト
- **データベース使用状況**: 読み取り、書き込み、ストレージの統計情報

### デプロイメントセクション

- すべてのデプロイメントのリスト：
  - デプロイメントID（コミットハッシュに基づく）
  - ステータス（成功/アーカイブ済み）
  - ブランチ
  - コミットハッシュ
  - タイムスタンプ

### ログセクション

ログビューには以下が表示されます：

- 各ログエントリのタイムスタンプ
- ログレベル（info、debug）
- ホスト名
- 詳細なログメッセージ（以下を含む）：
  - API起動情報
  - ストレージ初期化
  - エージェントとワークフローのアクティビティ

## ナビゲーション

サイドバーから以下にアクセスできます：

- **概要**：プロジェクトの概要と統計
- **デプロイメント**：デプロイ履歴と詳細
- **ログ**：デバッグ用のアプリケーションログ
- **エージェント**：すべてのエージェントのリストと設定
- **ワークフロー**：すべてのワークフローのリストと構造
- **設定**：プロジェクト構成オプション

## 環境変数の設定

ダッシュボードを通じて環境変数を設定します：

1. ダッシュボードでプロジェクトに移動します
2. 「環境変数」セクションに進みます
3. 変数を追加または編集します（例：`OPENAI_API_KEY`）
4. 設定を保存します

環境変数は暗号化され、デプロイメントと実行中にアプリケーションで利用可能になります。

## デプロイメントのテスト

デプロイメント後、以下の方法でエージェントとワークフローをテストできます：

1. プロジェクトに割り当てられたカスタムドメイン：`https://[project-name].mastra.cloud`
2. エージェントと直接対話するためのダッシュボードインターフェース

## 次のステップ

プロジェクトを設定した後、GitHubリポジトリの`main`ブランチにプッシュするたびに自動デプロイが行われます。詳細については、[デプロイメントのドキュメント](./deploying.mdx)を参照してください。
