---
title: Mastra Cloud
description: Mastraアプリケーションのデプロイメントと監視サービス
---

# Mastra Cloud

Mastra Cloudは、Mastraチームによって構築された展開サービスで、Mastraアプリケーションの実行、管理、監視を行います。標準的なMastraプロジェクトと連携し、デプロイメント、スケーリング、運用タスクを処理します。

> **ベータ版のお知らせ**
> Mastra Cloudは現在**パブリックベータ版**です。開発が進むにつれて、機能、API、UIが変更される可能性があります。

## コア機能

- **アトミックデプロイメント** - エージェントとワークフローが単一のユニットとしてデプロイされる
- **プロジェクト編成** - エージェントとワークフローをURLが割り当てられたプロジェクトにグループ化
- **環境変数** - 環境ごとに設定を安全に保存
- **テストコンソール** - Webインターフェースを通じてエージェントにメッセージを送信
- **実行トレース** - エージェントの対話とツール呼び出しを記録
- **ワークフロー可視化** - ワークフローのステップと実行パスを表示
- **ログ** - デバッグ用の標準ログ出力
- **プラットフォーム互換性** - Cloudflare、Vercel、Netlifyデプロイヤーと同じインフラストラクチャを使用

## ダッシュボードコンポーネント

Mastra Cloudダッシュボードには以下が含まれています：

- **プロジェクトリスト** - アカウント内のすべてのプロジェクト
- **プロジェクト詳細** - デプロイメント、環境変数、アクセスURL
- **デプロイメント履歴** - タイムスタンプとステータスを含むデプロイメントの記録
- **エージェントインスペクター** - モデル、ツール、システムプロンプトを表示するエージェント設定ビュー
- **テストコンソール** - エージェントにメッセージを送信するためのインターフェース
- **トレースエクスプローラー** - ツール呼び出し、パラメータ、レスポンスの記録
- **ワークフロービューア** - ワークフローステップと接続の図

## 技術的な実装

Mastra Cloudは、プラットフォーム固有のデプロイヤーと同じコアコードで動作し、以下の修正が加えられています：

- **エッジネットワーク分散** - 地理的に分散された実行
- **動的リソース割り当て** - トラフィックに基づいてコンピューティングリソースを調整
- **Mastra専用ランタイム** - エージェント実行に最適化されたランタイム
- **標準デプロイメントAPI** - 環境間で一貫したデプロイメントインターフェース
- **トレーシングインフラストラクチャ** - すべてのエージェントとワークフロー実行ステップを記録

## ユースケース

一般的な使用パターン：

- インフラストラクチャを管理せずにアプリケーションをデプロイする
- ステージング環境と本番環境を維持する
- 多くのリクエストにわたるエージェントの動作を監視する
- Webインターフェースを通じてエージェントの応答をテストする
- 複数のリージョンにデプロイする

## セットアッププロセス

1. [Mastra Cloudプロジェクトを設定する](/docs/mastra-cloud/setting-up)
2. [コードをデプロイする](/docs/mastra-cloud/deploying)
3. [実行トレースを表示する](/docs/mastra-cloud/observability)
