---
title: Mastra Cloudへのデプロイ
description: Mastraアプリケーション向けのGitHubベースのデプロイプロセス
---

# Mastra Cloudへのデプロイ

> **ベータ版のお知らせ**
> Mastra Cloudは現在**パブリックベータ版**です。

このページでは、GitHubインテグレーションを使用してMastraアプリケーションをMastra Cloudにデプロイするプロセスについて説明します。

## 前提条件

- GitHubアカウント
- Mastraアプリケーションを含むGitHubリポジトリ
- Mastra Cloudへのアクセス権

## デプロイメントプロセス

Mastra Cloud は、Vercel や Netlify のようなプラットフォームに似た、Git ベースのデプロイメントワークフローを採用しています。

1. **GitHub リポジトリのインポート**

   - Projects ダッシュボードから「Add new」をクリックします
   - Mastra アプリケーションが含まれているリポジトリを選択します
   - 対象のリポジトリの横にある「Import」をクリックします

2. **デプロイメント設定の構成**

   - プロジェクト名を設定します（デフォルトはリポジトリ名）
   - デプロイするブランチを選択します（通常は `main`）
   - Mastra ディレクトリパスを設定します（デフォルトは `src/mastra`）
   - 必要な環境変数（API キーなど）を追加します

3. **Git からのデプロイ**
   - 初期設定後、選択したブランチへのプッシュによってデプロイメントがトリガーされます
   - Mastra Cloud が自動的にアプリケーションをビルドし、デプロイします
   - 各デプロイメントごとに、エージェントとワークフローのアトミックスナップショットが作成されます

## 自動デプロイ

Mastra Cloudはギット駆動のワークフローに従います：

1. ローカルでMastraアプリケーションに変更を加える
2. 変更を`main`ブランチにコミットする
3. G<PERSON><PERSON>ubにプッシュする
4. Mastra Cloudは自動的にプッシュを検出し、新しいデプロイメントを作成する
5. ビルドが完了すると、アプリケーションが本番環境で利用可能になる

## デプロイメントドメイン

各プロジェクトには2つのURLが付与されます。

1. **プロジェクト専用ドメイン**: `https://[project-name].mastra.cloud`

   - 例: `https://gray-acoustic-helicopter.mastra.cloud`

2. **デプロイメント専用ドメイン**: `https://[deployment-id].mastra.cloud`
   - 例: `https://young-loud-caravan-6156280f-ad56-4ec8-9701-6bb5271fd73d.mastra.cloud`

これらのURLから、デプロイしたエージェントやワークフローに直接アクセスできます。

## デプロイメントの表示

![デプロイメントリスト](../../../../../public/image/cloud-agents.png)

ダッシュボードのデプロイメントセクションには以下が表示されます：

- **タイトル**：デプロイメント識別子（コミットハッシュに基づく）
- **ステータス**：現在の状態（成功またはアーカイブ済み）
- **ブランチ**：使用されたブランチ（通常は`main`）
- **コミット**：Gitコミットハッシュ
- **更新日時**：デプロイメントのタイムスタンプ

各デプロイメントは、特定の時点におけるMastraアプリケーションの原子的なスナップショットを表します。

## エージェントとの対話

![エージェントインターフェース](../../../../../public/image/cloud-agent.png)

デプロイ後、エージェントと対話する方法：

1. ダッシュボードでプロジェクトに移動する
2. エージェントセクションに進む
3. エージェントを選択して詳細とインターフェースを表示する
4. チャットタブを使用してエージェントとコミュニケーションを取る
5. 右側のパネルでエージェントの設定を確認する：
   - モデル情報（例：OpenAI）
   - 利用可能なツール（例：getWeather）
   - 完全なシステムプロンプト
6. 提案されたプロンプト（「どのような機能がありますか？」など）を使用するか、カスタムメッセージを入力する

インターフェースにはエージェントのブランチ（通常は「main」）が表示され、会話メモリが有効かどうかが示されます。

## ログのモニタリング

ログセクションはアプリケーションに関する詳細情報を提供します：

- **時間**: ログエントリが作成された時刻
- **レベル**: ログレベル（info、debug）
- **ホスト名**: サーバー識別情報
- **メッセージ**: 詳細なログ情報、以下を含む：
  - APIの初期化
  - ストレージ接続
  - エージェントとワークフローのアクティビティ

これらのログは、本番環境でのアプリケーションの動作をデバッグおよびモニタリングするのに役立ちます。

## ワークフロー

![ワークフローインターフェース](../../../../../public/image/cloud-workflows.png)

ワークフローセクションでは、デプロイされたワークフローを表示および操作できます：

1. プロジェクト内のすべてのワークフローを表示
2. ワークフロー構造とステップを確認
3. 実行履歴とパフォーマンスデータにアクセス

## データベース使用量

Mastra Cloudはデータベース使用状況の指標を追跡します：

- 読み取り回数
- 書き込み回数
- 使用ストレージ（MB）

これらの指標はプロジェクト概要に表示され、リソース消費を監視するのに役立ちます。

## デプロイメント設定

ダッシュボードからデプロイメントを設定します：

1. プロジェクト設定に移動します
2. 環境変数（`OPENAI_API_KEY`など）を設定します
3. プロジェクト固有の設定を構成します

設定の変更を反映させるには、新しいデプロイメントが必要です。

## 次のステップ

デプロイ後、オブザーバビリティツールを使用して[実行をトレースおよび監視](./observability.mdx)します。
