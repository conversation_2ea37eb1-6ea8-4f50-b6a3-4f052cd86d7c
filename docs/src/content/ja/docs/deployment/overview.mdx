---
title: デプロイメント概要
description: Mastraアプリケーションの様々なデプロイメントオプションについて学ぶ
---

# デプロイメント概要

Mastraは、フルマネージドソリューションからセルフホスティングオプションまで、アプリケーションのニーズに合わせた複数のデプロイメントオプションを提供しています。このガイドでは、利用可能なデプロイメントパスを理解し、プロジェクトに最適なものを選択するのに役立ちます。

## デプロイメントオプション

### Mastra Cloud

Mastra Cloudはデプロイメントプラットフォームで、GitHubリポジトリに接続し、コード変更時に自動的にデプロイし、モニタリングツールを提供します。以下が含まれます：

- GitHubリポジトリ統合
- git pushによるデプロイメント
- エージェントテストインターフェース
- 包括的なログとトレース
- 各プロジェクト用のカスタムドメイン

[Mastra Cloudのドキュメントを見る →](/docs/mastra-cloud/overview)

### サーバーを使用

Mastraを標準のNode.js HTTPサーバーとしてデプロイすることができ、インフラストラクチャとデプロイメント環境を完全に制御できます。

- カスタムAPIルートとミドルウェア
- 設定可能なCORSと認証
- VM、コンテナ、またはPaaSプラットフォームへのデプロイ
- 既存のNode.jsアプリケーションとの統合に最適

[サーバーデプロイメントガイド →](/docs/deployment/server)

### サーバーレスプラットフォーム

Mastraは人気のあるサーバーレスプラットフォーム向けのプラットフォーム固有のデプロイヤーを提供し、最小限の設定でアプリケーションをデプロイすることができます。

- Cloudflare Workers、Vercel、またはNetlifyへのデプロイ
- プラットフォーム固有の最適化
- 簡素化されたデプロイメントプロセス
- プラットフォームを通じた自動スケーリング

[サーバーレスデプロイメントガイド →](/docs/deployment/deployment)

## クライアント設定

Mastraアプリケーションをデプロイした後、クライアントを設定して通信できるようにする必要があります。Mastra Client SDKは、Mastraサーバーとやり取りするためのシンプルで型安全なインターフェースを提供します。

- 型安全なAPI操作
- 認証とリクエスト処理
- リトライとエラー処理
- ストリーミングレスポンスのサポート

[クライアント設定ガイド →](/docs/deployment/client)

## デプロイメントオプションの選択

| オプション                       | 最適な用途                                            | 主なメリット                                                       |
| -------------------------------- | ----------------------------------------------------- | ------------------------------------------------------------------ |
| **Mastra Cloud**                 | インフラの心配なく迅速に展開したいチーム              | フルマネージド、自動スケーリング、組み込み型の可観測性             |
| **サーバーデプロイメント**       | 最大限のコントロールとカスタマイズが必要なチーム      | 完全なコントロール、カスタムミドルウェア、既存アプリとの統合       |
| **サーバーレスプラットフォーム** | すでにVercel、Netlify、Cloudflareを使用しているチーム | プラットフォーム統合、簡素化されたデプロイメント、自動スケーリング |
