---
title: "Mastraサーバーの作成"
description: "ミドルウェアやその他のオプションでMastraサーバーを設定およびカスタマイズする"
---

# Mastraサーバーの作成

開発中または Mastra アプリケーションをデプロイする際、エージェント、ワークフロー、およびその他の機能を API エンドポイントとして公開する HTTP サーバーとして実行されます。このページでは、サーバーの動作を設定およびカスタマイズする方法について説明します。

## サーバーアーキテクチャ

Mastraは[Hono](https://hono.dev)を基盤となるHTTPサーバーフレームワークとして使用しています。`mastra build`を使用してMastraアプリケーションをビルドすると、`.mastra`ディレクトリにHonoベースのHTTPサーバーが生成されます。

サーバーは以下を提供します：

- 登録されたすべてのエージェント用のAPIエンドポイント
- 登録されたすべてのワークフロー用のAPIエンドポイント
- カスタムAPIルートのサポート
- カスタムミドルウェアのサポート
- タイムアウトの設定
- ポートの設定
- ボディリミットの設定

追加のサーバー動作の追加については、[ミドルウェア](/docs/deployment/middleware)と
[カスタムAPIルート](/docs/deployment/custom-api-routes)のページを参照してください。

## サーバー設定

Mastraインスタンスでサーバーの`port`と`timeout`を設定できます。

```typescript copy showLineNumbers
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  server: {
    port: 3000, // デフォルトは4111
    timeout: 10000, // デフォルトは30000（30秒）
  },
});
```

`method`オプションは`"GET"`、`"POST"`、`"PUT"`、`"DELETE"`または`"ALL"`のいずれかです。`"ALL"`を使用すると、パスに一致する任意のHTTPメソッドに対してハンドラーが呼び出されます。

## カスタムCORS設定

Mastraでは、サーバーのCORS（クロスオリジンリソース共有）設定をカスタマイズすることができます。

```typescript copy showLineNumbers
import { Mastra } from "@mastra/core";

export const mastra = new Mastra({
  server: {
    cors: {
      origin: ["https://example.com"], // 特定のオリジンを許可、または'*'ですべてを許可
      allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowHeaders: ["Content-Type", "Authorization"],
      credentials: false,
    },
  },
});
```

## デプロイメント

Mastraは標準的なNode.jsサーバーにビルドされるため、Node.jsアプリケーションを実行するあらゆるプラットフォームにデプロイできます：

- クラウドVM（AWS EC2、DigitalOcean Droplets、GCP Compute Engine）
- コンテナプラットフォーム（Docker、Kubernetes）
- Platform as a Service（Heroku、Railway）
- 自己ホスト型サーバー

### ビルド

アプリケーションをビルドします：

```bash copy
# 現在のディレクトリからビルド
mastra build

# またはディレクトリを指定
mastra build --dir ./my-project
```

ビルドプロセス：

1. エントリーファイル（`src/mastra/index.ts`または`src/mastra/index.js`）を特定
2. `.mastra`出力ディレクトリを作成
3. ツリーシェイキングとソースマップを使用してRollupでコードをバンドル
4. [Hono](https://hono.dev) HTTPサーバーを生成

すべてのオプションについては[`mastra build`](/reference/cli/build)を参照してください。

### サーバーの実行

HTTPサーバーを起動します：

```bash copy
node .mastra/output/index.mjs
```

### ビルド出力用のテレメトリを有効にする

ビルド出力のインストルメンテーションを次のように読み込みます：

```bash copy
node --import=./.mastra/output/instrumentation.mjs  .mastra/output/index.mjs
```

## サーバーレスデプロイメント

MastraはCloudflare Workers、Vercel、Netlifyでのサーバーレスデプロイメントもサポートしています。

セットアップ手順については、[サーバーレスデプロイメント](/docs/deployment/deployment)ガイドをご覧ください。
