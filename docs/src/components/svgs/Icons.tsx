export const CopyIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="2"
    stroke="currentColor"
    width="16"
    height="16"
    style={{ minWidth: "16px", minHeight: "16px" }}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
    />
  </svg>
);

export const DownloadIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="2"
    stroke="currentColor"
    width="16"
    height="16"
    style={{ minWidth: "16px", minHeight: "16px" }}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"
    />
  </svg>
);

export const CheckIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="2"
    stroke="currentColor"
    width="16"
    height="16"
    style={{ minWidth: "16px", minHeight: "16px" }}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M4.5 12.75l6 6 9-13.5"
    />
  </svg>
);

export const SearchIcon = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.4473 17.989L14.0453 12.5856C15.2243 11.0351 15.7707 9.0946 15.5738 7.15654C15.3768 5.21847 14.4513 3.42759 12.9843 2.14621C11.5174 0.864832 9.61869 0.188614 7.67224 0.254384C5.72581 0.320155 3.87701 1.12298 2.49988 2.50046C1.12276 3.87794 0.320126 5.72721 0.254384 7.67416C0.188628 9.6211 0.864664 11.5203 2.14572 12.9877C3.42677 14.4549 5.21719 15.3807 7.15475 15.5777C9.09231 15.7748 11.0324 15.2282 12.5824 14.0488L17.9844 19.4522C18.1801 19.6432 18.4427 19.75 18.7159 19.75C18.9892 19.75 19.2516 19.6432 19.4473 19.4522C19.6411 19.2582 19.75 18.9951 19.75 18.7207C19.75 18.4463 19.6411 18.1832 19.4473 17.989ZM2.37476 7.95921C2.37476 6.85433 2.7023 5.77425 3.31598 4.85558C3.92967 3.9369 4.80192 3.22088 5.82243 2.79806C6.84295 2.37523 7.9659 2.26461 9.04928 2.48016C10.1326 2.69572 11.1278 3.22776 11.9089 4.00904C12.6899 4.79031 13.2218 5.7857 13.4373 6.86936C13.6528 7.95301 13.5422 9.07624 13.1196 10.097C12.6968 11.1178 11.981 11.9903 11.0625 12.6041C10.1441 13.2179 9.0643 13.5457 7.9597 13.5457C6.47902 13.5438 5.05948 12.9547 4.01248 11.9074C2.96548 10.8602 2.3765 9.44027 2.37476 7.95921Z"
      fill="#939393"
    />
  </svg>
);

export const JarvisIcon = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="22"
    viewBox="0 0 12 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M1.67367 8.51865C1.70412 8.21908 1.96814 7.99681 2.27013 8.01979C2.5769 8.04314 2.8067 8.31076 2.78335 8.61754L2.77481 8.73371C2.69004 9.93701 2.70422 11.2298 2.82645 12.5636L2.84097 12.717C3.15399 15.9238 3.9724 18.0917 4.99007 19.2796C5.50021 19.8751 6.0462 20.2091 6.59501 20.3311C7.14145 20.4526 7.74556 20.3762 8.39782 20.054C9.58122 19.4694 9.90493 18.2567 9.87429 16.7007C9.85921 15.9349 9.75888 15.1467 9.66622 14.391C9.5766 13.66 9.48846 12.9239 9.52896 12.3417C9.65585 10.5176 10.5725 9.53314 11.1801 9.24781C11.4586 9.11703 11.7904 9.23674 11.9211 9.51522C12.0519 9.7937 11.9322 10.1255 11.6537 10.2563C11.4692 10.343 10.744 10.9306 10.6404 12.419C10.6089 12.8719 10.6784 13.4905 10.7721 14.2554C10.8629 14.9956 10.9718 15.8445 10.9882 16.6787C11.0206 18.3226 10.6962 20.1613 8.89124 21.0529C8.04932 21.4688 7.18624 21.6039 6.35326 21.4187C5.52264 21.2341 4.77695 20.7434 4.14395 20.0045C2.91103 18.5653 2.05448 16.1305 1.73188 12.8234L1.71694 12.6652C1.58544 11.2304 1.57328 9.83481 1.67237 8.53297L1.67367 8.51865ZM7.25085 10.0281C7.28523 9.72892 7.55218 9.51012 7.85384 9.53706C8.16028 9.56443 8.38652 9.83507 8.35915 10.1415L8.34337 10.3205C8.26743 11.1995 8.23873 11.8552 8.43682 12.9667L8.45176 13.0515C8.6037 13.9239 8.67854 14.6807 8.56085 15.2745C8.49746 15.5944 8.37341 15.895 8.15301 16.1432C7.93048 16.3937 7.64329 16.5531 7.31739 16.6436C7.02142 16.7258 6.63413 16.7821 6.22441 16.6889C5.79962 16.5922 5.3937 16.3439 5.06878 15.892C4.45063 15.0322 4.14505 13.4647 4.34444 10.773C4.36718 10.4662 4.63433 10.2359 4.94115 10.2586C5.24797 10.2813 5.47826 10.5485 5.45554 10.8553C5.25887 13.5103 5.60142 14.7242 5.97341 15.2416C6.14354 15.4782 6.31817 15.5675 6.47169 15.6025C6.64029 15.6409 6.82909 15.6229 7.01918 15.5701C7.17934 15.5256 7.26568 15.4644 7.31994 15.4033C7.37632 15.3398 7.43234 15.2376 7.46796 15.0579C7.5421 14.6838 7.50466 14.1095 7.3548 13.2466L7.33995 13.1622C7.10338 11.8348 7.15901 11.0548 7.24943 10.0424L7.25085 10.0281ZM5.32389 0.757226C6.00483 0.469969 6.75382 0.419014 7.54358 0.626033L7.58513 0.637122C8.45372 0.873033 9.06389 1.30029 9.41995 1.90628C9.77308 2.50729 9.82553 3.19936 9.75657 3.85907C9.72459 4.16507 9.45059 4.3872 9.14459 4.35522C8.8386 4.32322 8.61646 4.04923 8.64844 3.74323C8.70471 3.2049 8.64408 2.78517 8.45933 2.47073C8.28866 2.18025 7.96544 1.90093 7.32413 1.7209L7.26106 1.70378C6.69342 1.55498 6.19716 1.59804 5.75693 1.78375C5.30972 1.97241 4.88496 2.32371 4.49798 2.84505L3.60336 2.18097C4.07749 1.54222 4.6499 1.04155 5.32389 0.757226ZM4.49798 2.84505C4.3146 3.09209 3.96569 3.14368 3.71865 2.9603C3.47161 2.77692 3.41998 2.42801 3.60336 2.18097L4.49798 2.84505Z"
      fill="currentColor"
    />
    <path
      d="M3.10877 1.84474C3.98563 1.84471 4.96126 2.12349 5.69718 3.00472L5.73204 3.04715L5.74091 3.05845C5.92427 3.2973 5.88466 3.64013 5.64918 3.83058C5.4137 4.02103 5.07014 3.98808 4.87492 3.75881L4.86571 3.74777L4.84332 3.72048C4.37023 3.15334 3.74659 2.95887 3.10881 2.95889C2.44567 2.95892 1.80776 3.17314 1.43783 3.3657C1.16492 3.50774 0.828547 3.40166 0.686495 3.12875C0.544447 2.85585 0.650531 2.51947 0.923438 2.37742C1.41979 2.11906 2.23325 1.84477 3.10877 1.84474Z"
      fill="currentColor"
    />
    <path
      d="M6.94917 3.37103C7.45685 3.15101 8.26812 3.02342 9.14957 3.27503C10.0346 3.52767 10.9537 4.15183 11.6976 5.36746L11.733 5.42579L11.7402 5.43822C11.8881 5.70048 11.8007 6.0344 11.5408 6.18985C11.2809 6.3453 10.9453 6.26437 10.7843 6.00995L10.7767 5.99769L10.7477 5.94969C10.1368 4.95095 9.43746 4.51589 8.84374 4.34641C8.22292 4.1692 7.67598 4.27029 7.39217 4.39328C7.10987 4.51562 6.78188 4.38595 6.65954 4.10366C6.5372 3.82136 6.66687 3.49337 6.94917 3.37103Z"
      fill="currentColor"
    />
    <path
      d="M4.01446 6.59668C4.01446 5.80226 3.37045 5.15825 2.57603 5.15825C1.7816 5.15825 1.13759 5.80226 1.13759 6.59668C1.13759 7.39111 1.7816 8.03512 2.57603 8.03512V9.14927C1.16627 9.14927 0.0234375 8.00644 0.0234375 6.59668C0.0234375 5.18692 1.16627 4.04409 2.57603 4.04409C3.98578 4.04409 5.12862 5.18692 5.12862 6.59668C5.12862 8.00644 3.98578 9.14927 2.57603 9.14927V8.03512C3.37045 8.03512 4.01446 7.39111 4.01446 6.59668Z"
      fill="currentColor"
    />
    <path
      d="M9.56214 8.02612C9.56214 7.23169 8.91813 6.58768 8.12371 6.58768C7.32928 6.58768 6.68527 7.23169 6.68527 8.02612C6.68527 8.82054 7.32928 9.46455 8.12371 9.46455V10.5787C6.71395 10.5787 5.57112 9.43587 5.57112 8.02612C5.57112 6.61636 6.71395 5.47353 8.12371 5.47353C9.53346 5.47353 10.6763 6.61636 10.6763 8.02612C10.6763 9.43587 9.53346 10.5787 8.12371 10.5787V9.46455C8.91813 9.46455 9.56214 8.82054 9.56214 8.02612Z"
      fill="currentColor"
    />
  </svg>
);

export const PaperIcon = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M4.95019 15.6001C4.95019 17.5052 6.49486 19.0494 8.3999 19.0498L15.5981 19.0498C17.5035 19.0498 19.0479 17.5055 19.0479 15.6001L19.0479 8.40332C19.0479 6.49794 17.5035 4.95215 15.5981 4.95215L12.0532 4.95215L12.0532 12.0552L4.95019 12.0552L4.95019 15.6001ZM9.95264 6.4375L6.43555 9.95459L9.95264 9.95459L9.95264 6.4375ZM2.85107 10.5698L10.5679 2.85303L15.5981 2.85303C18.6633 2.85303 21.1484 5.33814 21.1484 8.40332L21.1484 15.6001C21.1484 18.6653 18.6633 21.1504 15.5981 21.1504L8.3999 21.1504C5.33506 21.15 2.85107 18.665 2.85107 15.6001L2.85107 10.5698Z"
      fill="#939393"
    />
  </svg>
);

export const ArrowLeftIcon = ({ className }: { className?: string }) => (
  <svg
    width="8"
    height="12"
    viewBox="0 0 8 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M6.89844 0.902344L1.79844 6.00293L6.89638 11.1008"
      stroke="currentColor"
      strokeWidth="1.56775"
      strokeLinecap="round"
    />
  </svg>
);

export const BookIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="14"
      height="18"
      viewBox="0 0 14 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.84338 2.9819L5.15693 1.43953C5.26946 1.36451 5.51359 1.33824 5.6377 1.38598L11.8924 3.79163C11.9603 3.81776 12.0576 3.9594 12.0576 4.03839V15.2704C12.0576 15.5807 12.3092 15.8323 12.6195 15.8323C12.9299 15.8323 13.1815 15.5807 13.1815 15.2704V4.03839C13.1815 3.49448 12.798 2.93574 12.2959 2.74262L6.04116 0.33697C5.57782 0.15876 4.9507 0.226234 4.53349 0.504373L1.63056 2.43966L1.25523 2.68988L1.25808 2.69722C0.991616 2.81753 0.818359 3.0944 0.818359 3.46918V13.6392C0.818359 14.26 1.28833 14.9368 1.86564 15.1501L8.74755 17.6932C9.32594 17.9069 9.79483 17.574 9.79483 16.9568V6.2906C9.79483 5.6702 9.31222 5.01554 8.72986 4.83246L2.84338 2.9819Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const BurgerIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M1.75977 1.49219H14.2332M1.75977 7.06508H10.4775M1.75977 12.5084H6.32663"
        stroke="currentColor"
        strokeWidth="2.125"
        strokeLinecap="round"
      />
    </svg>
  );
};
