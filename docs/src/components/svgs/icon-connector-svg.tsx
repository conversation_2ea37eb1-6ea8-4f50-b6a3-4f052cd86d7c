export const Lines = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="772"
    height="268"
    viewBox="0 0 772 268"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{ marginLeft: "-90px" }}
    {...props}
  >
    <path
      d="M285.935 1.42492C285.935 1.42492 277.681 62.9551 336.018 134.625C394.354 206.295 386.1 267.825 386.1 267.825M411.061 0.540039C411.061 0.540039 377.48 87.7173 210.898 88.4676C77.0171 89.2795 1.25586 210.081 1.25586 210.081M436.187 0.690071C436.187 0.690071 442.16 92.1156 496.754 128.984C566.363 182.666 579.945 254.971 579.945 254.971M461.312 0.782455C461.312 0.782455 467.169 109.6 341.742 119.604C216.316 129.607 188.615 249.647 188.615 249.647M361.344 0.790337C361.344 0.790337 381.462 95.4191 547.587 93.7673C691.024 90.6975 770.809 210.081 770.809 210.081M385.765 0.540039C385.765 0.540039 395.489 76.4617 340.712 125.236C285.935 174.01 289.598 266.152 289.598 266.152M335.878 0.690071C335.878 0.690071 330.97 84.9767 407.149 116.526C483.328 148.075 483.328 265.036 483.328 265.036M486.749 1.60795C486.749 1.60795 474.373 95.6638 280.321 107.049C143.665 118.349 94.8179 236.441 94.8179 236.441M460.654 0.690071C460.654 0.690071 462.122 90.9822 566.237 128.984C647.065 157.345 677.579 237.067 677.579 237.067"
      stroke="url(#paint0_linear_16665_118371)"
      strokeWidth="1.2223"
      strokeDasharray="6 6"
    />
    <defs>
      <linearGradient
        id="paint0_linear_16665_118371"
        x1="386.032"
        y1="0.540039"
        x2="386.032"
        y2="267.825"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#393939" stopOpacity="0.3" />
        <stop offset="1" stopColor="#393939" />
      </linearGradient>
    </defs>
  </svg>
);
