import React from "react";

function formatToK(number: number) {
  if (number >= 1000) {
    return (number / 1000).toFixed(number % 1000 === 0 ? 0 : 1) + "k";
  }
  return number?.toString();
}

export const GithubStarCount = ({ stars }: { stars: number }) => {
  return (
    <div className="font-medium w-fit text-[var(--light-color-text-3)] dark:text-white rounded-md opacity-90 transition-colors hover:opacity-100 flex items-center gap-2 justify-start pl-[7px] pr-2.5 py-2 h-[2.125rem] text-sm">
      <svg
        width="37"
        height="37"
        viewBox="0 0 37 37"
        fill="none"
        className="size-5 dark:text-white"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.6239 0.969559C8.78382 0.969559 0.829102 9.11871 0.829102 19.2003C0.829102 27.2591 5.92596 34.0807 12.9967 36.4951C13.8807 36.6766 14.2045 36.1028 14.2045 35.6201C14.2045 35.1975 14.1754 33.7488 14.1754 32.2393C9.22528 33.3261 8.19447 30.0661 8.19447 30.0661C7.39896 27.9533 6.22027 27.4102 6.22027 27.4102C4.60012 26.2934 6.33829 26.2934 6.33829 26.2934C8.13547 26.4142 9.07849 28.1648 9.07849 28.1648C10.6691 30.9414 13.2323 30.1569 14.2635 29.6738C14.4107 28.4967 14.8824 27.6817 15.3832 27.2291C11.4351 26.8065 7.28131 25.237 7.28131 18.2343C7.28131 16.2422 7.98795 14.6124 9.10763 13.3448C8.93097 12.8921 8.31212 11.0204 9.28465 8.51531C9.28465 8.51531 10.7872 8.03228 14.175 10.3866C15.6254 9.98759 17.1213 9.78458 18.6239 9.78287C20.1264 9.78287 21.658 9.99438 23.0724 10.3866C26.4606 8.03228 27.9631 8.51531 27.9631 8.51531C28.9356 11.0204 28.3164 12.8921 28.1397 13.3448C29.2889 14.6124 29.9664 16.2422 29.9664 18.2343C29.9664 25.237 25.8126 26.7761 21.835 27.2291C22.4834 27.8025 23.0429 28.8889 23.0429 30.6095C23.0429 33.0543 23.0137 35.0164 23.0137 35.6198C23.0137 36.1028 23.3379 36.6766 24.2216 36.4954C31.2923 34.0803 36.3891 27.2591 36.3891 19.2003C36.4183 9.11871 28.4344 0.969559 18.6239 0.969559Z"
          fill="currentColor"
        />
      </svg>

      <div className="flex gap-1  items-center w-4">
        <span>{formatToK(stars)}</span>
      </div>
    </div>
  );
};
