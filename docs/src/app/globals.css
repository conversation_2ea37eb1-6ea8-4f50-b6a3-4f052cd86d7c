@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

/* TODO: use :root and .dark class to make theming easier */
/* Move all colors to :root and unify dark mode */
/* Use one color format*/
:root {
  --border: hsla(0, 0%, 80%, 1);
  --color-text-icon-5: hsla(0, 0%, 8%, 1);
  --color-text-icon-6: hsla(0, 0%, 4%, 1);
  --color-green-accent: hsla(130deg, 81.6%, 27.6%, 1);
  --color-green-accent-2: hsla(125, 66%, 50%, 1);
  --border-2: hsla(0, 0%, 72%, 1);
  --border-radius-lg: 0.5rem;
  --diff-color: #2ea04326;
}

.dark {
  --border: hsla(0, 0%, 19%, 1);
  --color-text-icon-5: hsla(0, 0%, 90%, 1);
  --color-text-icon-6: hsla(0, 0%, 100%, 1);
  --primary: #050505;
  --border-2: hsla(0, 0%, 26%, 1);
}

@theme inline {
  --primary-bg: #050505;
  --secondary-bg: #121212;
  --tertiary-bg: #171717;
  --border: #343434;
  --color-primary: #fff;
  --color-el-2: #707070;
  --color-el-3: #939393;
  --color-el-4: #e6e6e6;
  --font-sans: var(--inter);
  --font-mono: var(--geist-mono);
  --font-serif: var(--tasa);

  --tag-blue: 211, 69%, 49%;
  --tag-green: 143, 97%, 54%;
  --tag-purple: 286, 38%, 52%;

  --tag-green-light: 130, 74%, 32%;
  --tag-blue-light: 212, 76%, 46%;
  --tag-purple-light: 282, 66%, 47%;

  --light-color-surface-1: #f0f0f0;
  --light-color-surface-15: #fafafa;
  --light-color-surface-2: #f2f2f2;
  --light-color-surface-3: #ededed;
  --light-color-surface-4: #ebebeb;
  --light-color-surface-5: hsla(0, 0%, 88%, 0.9);
  --light-color-text-6: #0a0a0a;
  --light-color-text-5: #141414;
  --light-color-text-4: #5f5f5f;
  --light-color-text-3: #7f7e7e;
  --light-color-text-2: #8f8f8f;
  --light-color-text-1: #929292;
  --light-color-accent-3: hsla(0, 0%, 50%, 1);
  --light-border-muted: #ccc;
  --light-border-code: #b8b8b8;
  --light-green-accent: #0d8020;
  --light-green-code: #177326;
  --light-green-muted: #84d291;
  --light-blue-accent: #1565c0;
  --light-green-accent-2: hsla(125, 66%, 50%, 1);
  --light-blue-code: #1565c0;
  --light-purple-accent: #7b1fa2;
  --light-purple-code: #7b1fa2;
  --light-red-accent: #d32f2f;
  --light-red-code: #d32f2f;

  --color-icons-2: hsla(0, 0%, 44%, 1);
  --color-icons-3: hsla(0, 0%, 58%, 1);
  --color-icons-4: hsla(0, 0%, 66%, 1);
  --color-icons-5: hsla(0, 0%, 90%, 1);
  --color-icons-6: hsla(0, 0%, 100%, 1);
  --color-surface-1: hsla(0, 0%, 100%, 1);
  --color-surface-2: hsla(0, 0%, 95%, 1);
  --color-surface-3: hsla(0, 0%, 8%, 1);
  --color-surface-4: hsla(0, 0%, 10%, 1);
  --color-surface-5: hsla(0, 0%, 18%, 0.9);
  --color-borders-1: hsla(0, 0%, 19%, 1);
  --color-borders-2: hsla(0, 0%, 26%, 1);
  --color-accent-green: hsla(143, 97%, 54%, 1);
  --color-tag-green-2: hsla(142, 30%, 15%, 1);
}

#use-ai-text {
  color: var(--color-icons-5);
}

:root {
  --nextra-navbar-height: 60px;
  --nextra-content-width: 1220px;
  --copilot-kit-primary-color: var(--tag-green);
}

body {
  &:is(html[class~="light"] *) {
    background: var(--light-color-surface-15);
  }

  &:is(html[class~="dark"] *) {
    background: var(--primary-bg);
  }
}

html[class~="light"] *::selection {
  background: #e3e3e3 !important;
  color: #158d29 !important;
}

html[class~="dark"] *::selection {
  background: #2a2a2a !important;
  color: #46f488 !important;
}

.copilotKitWindow {
  background: var(--copilot-kit-background-color);
}

#scroll-area-viewport > div {
  display: block !important;
}

.copilotKitMarkdown p {
  font-size: 13px !important;
}

.copilotKitMarkdown hr {
  margin-block: 16px !important;
  color: var(--color-borders-1) !important;
}

.copilotKitMarkdown a {
  color: var(--color-accent-green) !important;
}

.copilotKitMarkdown {
  max-width: 100% !important;
}

button.copilotKitCodeBlockToolbarButton {
  padding-inline: 8px !important;
  cursor: pointer !important;
}

div .copilotKitPopup {
  z-index: 1000;
}

#custom-chat-input {
  /* hide scroll bar */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

#custom-chat-input::-webkit-scrollbar {
  display: none;
}

#docs-search-results {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

#docs-search-results::-webkit-scrollbar {
  display: none;
}
.poweredBy {
  background: var(--copilot-kit-background-color);
}

div .copilotKitInputContainer {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* Navbar */
.nextra-navbar-blur {
  display: none;
}

header + div,
footer {
  --nextra-content-width: 1220px;
}

header {
  position: static;
  background: var(--primary-bg);

  &:is(html[class~="light"] *) {
    background: var(--light-color-surface-15);
  }
}

header > * {
  &:is(html[class~="light"] *) {
    background: var(--light-color-surface-15);
  }

  &:is(html[class~="dark"] *) {
    background: var(--primary-bg);
  }
}

header nav > div:nth-child(2) {
  display: none;
}

header nav:nth-child(2) {
  &:is(html[class~="light"] *) {
    border-bottom: 0.5px solid var(--light-border-muted);
  }

  &:is(html[class~="dark"] *) {
    color: white;
    border-bottom: 0.5px solid var(--border);
  }
}

/* mobile nav */
.nextra-mobile-nav {
  padding-block-start: calc(var(--nextra-navbar-height) * 2);
}

.nextra-navbar nav > a {
  &:hover {
    transition: none;
    opacity: 1;
  }
}

.nextra-navbar input[type="search"] {
  width: 180px;
  height: 34px;
  font-size: 14px;
  border-radius: 0.375rem;

  &:is(html[class~="light"] *) {
    background: var(--light-color-surface-1);
  }

  &:is(html[class~="dark"] *) {
    background: #121212;
    border: 0.5px solid #343434;
  }

  &::placeholder {
    color: var(--color-el-3);
  }

  &:focus {
    box-shadow: none;
    outline: 1px solid hsl(var(--tag-green));
  }
}

.nextra-navbar kbd {
  border-radius: 4px;
  border: unset;
  color: var(--color-el-3);
}

.nextra-navbar kbd {
  padding-inline: 0.38rem;
  padding-block: 0.19rem;
  height: 1.375rem;
  background: var(--light-color-surface-15);

  &:is(html[class~="dark"] *) {
    background: var(--primary-bg);
  }
}

.nextra-navbar nav {
  position: relative;
  --nextra-content-width: 1220px;

  @media screen and (min-width: 768px) {
    padding-inline: 0;
  }
}

/* Hack for menu */
.nextra-navbar nav > div:nth-child(2) {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  gap: 1rem;
  margin-top: 1px;
}

/* Navbar menu items */
.nextra-navbar nav > div:nth-child(2) > * {
  padding: 0.62rem;
  font-weight: 500;
}

/* Navbar menu items svg */
.nextra-navbar nav > div:nth-child(2) > * svg {
  display: none;
}

@media (max-width: 1024px) {
  .nextra-navbar nav > div:nth-child(2) {
    position: relative;
    left: 0;
    transform: translateX(0);
  }
}

/* Hack for Tabs / Nextra auto-inserted h3s */
div[role="tabpanel"] > h3:first-child {
  display: none;
}

/*****Main Content******/

aside {
  --button-gap: 0.38rem;
  --icon-position: 11.5px;
}

article main a {
  font-weight: 500;
  &:is(html[class~="light"] *) {
    --x-color-primary-600: var(--light-color-text-6);
    text-decoration-thickness: 1px;
    text-decoration-line: underline;
    text-decoration-color: var(--light-green-muted);
  }
}

article {
  &:is(html[class~="light"] *) {
    --x-color-gray-400: var(--light-color-text-3);
  }

  &:is(html[class~="dark"] *) {
    --x-color-gray-400: var(--color-el-3);
  }
}

aside + div {
  --nextra-content-width: 1220px;
}

article span.m-tag {
  display: none;
}

article a:not(.showcase-link) {
  text-underline-offset: 1px;
  display: inline-flex;
  align-items: center;
}

article strong > a {
  font-weight: 500;
}

.nextra-breadcrumb {
  &:is(html[class~="light"] *) {
    --x-color-gray-700: var(--light-color-text-5);
  }
  margin-bottom: 1.12rem;
  width: fit-content;
  border-radius: 0.375rem;
}

pre code {
  --x-text-sm: 13px;
  &:is(html[class~="light"] *) {
    --x-color-gray-500: var(--light-color-text-2);
  }

  &:is(html[class~="dark"] *) {
    --x-color-gray-500: var(--color-el-2);
  }
}

pre,
div:has(+ pre) {
  &:is(html[class~="light"] *) {
    border: 0.5px solid var(--light-border-muted);
    background: var(--light-color-surface-2);
  }

  &:is(html[class~="dark"] *) {
    border: 0.5px solid var(--border);
    background: var(--tertiary-bg);
  }
  border-radius: 0.5rem;
  box-shadow: none;
}

div:has(+ pre) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

div + pre {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

button[title="Copy code"] {
  border: none;
}

pre code.nextra-code:not([class*="twoslash-"]) > span[data-highlighted-line] {
  --bg-color: var(--light-color-surface-5);
  background-color: color-mix(in oklab, var(--bg-color) 90%, transparent);

  &:is(html[class~="dark"] *) {
    background-color: var(--color-surface-5);
  }
}

.nextra-code {
  white-space: nowrap;
}

.nextra-code > span {
  white-space-collapse: preserve;
}

/*****Sidebar******/
aside {
  &:is(html[class~="light"] *) {
    border-right: 0.5px solid var(--light-border-muted);
  }

  &:is(html[class~="dark"] *) {
    border-right: 0.5px solid var(--border);
  }
}

aside > * {
  &:is(html[class~="light"] *) {
    --x-color-primary-600: var(--light-color-text-5);
    --x-color-primary-400: var(--light-color-text-4);
  }

  &:is(html[class~="dark"] *) {
    --x-color-primary-600: #fff;
    --x-color-primary-400: oklab(
      18.2% -0.000000000000000147 0.0000000000000000204
    );
  }
}

aside > div:first-child button {
  font-weight: 500;

  &:is(html[class~="light"] *) {
    color: var(--light-color-text-6);
  }

  &:is(html[class~="dark"] *) {
    color: var(--color-primary);
  }
}

/*Size of chevron*/
aside > div:nth-child(1) button svg {
  width: 1rem;
  height: 1rem;
}

aside > div:nth-child(1) ul:before {
  inset-inline-start: calc(var(--x-spacing) * 0.92);
}

/*Active link indicator*/
aside > div:nth-child(1) ul li {
  position: relative;
}

aside > div:nth-child(1) ul li a {
  padding-block: 0.25rem;
  background: transparent;

  &:is(html[class~="light"] *) {
    --x-color-primary-800: var(--light-color-text-6);
  }

  &:hover {
    background: transparent;
  }
}

aside > div:nth-child(1) ul li.active:has(> a) {
  &::before {
    content: "";
    position: absolute;
    left: -8px;
    top: 0px;
    bottom: 0;
    height: calc(100%);
    background: var(--x-color-primary-600);
    width: 1.5px;
    transition: opacity 0.1s ease-in-out;
  }
}

aside > div:nth-child(1) ul li.active:has(a[href="/docs"]),
aside > div:nth-child(1) ul li.active:has(a[href="/examples"]),
aside > div:nth-child(1) ul li.active:has(a[href="/guides"]),
aside > div:nth-child(1) ul li.active:has(a[href="/reference"]) {
  &::before {
    content: "";
    background: transparent;
  }
}

article main li:has(ul) ul {
  margin-top: calc(var(--x-spacing) * 2);
}

aside a[href="/docs"],
aside button[data-href="/docs/reference"],
aside a[href="/examples"],
aside a[href="/guides"],
aside a[href="/reference"] {
  position: relative;
  padding-inline-start: calc((var(--button-gap) * 3) + var(--icon-position));
}

aside a[href="/docs"]::before,
aside button[data-href="/docs/reference"]::before,
aside a[href="/examples"]::before,
aside a[href="/guides"]::before,
aside a[href="/reference"]::before {
  content: "";
  position: absolute;
  left: var(--icon-position);
  top: 50%;
  transform: translateY(-50%);
  width: 0.7rem;
  height: 0.8rem;
  background-size: contain;
  background-repeat: no-repeat;
}

aside a[href="/docs"]::before,
aside a[href="/examples"]::before,
aside a[href="/guides"]::before {
  background-image: url("icons/introduction.svg");
}

aside button[data-href="/docs/reference"]::before,
aside a[href="/reference"]::before {
  background-image: url("icons/reference.svg");
}

button[data-href="/docs/reference"] svg {
  display: none;
}

button[title="Collapse sidebar"] {
  display: none;
}

aside > div:nth-child(2) {
  display: flex;
  &:is(html[class~="light"] *) {
    --x-color-nextra-bg: var(--light-color-surface-15);
  }

  &:is(html[class~="dark"] *) {
    --x-color-nextra-bg: var(--primary-bg);
  }
}

aside > div:nth-child(1) button {
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: var(--button-gap);
}

/*** Table of Contents ***/

.nextra-toc > * {
  &:is(html[class~="light"] *) {
    --x-color-gray-400: var(--light-color-text-3);
  }

  &:is(html[class~="dark"] *) {
    --x-color-gray-400: var(--color-el-3);
  }
}

.nextra-toc div > p {
  position: relative;
  color: var(--color-el-3);
  font-weight: 500;
  padding-inline-start: 2.24rem;

  &::before {
    content: "";
    position: absolute;
    left: 12px;
    bottom: 1;
    width: 1rem;
    height: 1rem;
    background-size: contain;
    background-repeat: no-repeat;
    background-image: url("icons/on-this-page.svg");
  }
}

.nextra-toc ul a {
  --x-font-weight-semibold: 400;

  &:is(html[class~="light"] *) {
    --x-color-gray-400: var(--light-color-text-3);
    --x-color-primary-600: var(--light-color-text-6);
    --x-color-gray-600: var(--light-color-text-4);
  }

  &:is(html[class~="dark"] *) {
    --x-color-gray-400: var(--color-el-3);
    --x-color-primary-600: var(--color-primary);
    --x-color-gray-600: var(--color-el-3);
  }
}

.showcase-item > * {
  &:hover {
    text-decoration: none;
  }
}

/** one off utility **/

a:focus-visible {
  border-radius: 4px;
}

/* Tab selector */

div[role="tablist"] button {
  &:is(html[class~="light"] *):not([aria-selected="true"]) {
    --x-color-primary-600: var(--light-color-text-3);
  }
}

div[role="tablist"] button[aria-selected="true"] {
  &:is(html[class~="light"] *) {
    --x-color-primary-600: var(--light-color-text-6);
    border-color: hsla(125, 66%, 50%, 1);
  }
}

div:has(> time) {
  font-family: var(--font-serif);
}

/* tables */
table {
  border: none;
  --x-color-gray-300: var(--border);
  --x-color-gray-600: var(--border);
}

table:not(:has(caption)) * {
  border: none;
  background: transparent;
}

thead th {
  text-align: left;
  color: var(--color-text-icon-6);
}

table thead tr {
  border-bottom: 1px solid var(--border);
}

table tbody tr {
  border-bottom: 1px solid var(--border);
}

table tbody td {
  padding-block: 0.8rem;
  color: var(--color-text-icon-5);
}

table td code.nextra-code:not([class*="twoslash-"]) {
  border: 1px solid var(--border-2);
  border-radius: var(--border-radius-lg);
  background: transparent;
  padding-inline: 0.62rem;
  padding-block: 0.31rem 0.44rem;
  font-size: 0.875rem;
  height: 1.625rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
}

@media screen and (min-width: 1024px) {
  button[title="Change theme"] {
    display: none;
  }
}

.diff.remove {
  background: oklch(0.65 0.22 16.44 / 0.16);
  --content-color: var(--light-color-text-4);

  &:is(html[class~="dark"] *) {
    --content-color: white;
  }

  &::after {
    content: "-";
    position: absolute;
    left: 2.6rem;
    color: var(--content-color);
  }
}

.diff.add {
  background: var(--diff-color);
  --content-color: var(--light-color-text-4);

  &:is(html[class~="dark"] *) {
    --content-color: white;
  }

  &::after {
    content: "+";
    position: absolute;
    left: 2.6rem;
    color: var(--content-color);
  }
}
