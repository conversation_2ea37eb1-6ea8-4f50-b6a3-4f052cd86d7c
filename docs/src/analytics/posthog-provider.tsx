"use client";

import posthog from "posthog-js";
import { PostHog<PERSON><PERSON>ider as <PERSON><PERSON><PERSON>ider } from "posthog-js/react";
import { useEffect } from "react";
import SuspendedPostHogPageView from "./posthog-page-view";

const isDev = process.env.NODE_ENV === "development";

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
      api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST!,
      capture_pageleave: true,
      persistence: "cookie",
      loaded: () => {
        if (isDev) posthog.debug();
      },
    });
  }, []);

  return (
    <PHProvider client={posthog}>
      <SuspendedPostHogPageView />
      {children}
    </PHProvider>
  );
}
