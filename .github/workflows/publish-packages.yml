name: Prerelease

permissions:
  contents: write
  pull-requests: write

on:
  workflow_dispatch:

jobs:
  release:
    if: ${{ github.repository == 'mastra-ai/mastra' && (contains(github.head_ref || github.ref_name, 'changeset-release') || github.event_name == 'workflow_dispatch') }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          # Fetch entire git history so  Changesets can generate changelogs
          # with the correct commits
          fetch-depth: 0

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Setup npm auth
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > .npmrc

      - name: Run build
        run: pnpm build

      - name: Publish packages
        run: pnpm publish -r --tag alpha --access public
