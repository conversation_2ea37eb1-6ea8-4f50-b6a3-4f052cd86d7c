name: Tool Builder Tests

on:
  pull_request:
    branches: [main]
    paths:
      - 'packages/core/src/tools/tool-builder/**'
      - 'packages/schema-compat/**'
      - '.github/workflows/tool-builder-test.yml'

jobs:
  test:
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build core
        run: pnpm build:core && pnpm build:deployer

      - name: Run tool-builder test
        run: pnpm test:tool-builder
        env:
          NODE_OPTIONS: '--max_old_space_size=8096'
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
