name: RAG Tests

on:
  pull_request:
    branches: [main]
    paths:
      - 'packages/rag/**'
      - '.github/workflows/test-rag.yml'

jobs:
  test:
    # Only run on the main repository, not on forks
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build core
        run: pnpm build:core && pnpm build:deployer

      - name: Build rag
        run: pnpm turbo --filter "@mastra/rag" build

      - name: Run RAG tests
        run: pnpm test:rag
        env:
          NODE_OPTIONS: '--max_old_space_size=8096'
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          COHERE_API_KEY: ${{ secrets.COHERE_API_KEY }}
