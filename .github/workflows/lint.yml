name: Quality assurance

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [main]

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    # Run on main repository or for trusted bot PRs
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:rw
    permissions:
      pull-requests: read

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Lint
        run: pnpm turbo --filter "!./examples/**/*" --filter "!./docs/**/*" --filter "!./integrations/**/*" --filter "!@mastra/playground" lint
      - name: Format
        run: pnpm prettier --check .

  prebuild:
    name: Prebuild
    runs-on: ubuntu-latest
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:rw
    permissions:
      pull-requests: read

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm turbo --filter "!./examples/**/*" --filter "!./docs/" build

  check-bundle:
    name: Validate build outputs
    runs-on: ubuntu-latest
    # Run on main repository or for trusted bot PRs
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r
    permissions:
      pull-requests: read

    needs: prebuild

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm turbo --filter "!./examples/**/*" --filter "!./docs/" build

      - name: Install test dependencies
        working-directory: ./e2e-tests/pkg-outputs
        run: pnpm install --ignore-workspace

      - name: Check bundles
        working-directory: ./e2e-tests/pkg-outputs
        run: pnpm test

  unit:
    name: Unit tests
    runs-on: ubuntu-latest
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r

    needs: prebuild

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm build

      - name: Test
        run: pnpm turbo test --affected --concurrency 1

  e2e-monorepo:
    name: E2E monorepo
    runs-on: ubuntu-latest
    timeout-minutes: 15
    # Run on main repository or for trusted bot PRs
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r

    permissions:
      pull-requests: read

    needs: prebuild

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm turbo --filter "./packages/core" --filter "./packages/cli" build

      - name: Install e2e test dependencies
        working-directory: ./e2e-tests/monorepo
        run: pnpm install --ignore-workspace

      - name: Test
        working-directory: ./e2e-tests/monorepo
        run: pnpm test
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

  e2e-create-mastra:
    name: E2E create-mastra
    runs-on: ubuntu-latest
    timeout-minutes: 15
    # Run on main repository or for trusted bot PRs
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r

    permissions:
      pull-requests: read

    needs: prebuild

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm turbo --filter "create-mastra" build

      - name: Install e2e test dependencies
        working-directory: ./e2e-tests/create-mastra
        run: pnpm install --ignore-workspace

      - name: Test
        working-directory: ./e2e-tests/create-mastra
        run: pnpm test

  e2e-commonjs:
    name: E2E CommonJS
    runs-on: ubuntu-latest
    timeout-minutes: 15
    # Run on main repository or for trusted bot PRs
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r

    permissions:
      pull-requests: read

    needs: prebuild

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm turbo --filter "@mastra/core" --filter "@mastra/loggers" build

      - name: Install e2e test dependencies
        working-directory: ./e2e-tests/commonjs
        run: pnpm install --ignore-workspace

      - name: Test
        working-directory: ./e2e-tests/commonjs
        run: pnpm test

  e2e-kitchen-sink:
    name: E2E kitchen-sink
    runs-on: ubuntu-latest
    timeout-minutes: 15
    # Run on main repository or for trusted bot PRs
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:r

    permissions:
      pull-requests: read

    needs: prebuild

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm build

      - name: Install e2e test dependencies
        working-directory: ./e2e-tests/kitchen-sink
        run: pnpm install --ignore-workspace

      - name: Install Playwright browsers
        working-directory: ./e2e-tests/kitchen-sink
        run: pnpm test:e2e:setup

      - name: Test E2E
        working-directory: ./e2e-tests/kitchen-sink
        run: pnpm test:e2e
