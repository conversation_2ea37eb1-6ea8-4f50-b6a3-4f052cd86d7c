name: Combined store Tests (vector+storage)

on:
  pull_request:
    branches: [main]
    paths:
      - 'stores/**'
      - 'packages/core/**'
      - 'stores/*/package.json'
      - '.github/workflows/test-combined-stores.yml'

permissions:
  contents: read
  pull-requests: read

jobs:
  setup:
    # Only run on the main repository, not on forks
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    outputs:
      stores: ${{ steps.set-stores.outputs.stores }}
    steps:
      - uses: actions/checkout@v4
      - id: set-stores
        run: |
          STORES=$(ls -d stores/*/ | grep -v '_test-utils' | cut -f2 -d'/' | jq -R -s -c 'split("\n")[:-1]')
          echo "stores=$STORES" >> $GITHUB_OUTPUT
  test:
    needs: setup
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    permissions:
      contents: read
    strategy:
      fail-fast: false
      matrix:
        store: ${{from<PERSON><PERSON>(needs.setup.outputs.stores)}}
    services:
      qdrant:
        image: ${{ matrix.store == 'qdrant' && 'qdrant/qdrant' || '' }}
        ports:
          - 6333:6333
      chromadb:
        image: ${{ matrix.store == 'chroma' && 'chromadb/chroma' || '' }}
        ports:
          - 8000:8000

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build combined storage packages
        run: pnpm turbo --filter "@mastra/${{ matrix.store }}" build

      - name: Run ${{ matrix.store }} tests
        run: pnpm test
        working-directory: stores/${{ matrix.store }}
        env:
          NODE_OPTIONS: '--max_old_space_size=8096'
          PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY }}
          ASTRA_DB_ENDPOINT: ${{ secrets.ASTRA_DB_ENDPOINT }}
          ASTRA_DB_TOKEN: ${{ secrets.ASTRA_DB_TOKEN }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.ABHI_CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.ABHI_CLOUDFLARE_ACCOUNT_ID }}
