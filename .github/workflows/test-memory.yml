name: Memory Tests

on:
  pull_request:
    branches: [main]
    paths:
      - 'packages/memory/**'
      - 'packages/memory/*/package.json'
      - 'stores/**'
      - 'stores/*/package.json'
      - 'packages/core/**'
      - 'packages/deployer/**'
      - 'packages/server/**'
      - 'client-sdks/client-js/**'
      - '.github/workflows/test-memory.yml'

jobs:
  test:
    # Only run on the main repository, not on forks
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      fail-fast: false
      matrix:
        suite:
          - pg
          - upstash
          - libsql
          - streaming
          - working-memory
          - agent-memory
          - processors
    services:
      postgres:
        image: pgvector/pgvector:pg16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: mastra
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build vector+store packages
        run: pnpm turbo build --filter "./stores/*" --filter "mastra" --filter "@mastra/memory" --filter "@mastra/fastembed"

      - name: Compile TypeScript Workers
        run: |
          npx tsc packages/memory/integration-tests/src/worker/generic-memory-worker.ts packages/memory/integration-tests/src/worker/mock-embedder.ts --esModuleInterop --resolveJsonModule --module commonjs --target es2020 --outDir packages/memory/ --rootDir packages/memory/ --skipLibCheck

      - name: List compiled worker directory
        run: ls -R packages/memory/integration-tests/src/worker

      - name: Run Memory ${{ matrix.suite }} tests
        run: pnpm run test:${{ matrix.suite }}
        working-directory: packages/memory/integration-tests
        env:
          NODE_OPTIONS: '--max_old_space_size=8096'
          DB_URL: 'postgresql://postgres:postgres@localhost:5432/mastra'
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          KV_REST_API_URL: ${{ secrets.KV_REST_API_URL }}
          KV_REST_API_TOKEN: ${{ secrets.KV_REST_API_TOKEN }}
          GOOGLE_GENERATIVE_AI_API_KEY: ${{ secrets.GOOGLE_GENERATIVE_AI_API_KEY }}
