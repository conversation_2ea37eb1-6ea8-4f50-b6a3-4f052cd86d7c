name: Integration Spec Writer

# on:
#   pull_request:
#     branches:
#       # You can specify branch patterns here
#       - "open-api-spec-writer/**"
#     types: [opened, synchronize, reopened]

on:
  pull_request: # Keep this as backup
    types: [opened, reopened, synchronize]
    branches:
      - '**'
jobs:
  process-and-commit:
    runs-on: ubuntu-latest
    if: |
      github.repository == 'mastra-ai/mastra' &&
      (github.event_name == 'pull_request' || github.event_name == 'pull_request_target') &&
      startsWith(github.head_ref, 'open-api-spec-writer/')
    permissions:
      contents: write
      pull-requests: write

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }} # Checkout the PR branch
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.19.1' # Adjust version as needed
          cache: 'npm'
          cache-dependency-path: './integration-generator/package-lock.json'

      - name: Install dependencies
        working-directory: ./integration-generator
        run: npm ci # Uses package-lock.json for exact versions

      - name: Extract branch name
        id: extract_branch
        run: |
          # Extract branch name and remove 'refs/heads/' prefix if present
          BRANCH_NAME=${GITHUB_HEAD_REF#refs/heads/}
          echo "Branch name: $BRANCH_NAME"
          echo "branch=$BRANCH_NAME" >> $GITHUB_OUTPUT

      - name: Run your script
        run: |
          # Add your script commands here
          # For example:
          # python ./your-script.py
          # or
          # bash ./your-script.sh

          BRANCH_TYPE=$(echo "${{ steps.extract_branch.outputs.branch }}" | cut -d'/' -f1)
          BRANCH_VALUE=$(echo "${{ steps.extract_branch.outputs.branch }}" | cut -d'/' -f2-)

          echo "Branch type: $BRANCH_TYPE"
          echo "Branch value: $BRANCH_VALUE"

          npx tsx ./integration-generator/writer.ts --branch=$BRANCH_VALUE

      - name: Commit changes
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add .
          git diff --staged --quiet || git commit -m "Auto-update from GitHub Actions"

      - name: Push changes
        run: git push

      - name: Comment PR
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🔄 Integration generator has run on this PR'
            })
