name: GH_Issue_Triage
on:
  issues:
    types: [opened, reopened]

jobs:
  issue_created:
    # Only run on the main repository, not on forks
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    permissions:
      issues: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1

      - name: Install dependencies
        run: npm install
        working-directory: scripts/gh-issue-triage

      - name: Label Issue
        run: npx tsx index.ts
        working-directory: scripts/gh-issue-triage
        env:
          GITHUB_PERSONAL_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OWNER: ${{ github.repository_owner }}
          REPO: ${{ github.event.repository.name }}
          ISSUE_NUMBER: ${{ github.event.issue.number }}
          MASTRA_BASE_URL: 'https://few-substantial-engine.mastra.cloud'
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_TEAM_ID: ${{ secrets.SLACK_TEAM_ID }}
          CHANNEL_ID: ${{ secrets.CHANNEL_ID }}
