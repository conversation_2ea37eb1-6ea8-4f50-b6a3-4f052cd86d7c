{"action": "opened", "number": 1435, "pull_request": {"url": "https://api.github.com/repos/mastra-ai/mastra/pulls/1435", "id": 2288330436, "node_id": "PR_kwDOMgKwqs6IZSbE", "html_url": "https://github.com/mastra-ai/mastra/pull/1435", "diff_url": "https://github.com/mastra-ai/mastra/pull/1435.diff", "patch_url": "https://github.com/mastra-ai/mastra/pull/1435.patch", "issue_url": "https://api.github.com/repos/mastra-ai/mastra/issues/1435", "number": 1435, "state": "open", "locked": false, "title": "feat: add support first contributor", "user": {"login": "wardpeet", "id": 1120926, "node_id": "MDQ6VXNlcjExMjA5MjY=", "avatar_url": "https://avatars.githubusercontent.com/u/1120926?v=4", "gravatar_id": "", "url": "https://api.github.com/users/wardpeet", "html_url": "https://github.com/wardpeet", "followers_url": "https://api.github.com/users/wardpeet/followers", "following_url": "https://api.github.com/users/wardpeet/following{/other_user}", "gists_url": "https://api.github.com/users/wardpeet/gists{/gist_id}", "starred_url": "https://api.github.com/users/wardpeet/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/wardpeet/subscriptions", "organizations_url": "https://api.github.com/users/wardpeet/orgs", "repos_url": "https://api.github.com/users/wardpeet/repos", "events_url": "https://api.github.com/users/wardpeet/events{/privacy}", "received_events_url": "https://api.github.com/users/wardpeet/received_events", "type": "User", "user_view_type": "public", "site_admin": false}, "body": null, "created_at": "2025-01-20T17:40:05Z", "updated_at": "2025-01-20T17:40:05Z", "closed_at": null, "merged_at": null, "merge_commit_sha": "5b8f9581ac7a32ffd2b40df92f49a42ce7f68fb8", "assignee": null, "assignees": [], "requested_reviewers": [], "requested_teams": [], "labels": [], "milestone": null, "draft": false, "commits_url": "https://api.github.com/repos/mastra-ai/mastra/pulls/1435/commits", "review_comments_url": "https://api.github.com/repos/mastra-ai/mastra/pulls/1435/comments", "review_comment_url": "https://api.github.com/repos/mastra-ai/mastra/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/mastra-ai/mastra/issues/1435/comments", "statuses_url": "https://api.github.com/repos/mastra-ai/mastra/statuses/8a4ea25e1bbb2b38668255bd4463c592804564dc", "head": {"label": "mastra-ai:dane-new-contributor", "ref": "dane-new-contributor", "sha": "8a4ea25e1bbb2b38668255bd4463c592804564dc", "user": {"login": "mastra-ai", "id": 149120496, "node_id": "O_kgDOCONl8A", "avatar_url": "https://avatars.githubusercontent.com/u/149120496?v=4", "gravatar_id": "", "url": "https://api.github.com/users/mastra-ai", "html_url": "https://github.com/mastra-ai", "followers_url": "https://api.github.com/users/mastra-ai/followers", "following_url": "https://api.github.com/users/mastra-ai/following{/other_user}", "gists_url": "https://api.github.com/users/mastra-ai/gists{/gist_id}", "starred_url": "https://api.github.com/users/mastra-ai/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/mastra-ai/subscriptions", "organizations_url": "https://api.github.com/users/mastra-ai/orgs", "repos_url": "https://api.github.com/users/mastra-ai/repos", "events_url": "https://api.github.com/users/mastra-ai/events{/privacy}", "received_events_url": "https://api.github.com/users/mastra-ai/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "repo": {"id": 839037098, "node_id": "R_kgDOMgKwqg", "name": "mastra", "full_name": "mastra-ai/mastra", "private": false, "owner": {"login": "mastra-ai", "id": 149120496, "node_id": "O_kgDOCONl8A", "avatar_url": "https://avatars.githubusercontent.com/u/149120496?v=4", "gravatar_id": "", "url": "https://api.github.com/users/mastra-ai", "html_url": "https://github.com/mastra-ai", "followers_url": "https://api.github.com/users/mastra-ai/followers", "following_url": "https://api.github.com/users/mastra-ai/following{/other_user}", "gists_url": "https://api.github.com/users/mastra-ai/gists{/gist_id}", "starred_url": "https://api.github.com/users/mastra-ai/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/mastra-ai/subscriptions", "organizations_url": "https://api.github.com/users/mastra-ai/orgs", "repos_url": "https://api.github.com/users/mastra-ai/repos", "events_url": "https://api.github.com/users/mastra-ai/events{/privacy}", "received_events_url": "https://api.github.com/users/mastra-ai/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/mastra-ai/mastra", "description": "The TypeScript AI framework.", "fork": false, "url": "https://api.github.com/repos/mastra-ai/mastra", "forks_url": "https://api.github.com/repos/mastra-ai/mastra/forks", "keys_url": "https://api.github.com/repos/mastra-ai/mastra/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/mastra-ai/mastra/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/mastra-ai/mastra/teams", "hooks_url": "https://api.github.com/repos/mastra-ai/mastra/hooks", "issue_events_url": "https://api.github.com/repos/mastra-ai/mastra/issues/events{/number}", "events_url": "https://api.github.com/repos/mastra-ai/mastra/events", "assignees_url": "https://api.github.com/repos/mastra-ai/mastra/assignees{/user}", "branches_url": "https://api.github.com/repos/mastra-ai/mastra/branches{/branch}", "tags_url": "https://api.github.com/repos/mastra-ai/mastra/tags", "blobs_url": "https://api.github.com/repos/mastra-ai/mastra/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/mastra-ai/mastra/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/mastra-ai/mastra/git/refs{/sha}", "trees_url": "https://api.github.com/repos/mastra-ai/mastra/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/mastra-ai/mastra/statuses/{sha}", "languages_url": "https://api.github.com/repos/mastra-ai/mastra/languages", "stargazers_url": "https://api.github.com/repos/mastra-ai/mastra/stargazers", "contributors_url": "https://api.github.com/repos/mastra-ai/mastra/contributors", "subscribers_url": "https://api.github.com/repos/mastra-ai/mastra/subscribers", "subscription_url": "https://api.github.com/repos/mastra-ai/mastra/subscription", "commits_url": "https://api.github.com/repos/mastra-ai/mastra/commits{/sha}", "git_commits_url": "https://api.github.com/repos/mastra-ai/mastra/git/commits{/sha}", "comments_url": "https://api.github.com/repos/mastra-ai/mastra/comments{/number}", "issue_comment_url": "https://api.github.com/repos/mastra-ai/mastra/issues/comments{/number}", "contents_url": "https://api.github.com/repos/mastra-ai/mastra/contents/{+path}", "compare_url": "https://api.github.com/repos/mastra-ai/mastra/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/mastra-ai/mastra/merges", "archive_url": "https://api.github.com/repos/mastra-ai/mastra/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/mastra-ai/mastra/downloads", "issues_url": "https://api.github.com/repos/mastra-ai/mastra/issues{/number}", "pulls_url": "https://api.github.com/repos/mastra-ai/mastra/pulls{/number}", "milestones_url": "https://api.github.com/repos/mastra-ai/mastra/milestones{/number}", "notifications_url": "https://api.github.com/repos/mastra-ai/mastra/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/mastra-ai/mastra/labels{/name}", "releases_url": "https://api.github.com/repos/mastra-ai/mastra/releases{/id}", "deployments_url": "https://api.github.com/repos/mastra-ai/mastra/deployments", "created_at": "2024-08-06T20:44:31Z", "updated_at": "2025-01-20T17:37:18Z", "pushed_at": "2025-01-20T17:40:05Z", "git_url": "git://github.com/mastra-ai/mastra.git", "ssh_url": "**************:mastra-ai/mastra.git", "clone_url": "https://github.com/mastra-ai/mastra.git", "svn_url": "https://github.com/mastra-ai/mastra", "homepage": "https://mastra.ai", "size": 110324, "stargazers_count": 1296, "watchers_count": 1296, "language": "TypeScript", "has_issues": true, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 46, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 6, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": ["agents", "ai", "chatbots", "evals", "javascript", "llm", "mcp", "nextjs", "nodejs", "reactjs", "tts", "typescript", "workflows"], "visibility": "public", "forks": 46, "open_issues": 6, "watchers": 1296, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": false, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "base": {"label": "mastra-ai:main", "ref": "main", "sha": "1ecd688506d2ae1528e3090573bbc8b5bf5ba848", "user": {"login": "mastra-ai", "id": 149120496, "node_id": "O_kgDOCONl8A", "avatar_url": "https://avatars.githubusercontent.com/u/149120496?v=4", "gravatar_id": "", "url": "https://api.github.com/users/mastra-ai", "html_url": "https://github.com/mastra-ai", "followers_url": "https://api.github.com/users/mastra-ai/followers", "following_url": "https://api.github.com/users/mastra-ai/following{/other_user}", "gists_url": "https://api.github.com/users/mastra-ai/gists{/gist_id}", "starred_url": "https://api.github.com/users/mastra-ai/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/mastra-ai/subscriptions", "organizations_url": "https://api.github.com/users/mastra-ai/orgs", "repos_url": "https://api.github.com/users/mastra-ai/repos", "events_url": "https://api.github.com/users/mastra-ai/events{/privacy}", "received_events_url": "https://api.github.com/users/mastra-ai/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "repo": {"id": 839037098, "node_id": "R_kgDOMgKwqg", "name": "mastra", "full_name": "mastra-ai/mastra", "private": false, "owner": {"login": "mastra-ai", "id": 149120496, "node_id": "O_kgDOCONl8A", "avatar_url": "https://avatars.githubusercontent.com/u/149120496?v=4", "gravatar_id": "", "url": "https://api.github.com/users/mastra-ai", "html_url": "https://github.com/mastra-ai", "followers_url": "https://api.github.com/users/mastra-ai/followers", "following_url": "https://api.github.com/users/mastra-ai/following{/other_user}", "gists_url": "https://api.github.com/users/mastra-ai/gists{/gist_id}", "starred_url": "https://api.github.com/users/mastra-ai/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/mastra-ai/subscriptions", "organizations_url": "https://api.github.com/users/mastra-ai/orgs", "repos_url": "https://api.github.com/users/mastra-ai/repos", "events_url": "https://api.github.com/users/mastra-ai/events{/privacy}", "received_events_url": "https://api.github.com/users/mastra-ai/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/mastra-ai/mastra", "description": "The TypeScript AI framework.", "fork": false, "url": "https://api.github.com/repos/mastra-ai/mastra", "forks_url": "https://api.github.com/repos/mastra-ai/mastra/forks", "keys_url": "https://api.github.com/repos/mastra-ai/mastra/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/mastra-ai/mastra/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/mastra-ai/mastra/teams", "hooks_url": "https://api.github.com/repos/mastra-ai/mastra/hooks", "issue_events_url": "https://api.github.com/repos/mastra-ai/mastra/issues/events{/number}", "events_url": "https://api.github.com/repos/mastra-ai/mastra/events", "assignees_url": "https://api.github.com/repos/mastra-ai/mastra/assignees{/user}", "branches_url": "https://api.github.com/repos/mastra-ai/mastra/branches{/branch}", "tags_url": "https://api.github.com/repos/mastra-ai/mastra/tags", "blobs_url": "https://api.github.com/repos/mastra-ai/mastra/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/mastra-ai/mastra/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/mastra-ai/mastra/git/refs{/sha}", "trees_url": "https://api.github.com/repos/mastra-ai/mastra/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/mastra-ai/mastra/statuses/{sha}", "languages_url": "https://api.github.com/repos/mastra-ai/mastra/languages", "stargazers_url": "https://api.github.com/repos/mastra-ai/mastra/stargazers", "contributors_url": "https://api.github.com/repos/mastra-ai/mastra/contributors", "subscribers_url": "https://api.github.com/repos/mastra-ai/mastra/subscribers", "subscription_url": "https://api.github.com/repos/mastra-ai/mastra/subscription", "commits_url": "https://api.github.com/repos/mastra-ai/mastra/commits{/sha}", "git_commits_url": "https://api.github.com/repos/mastra-ai/mastra/git/commits{/sha}", "comments_url": "https://api.github.com/repos/mastra-ai/mastra/comments{/number}", "issue_comment_url": "https://api.github.com/repos/mastra-ai/mastra/issues/comments{/number}", "contents_url": "https://api.github.com/repos/mastra-ai/mastra/contents/{+path}", "compare_url": "https://api.github.com/repos/mastra-ai/mastra/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/mastra-ai/mastra/merges", "archive_url": "https://api.github.com/repos/mastra-ai/mastra/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/mastra-ai/mastra/downloads", "issues_url": "https://api.github.com/repos/mastra-ai/mastra/issues{/number}", "pulls_url": "https://api.github.com/repos/mastra-ai/mastra/pulls{/number}", "milestones_url": "https://api.github.com/repos/mastra-ai/mastra/milestones{/number}", "notifications_url": "https://api.github.com/repos/mastra-ai/mastra/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/mastra-ai/mastra/labels{/name}", "releases_url": "https://api.github.com/repos/mastra-ai/mastra/releases{/id}", "deployments_url": "https://api.github.com/repos/mastra-ai/mastra/deployments", "created_at": "2024-08-06T20:44:31Z", "updated_at": "2025-01-20T17:37:18Z", "pushed_at": "2025-01-20T17:40:05Z", "git_url": "git://github.com/mastra-ai/mastra.git", "ssh_url": "**************:mastra-ai/mastra.git", "clone_url": "https://github.com/mastra-ai/mastra.git", "svn_url": "https://github.com/mastra-ai/mastra", "homepage": "https://mastra.ai", "size": 110324, "stargazers_count": 1296, "watchers_count": 1296, "language": "TypeScript", "has_issues": true, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 46, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 6, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": ["agents", "ai", "chatbots", "evals", "javascript", "llm", "mcp", "nextjs", "nodejs", "reactjs", "tts", "typescript", "workflows"], "visibility": "public", "forks": 46, "open_issues": 6, "watchers": 1296, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": false, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "_links": {"self": {"href": "https://api.github.com/repos/mastra-ai/mastra/pulls/1435"}, "html": {"href": "https://github.com/mastra-ai/mastra/pull/1435"}, "issue": {"href": "https://api.github.com/repos/mastra-ai/mastra/issues/1435"}, "comments": {"href": "https://api.github.com/repos/mastra-ai/mastra/issues/1435/comments"}, "review_comments": {"href": "https://api.github.com/repos/mastra-ai/mastra/pulls/1435/comments"}, "review_comment": {"href": "https://api.github.com/repos/mastra-ai/mastra/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/mastra-ai/mastra/pulls/1435/commits"}, "statuses": {"href": "https://api.github.com/repos/mastra-ai/mastra/statuses/8a4ea25e1bbb2b38668255bd4463c592804564dc"}}, "author_association": "CONTRIBUTOR", "auto_merge": null, "active_lock_reason": null, "merged": false, "mergeable": true, "rebaseable": true, "mergeable_state": "clean", "merged_by": null, "comments": 0, "review_comments": 0, "maintainer_can_modify": false, "commits": 1, "additions": 58, "deletions": 1, "changed_files": 4}, "repository": {"id": 839037098, "node_id": "R_kgDOMgKwqg", "name": "mastra", "full_name": "mastra-ai/mastra", "private": false, "owner": {"login": "mastra-ai", "id": 149120496, "node_id": "O_kgDOCONl8A", "avatar_url": "https://avatars.githubusercontent.com/u/149120496?v=4", "gravatar_id": "", "url": "https://api.github.com/users/mastra-ai", "html_url": "https://github.com/mastra-ai", "followers_url": "https://api.github.com/users/mastra-ai/followers", "following_url": "https://api.github.com/users/mastra-ai/following{/other_user}", "gists_url": "https://api.github.com/users/mastra-ai/gists{/gist_id}", "starred_url": "https://api.github.com/users/mastra-ai/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/mastra-ai/subscriptions", "organizations_url": "https://api.github.com/users/mastra-ai/orgs", "repos_url": "https://api.github.com/users/mastra-ai/repos", "events_url": "https://api.github.com/users/mastra-ai/events{/privacy}", "received_events_url": "https://api.github.com/users/mastra-ai/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/mastra-ai/mastra", "description": "The TypeScript AI framework.", "fork": false, "url": "https://api.github.com/repos/mastra-ai/mastra", "forks_url": "https://api.github.com/repos/mastra-ai/mastra/forks", "keys_url": "https://api.github.com/repos/mastra-ai/mastra/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/mastra-ai/mastra/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/mastra-ai/mastra/teams", "hooks_url": "https://api.github.com/repos/mastra-ai/mastra/hooks", "issue_events_url": "https://api.github.com/repos/mastra-ai/mastra/issues/events{/number}", "events_url": "https://api.github.com/repos/mastra-ai/mastra/events", "assignees_url": "https://api.github.com/repos/mastra-ai/mastra/assignees{/user}", "branches_url": "https://api.github.com/repos/mastra-ai/mastra/branches{/branch}", "tags_url": "https://api.github.com/repos/mastra-ai/mastra/tags", "blobs_url": "https://api.github.com/repos/mastra-ai/mastra/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/mastra-ai/mastra/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/mastra-ai/mastra/git/refs{/sha}", "trees_url": "https://api.github.com/repos/mastra-ai/mastra/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/mastra-ai/mastra/statuses/{sha}", "languages_url": "https://api.github.com/repos/mastra-ai/mastra/languages", "stargazers_url": "https://api.github.com/repos/mastra-ai/mastra/stargazers", "contributors_url": "https://api.github.com/repos/mastra-ai/mastra/contributors", "subscribers_url": "https://api.github.com/repos/mastra-ai/mastra/subscribers", "subscription_url": "https://api.github.com/repos/mastra-ai/mastra/subscription", "commits_url": "https://api.github.com/repos/mastra-ai/mastra/commits{/sha}", "git_commits_url": "https://api.github.com/repos/mastra-ai/mastra/git/commits{/sha}", "comments_url": "https://api.github.com/repos/mastra-ai/mastra/comments{/number}", "issue_comment_url": "https://api.github.com/repos/mastra-ai/mastra/issues/comments{/number}", "contents_url": "https://api.github.com/repos/mastra-ai/mastra/contents/{+path}", "compare_url": "https://api.github.com/repos/mastra-ai/mastra/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/mastra-ai/mastra/merges", "archive_url": "https://api.github.com/repos/mastra-ai/mastra/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/mastra-ai/mastra/downloads", "issues_url": "https://api.github.com/repos/mastra-ai/mastra/issues{/number}", "pulls_url": "https://api.github.com/repos/mastra-ai/mastra/pulls{/number}", "milestones_url": "https://api.github.com/repos/mastra-ai/mastra/milestones{/number}", "notifications_url": "https://api.github.com/repos/mastra-ai/mastra/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/mastra-ai/mastra/labels{/name}", "releases_url": "https://api.github.com/repos/mastra-ai/mastra/releases{/id}", "deployments_url": "https://api.github.com/repos/mastra-ai/mastra/deployments", "created_at": "2024-08-06T20:44:31Z", "updated_at": "2025-01-20T17:37:18Z", "pushed_at": "2025-01-20T17:40:05Z", "git_url": "git://github.com/mastra-ai/mastra.git", "ssh_url": "**************:mastra-ai/mastra.git", "clone_url": "https://github.com/mastra-ai/mastra.git", "svn_url": "https://github.com/mastra-ai/mastra", "homepage": "https://mastra.ai", "size": 110324, "stargazers_count": 1296, "watchers_count": 1296, "language": "TypeScript", "has_issues": true, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 46, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 6, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": ["agents", "ai", "chatbots", "evals", "javascript", "llm", "mcp", "nextjs", "nodejs", "reactjs", "tts", "typescript", "workflows"], "visibility": "public", "forks": 46, "open_issues": 6, "watchers": 1296, "default_branch": "main", "custom_properties": {}}, "organization": {"login": "mastra-ai", "id": 149120496, "node_id": "O_kgDOCONl8A", "url": "https://api.github.com/orgs/mastra-ai", "repos_url": "https://api.github.com/orgs/mastra-ai/repos", "events_url": "https://api.github.com/orgs/mastra-ai/events", "hooks_url": "https://api.github.com/orgs/mastra-ai/hooks", "issues_url": "https://api.github.com/orgs/mastra-ai/issues", "members_url": "https://api.github.com/orgs/mastra-ai/members{/member}", "public_members_url": "https://api.github.com/orgs/mastra-ai/public_members{/member}", "avatar_url": "https://avatars.githubusercontent.com/u/149120496?v=4", "description": ""}, "sender": {"login": "wardpeet", "id": 1120926, "node_id": "MDQ6VXNlcjExMjA5MjY=", "avatar_url": "https://avatars.githubusercontent.com/u/1120926?v=4", "gravatar_id": "", "url": "https://api.github.com/users/wardpeet", "html_url": "https://github.com/wardpeet", "followers_url": "https://api.github.com/users/wardpeet/followers", "following_url": "https://api.github.com/users/wardpeet/following{/other_user}", "gists_url": "https://api.github.com/users/wardpeet/gists{/gist_id}", "starred_url": "https://api.github.com/users/wardpeet/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/wardpeet/subscriptions", "organizations_url": "https://api.github.com/users/wardpeet/orgs", "repos_url": "https://api.github.com/users/wardpeet/repos", "events_url": "https://api.github.com/users/wardpeet/events{/privacy}", "received_events_url": "https://api.github.com/users/wardpeet/received_events", "type": "User", "user_view_type": "public", "site_admin": false}}