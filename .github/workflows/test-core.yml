name: Core Package Tests

on:
  workflow_dispatch:
  pull_request:
    branches: [main]
    paths:
      - 'packages/core/**'
      - '.github/workflows/test-core.yml'

jobs:
  test:
    # Only run on the main repository, not on forks
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build cli
        run: pnpm build:core

      - name: Run Core tests
        run: pnpm test:core
        env:
          NODE_OPTIONS: '--max_old_space_size=8096'
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
