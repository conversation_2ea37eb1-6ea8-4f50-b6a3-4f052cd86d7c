name: Docs MCP Tests

on:
  pull_request:
    branches: [main]
    paths:
      - 'packages/mcp/**'
      - 'packages/mcp-docs-server/**'
      - '.github/workflows/test-docs-mcp.yml'

jobs:
  test:
    # Only run on the main repository, not on forks
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build @mastra/mcp
        run: pnpm turbo --filter="./packages/mcp" build

      - name: Run Docs MCP tests
        run: pnpm test:docs-mcp
