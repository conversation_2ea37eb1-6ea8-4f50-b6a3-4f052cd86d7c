name: Renovate
permissions:
  contents: read
on:
  # This lets you dispatch a renovate job with different cache options if you want to reset or disable the cache manually.
  workflow_dispatch:
    inputs:
      repoCache:
        description: 'Reset or disable the cache?'
        type: choice
        default: enabled
        options:
          - enabled
          - disabled
          - reset
      logLevel:
        description: 'Set logLevel'
        type: choice
        default: INFO
        options:
          - INFO
          - DEBUG
  schedule:
    # The "*" (#42, asterisk) character has special semantics in YAML, so this
    # string has to be quoted.
    - cron: '0 */6 * * *'
# Adding these as env variables makes it easy to re-use them in different steps and in bash.
env:
  cache_archive: renovate_cache.tar.gz
  # This is the dir renovate provides -- if we set our own directory via cacheDir, we can run into permissions issues.
  # It is also possible to cache a higher level of the directory, but it has minimal benefit. While renovate execution
  # time gets faster, it also takes longer to upload the cache as it grows bigger.
  cache_dir: /tmp/renovate/cache/renovate/repository
  # This can be manually changed to bust the cache if neccessary.
  cache_key: renovate-cache

jobs:
  renovate:
    runs-on: ubuntu-latest
    if: github.repository == 'mastra-ai/mastra'
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.DANE_APP_ID }}
          private-key: ${{ secrets.DANE_APP_PRIVATE_KEY }}
          permission-pull-requests: write
          permission-issues: write
          permission-security-events: write
          permission-statuses: write

      - name: Get GitHub App User ID
        id: get-user-id
        run: echo "user-id=$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: false

      # This third party action allows you to download the cache artifact from different workflow runs
      # Note that actions/cache doesn't work well because the cache key would need to be computed from
      # a file within the cache, meaning there would never be any data to restore. With other keys, the
      # cache wouldn't necessarily upload when it changes. actions/download-artifact also doesn't work
      # because it only handles artifacts uploaded in the same run, and we want to restore from the
      # previous successful run.
      - uses: dawidd6/action-download-artifact@v10
        if: github.event.inputs.repoCache != 'disabled'
        continue-on-error: true
        with:
          name: ${{ env.cache_key }}
          path: cache-download

      # Using tar to compress and extract the archive isn't strictly necessary, but it can improve
      # performance significantly when uploading artifacts with lots of files.
      - name: Extract renovate cache
        run: |
          set -x
          mkdir -p $cache_dir
          # Skip if no cache is set, such as the first time it runs.
          if [ ! -d cache-download ] ; then
            echo "No cache found."
            exit 0
          fi

          # Make sure the directory exists, and extract it there. Note that it's nested in the download directory.
          tar -xzf cache-download/$cache_archive -C $cache_dir

          # Unfortunately, the permissions expected within renovate's docker container
          # are different than the ones given after the cache is restored. We have to
          # change ownership to solve this. We also need to have correct permissions in
          # the entire /tmp/renovate tree, not just the section with the repo cache.
          sudo chown -R 12021:0 /tmp/renovate/
          ls -R $cache_dir

      - run: |
          cat <<- EOF > $HOME/.netrc
            machine github.com
            login ${{ steps.app-token.outputs.app-slug }}
            password ${{ steps.app-token.outputs.token }}
            machine api.github.com
            login ${{ steps.app-token.outputs.app-slug }}
            password ${{ steps.app-token.outputs.token }}
          EOF
          git config --global user.name '${{ steps.app-token.outputs.app-slug }}[bot]'
          git config --global user.email '${{ steps.get-user-id.outputs.user-id }}+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com'

      - name: Self-hosted Renovate
        uses: renovatebot/github-action@v42.0.3
        with:
          configurationFile: renovate.json
          docker-user: '${{ steps.id.outputs.user }}:${{ steps.id.outputs.group }}'
          token: ${{ steps.app-token.outputs.token  }}
        env:
          LOG_LEVEL: ${{ github.event.inputs.logLevel }}
          RENOVATE_REPOSITORIES: ${{ github.repository }}
          # This enables the cache -- if this is set, it's not necessary to add it to renovate.json.
          RENOVATE_REPOSITORY_CACHE: ${{ github.event.inputs.repoCache || 'enabled' }}

      # Compression helps performance in the upload step!
      - name: Compress renovate cache
        run: |
          ls $cache_dir
          # The -C is important -- otherwise we end up extracting the files with
          # their full path, ultimately leading to a nested directory situation.
          # To solve *that*, we'd have to extract to root (/), which isn't safe.
          tar -czvf $cache_archive -C $cache_dir .

      - uses: actions/upload-artifact@v4
        if: github.event.inputs.repoCache != 'disabled'
        with:
          name: ${{ env.cache_key }}
          path: ${{ env.cache_archive }}
          # Since this is updated and restored on every run, we don't need to keep it
          # for long. Just make sure this value is large enough that multiple renovate
          # runs can happen before older cache archives are deleted.
          retention-days: 1
