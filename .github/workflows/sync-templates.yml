name: Sync Template Repositories

on:
  push:
    branches:
      - main # Or your default branch
    paths:
      - 'templates/**' # Only trigger when templates change
  workflow_dispatch: # Allow manual triggers

jobs:
  sync-templates:
    name: Sync Template Repositories
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest

    steps:
      - name: Get Github App Token
        uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.DANE_APP_ID }}
          private-key: ${{ secrets.DANE_APP_PRIVATE_KEY }}
          owner: mastra-ai
      - name: Get GitHub App User ID
        id: get-user-id
        run: echo "user-id=$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}

      - run: |
          cat <<- EOF > $HOME/.netrc
            machine github.com
            login ${{ steps.app-token.outputs.app-slug }}
            password ${{ steps.app-token.outputs.token }}
            machine api.github.com
            login ${{ steps.app-token.outputs.app-slug }}
            password ${{ steps.app-token.outputs.token }}
          EOF

      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'

      - name: Install dependencies
        run: npm install @octokit/rest fs-extra dotenv

      - name: Run Template Sync Script
        env:
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
          ORGANIZATION: 'mastra-ai'
          USERNAME: '${{ steps.app-token.outputs.app-slug }}[bot]'
          EMAIL: '${{ steps.get-user-id.outputs.user-id }}+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com'
        run: node .github/scripts/sync-templates.js
