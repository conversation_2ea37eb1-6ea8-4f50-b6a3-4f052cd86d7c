name: Quality assurance

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [main]

jobs:
  validator:
    name: Validate examples packages.json
    runs-on: ubuntu-latest
    # Run on main repository or for trusted bot PRs
    if: ${{ github.repository == 'mastra-ai/mastra' }} && ${{ !contains(github.event.pull_request.files.*.path, 'examples/') && !contains(github.event.pull_request.files.*.path, 'docs/') && !contains(github.event.pull_request.files.*.path, '.changeset/') && !contains(github.event.pull_request.files.*.path, 'generated-changesets/') }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      TURBO_CACHE: remote:rw
    permissions:
      pull-requests: read

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1

      - name: Install dependencies
        run: npm install globby

      - name: Run examples validator
        run: node .github/scripts/validate-examples.js
