name: DANE_Changelog

permissions:
  contents: write
  pull-requests: write

on:
  workflow_dispatch:

jobs:
  changelog:
    services:
      postgres:
        image: pgvector/pgvector:pg16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: mastra
        ports:
          - 5433:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    if: ${{ github.repository == 'mastra-ai/mastra' && (contains(github.head_ref || github.ref_name, 'changeset-release') || github.event_name == 'workflow_dispatch') }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9.7.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.19.1'
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org'

      - name: Install dependencies
        run: pnpm install

      - name: Prepare CLI package for publishing
        run: cd packages/cli && rm -rf node_modules && pnpm install --shamefully-hoist --ignore-scripts && cd ../..

      - name: Setup npm auth
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > .npmrc

      - name: Build core
        run: pnpm build:core

      - name: Install dane cli
        run: pnpm install -g @mastra/dane

      - name: Set Anthropic Key
        run: dane config --set ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }}

      - name: Publish packages
        run: dane changelog
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_TEAM_ID: ${{ secrets.SLACK_TEAM_ID }}
          CHANNEL_ID: ${{ secrets.CHANNEL_ID }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
