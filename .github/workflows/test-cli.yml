name: CLI Tests

on:
  pull_request:
    branches: [main]
    paths:
      - 'packages/cli/**'
      - '.github/workflows/test-cli.yml'

jobs:
  test:
    # Only run on the main repository, not on forks
    if: ${{ github.repository == 'mastra-ai/mastra' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.19.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build packages
        run: pnpm turbo build --filter "mastra"

      - name: Run CLI tests
        run: pnpm test:cli
        env:
          NODE_OPTIONS: '--max_old_space_size=8096'
