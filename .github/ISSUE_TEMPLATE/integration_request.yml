name: Integration Request
description: Request a new integration to be added to Mastra
title: '[INTEGRATION] '
labels: ['integration', 'enhancement']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new integration for Mastra! Please provide as much detail as possible about the service you'd like to see integrated.

  - type: textarea
    attributes:
      label: Service Information
      description: Provide details about the service you want integrated.
      placeholder: |
        - Service Name: (e.g., Slack, MongoDB, etc.)
        - Service Website: (e.g., https://service.com)
        - API Documentation: (e.g., https://service.com/api-docs)
    validations:
      required: true

  - type: textarea
    attributes:
      label: Use Case
      description: Describe how you would use this integration with Mastra.
      placeholder: 'I want to use this integration to allow my Mastra agents to fetch data from this service and use it in workflows.'
    validations:
      required: true

  - type: checkboxes
    attributes:
      label: Verification
      description: Before submitting, please make sure you've completed the following
      options:
        - label: I have searched the existing issues to make sure this integration hasn't already been requested
          required: true
        - label: I have checked that this service has a public API that can be integrated
          required: true
