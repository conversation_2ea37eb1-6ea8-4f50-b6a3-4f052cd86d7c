name: Documentation Improvement
description: Suggest improvements to Mastra documentation
title: '[DOCS] '
labels: ['documentation']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for helping us improve our documentation! Please provide as much detail as possible about what needs to be improved.

  - type: textarea
    attributes:
      label: Describe the Improvement Needed
      description: A clear and concise description of what needs to be improved.
      placeholder: |
        For example, the documentation should include more examples of how to use the RAG feature with custom data sources.
    validations:
      required: true

  - type: dropdown
    attributes:
      label: Documentation Type
      description: What type of documentation improvement are you suggesting?
      multiple: true
      options:
        - Missing Information
        - Incorrect Information
        - Unclear Explanation
        - Code Example Issue
        - Typo/Grammar
        - Formatting/Structure
        - Other
    validations:
      required: true
