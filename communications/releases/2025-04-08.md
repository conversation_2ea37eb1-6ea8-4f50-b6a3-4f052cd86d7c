# Mastra Release - 2025-04-08

Today we release a new latest version of Mastra. There are several issues lurking we are trying to figure out around the clock right now.

Top issues:

- MCP rough edges, unable to connect, failures in IDEs (if you're around discord a lot you know)
- Memory default embedder "fastembedjs" breaks in certain execution environnments
- Deployers
- Improving Documentation on Memory and MCP/Tools

As for the release itself there is good stuff. Since this our first official release-channel comm, if you want to give feedback on how to better improve it, let me know!

**To take advantage of this release please install all modules @latest.**

Here's the changes

## Getting Started

- Fix example workflow in getting started ([#3473](https://github.com/mastra-ai/mastra/pull/3473))

## Playground

- Show "No Input" for empty workflow steps ([#2696](https://github.com/mastra-ai/mastra/pull/2696))
- Nested workflows rendering in dev playground ([#3408](https://github.com/mastra-ai/mastra/pull/3408))
- Leverage autoform for playground dynamic form ([#3427](https://github.com/mastra-ai/mastra/pull/3427))
- Fix workflow sidebar not expanding the output section ([#3447](https://github.com/mastra-ai/mastra/pull/3447))
- Cleanup playground dynamic form ([#3449](https://github.com/mastra-ai/mastra/pull/3449))
- Ability to configure llm settings from playground ([#3454](https://github.com/mastra-ai/mastra/pull/3454))
- Fix playground freezing when buffer is passed between steps ([#3484](https://github.com/mastra-ai/mastra/pull/3484))
- Add our new design system to the playground ([#3482](https://github.com/mastra-ai/mastra/pull/3482))
- Fix scroll issue on playground tools page ([#3489](https://github.com/mastra-ai/mastra/pull/3489))

## CLI

- Fix CLI build command to use correct Mastra directory structure ([#3435](https://github.com/mastra-ai/mastra/pull/3435))
- Set disablegeoip to false in getsystemproperties ([#3481](https://github.com/mastra-ai/mastra/pull/3481))

## Storage / Vectors

- Cloudflare kv support ([#2642](https://github.com/mastra-ai/mastra/pull/2642))
- Update error message for upsert operations ([#3300](https://github.com/mastra-ai/mastra/pull/3300))
- Clickhouse storage ([#3351](https://github.com/mastra-ai/mastra/pull/3351))
- Add missing ssl property postgres config ([#3399](https://github.com/mastra-ai/mastra/pull/3399))
- Clickhouse ttl configs ([#3397](https://github.com/mastra-ai/mastra/pull/3397))
- Support missing getEvalsByAgentName method in pg and upstash ([#3415](https://github.com/mastra-ai/mastra/pull/3415))
- Move to batch insert for memory management ([#3422](https://github.com/mastra-ai/mastra/pull/3422))
- Update storage initialization and add underscore methods ([#3433](https://github.com/mastra-ai/mastra/pull/3433))
- Check Vectorize index Existence ([#3470](https://github.com/mastra-ai/mastra/pull/3470))
- Update Chroma version and add specific tests ([#3471](https://github.com/mastra-ai/mastra/pull/3471))
- Add missing getTraces method to Upstash ([#3472](https://github.com/mastra-ai/mastra/pull/3472))

## Memory

- Fix crash when parsing invalid JSON in memory messages ([#3280](https://github.com/mastra-ai/mastra/pull/3280))
- Add resourceId to memory metadata ([#3266](https://github.com/mastra-ai/mastra/pull/3266))
- Memory processors ([#3304](https://github.com/mastra-ai/mastra/pull/3304))
- Use markdown formatting instead of XML inside working memory tags ([#3396](https://github.com/mastra-ai/mastra/pull/3396))
- Fix memory semantic recall performance and bundle size ([#3419](https://github.com/mastra-ai/mastra/pull/3419))
- Add performance testing suite for memory ([#3457](https://github.com/mastra-ai/mastra/pull/3457))

## Agents

- Add defaultGenerateOptions/defaultStreamOptions to Agent constructor ([#3143](https://github.com/mastra-ai/mastra/pull/3143))
- Fix fastembed ([#3455](https://github.com/mastra-ai/mastra/pull/3455)) <- Still present on latest. NOT FIXED.

## Workflows

- Fix hanging and excessive execution ([#3253](https://github.com/mastra-ai/mastra/pull/3253))
- Unify start and watch results ([#3282](https://github.com/mastra-ai/mastra/pull/3282))
- Accept unique id on step config ([#3316](https://github.com/mastra-ai/mastra/pull/3316))
- GetWorkflows API ([#3350](https://github.com/mastra-ai/mastra/pull/3350))
- Make runId optional for workflow startAsync api ([#3405](https://github.com/mastra-ai/mastra/pull/3405))
- Fix compound subscribers edge case ([#3406](https://github.com/mastra-ai/mastra/pull/3406))
- Loop Variables ([#3414](https://github.com/mastra-ai/mastra/pull/3414))
- Fix if-else execution ([#3428](https://github.com/mastra-ai/mastra/pull/3428))

## Client SDK

- Update ai sdk to ^4.2.2 ([#3244](https://github.com/mastra-ai/mastra/pull/3244))
- Remove x-mastra-client-type custom header from mastraClient ([#3469](https://github.com/mastra-ai/mastra/pull/3469))

## Mastra Server / Deployers

- Add missing triggerData to the openapi.json for the POST /api/workflow/{workflowId}/start endpoint ([#3263](https://github.com/mastra-ai/mastra/pull/3263))
- Decouple handlers from Hono ([#3294](https://github.com/mastra-ai/mastra/pull/3294))
- Mastra custom API Routes ([#3308](https://github.com/mastra-ai/mastra/pull/3308))
- Vercel deployer fix attempt ([#3340](https://github.com/mastra-ai/mastra/pull/3340)) <-- (still present on latest)
- Support port and server timeouts ([#3395](https://github.com/mastra-ai/mastra/pull/3395))
- Make timeout 30s ([#3422](https://github.com/mastra-ai/mastra/pull/3422))
- Add Cloudflare Worker environment variable auto-population ([#3439](https://github.com/mastra-ai/mastra/pull/3439))
- Fix deployer server ([#3468](https://github.com/mastra-ai/mastra/pull/3468)) <- Caused more issues (still present on latest)

## Voice

- Update voice dependencies ([#3261](https://github.com/mastra-ai/mastra/pull/3261))
- Change listenProvider and speakProvider to input and output ([#3343](https://github.com/mastra-ai/mastra/pull/3343))
- Default agent voice ([#3344](https://github.com/mastra-ai/mastra/pull/3344))
- Voice method references ([#3388](https://github.com/mastra-ai/mastra/pull/3388))

## Evals

- Modified evaluation to include output ([#3353](https://github.com/mastra-ai/mastra/pull/3353))

## CI / Tests

- Update babel monorepo ([#3225](https://github.com/mastra-ai/mastra/pull/3225))
- Update tests to ensure collection is empty ([#3313](https://github.com/mastra-ai/mastra/pull/3313))
- Remove non-package from changeset ([#3346](https://github.com/mastra-ai/mastra/pull/3346))

## Observability

- Exclude more methods from tracing ([#3305](https://github.com/mastra-ai/mastra/pull/3305))
- Add request id to traces ([#3342](https://github.com/mastra-ai/mastra/pull/3342))
- Disable instrumentation if inside web container ([#3410](https://github.com/mastra-ai/mastra/pull/3410))
- Fix tracing and add dual tracing support ([#3453](https://github.com/mastra-ai/mastra/pull/3453))
- Fix error on traces page ([#3466](https://github.com/mastra-ai/mastra/pull/3466))
