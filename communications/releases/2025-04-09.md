Continuing bug fixes found in latest.

## General

- Fix yarn workspace integration (#3519)

## Docs

- Docs have been redesigned and implemented (#3109)

## Playground

- show status UI on the playground for workflows without trigger schema (#3511)
- Correctly reset value maxSteps (#3515)

## Mastra Server

- Fix infinite await loop in Mastra server entrypoints (#3513)

## Storage/Vectors

- Add missing getTraces method to Cloudflare KV (#3499)

## MCP

- Removed fastmcp from mcp-docs-server for more stability (#3496)
