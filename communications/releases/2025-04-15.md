# 2025-04-15 Mastra Release

This release was a grind. We addressed some overdue issues with our deployers. Hopefully this is a step in the right direction. We will continue doing right by the community and squash issues!

## Deployer [IMPORTANT]

- cross-platform support and updates deployment documentation for Vercel, Netlify, Cloudflare. [#3618](https://github.com/mastra-ai/mastra/pull/3618)
- Revamp the deployment experience for Mastra. [#3581](https://github.com/mastra-ai/mastra/pull/3581)
- Deprecating the existing deploy command and introducing a new build deployer output. [#3555](https://github.com/mastra-ai/mastra/pull/3555)

## Memory

- Add 'createdAt' timestamp to UI messages during memory conversion to address missing timestamps in UI components. [#3536](https://github.com/mastra-ai/mastra/pull/3536)
- The Memory instance to operate without a vector store or embedder by disabling semantic recall or setting the vector option to false, providing flexibility for users to opt-out of these features. [#3529](https://github.com/mastra-ai/mastra/pull/3529)
- ensuring message content is only filtered if it's an array [#3589](https://github.com/mastra-ai/mastra/pull/3589)

## Storage

- @mastra/pg`, allowing custom schemas for storage and vector modules without affecting the main schema. [#3463](https://github.com/mastra-ai/mastra/pull/3463)
- Add a helper function to check permissions for creating vector extensions in PostgreSQL. [#3492](https://github.com/mastra-ai/mastra/pull/3492)
- Error logging for vector query and graph rag tool to provide more detailed error information. [#3632](https://github.com/mastra-ai/mastra/pull/3632)
- Added error and debug logging to vector-query and graph-rag components. [#3629](https://github.com/mastra-ai/mastra/pull/3629)
- An issue with traces not displaying in the playground by removing unnecessary wrapping in a traces object. [#3572](https://github.com/mastra-ai/mastra/pull/3572)

## Tools

- Add execution container for tools when called from the API or playground #3626. [#3628](https://github.com/mastra-ai/mastra/pull/3628)
- Adds logging functionality to the MCP documentation server. [#3530](https://github.com/mastra-ai/mastra/pull/3530)
- The MCP Registry Registry MCP Server to maintain the MCP Registry Registry meme. [#3575](https://github.com/mastra-ai/mastra/pull/3575)
- Parallelization of MCP connections to enhance performance. [#3592](https://github.com/mastra-ai/mastra/pull/3592)
- Configurable timeouts for MCPConfigurations, extending beyond the default 60 seconds. [#3571](https://github.com/mastra-ai/mastra/pull/3571)

## Workflows

- We will be releasing a new preview of Workflows `vNext` 2025-04-16. More details to follow. [IMPORTANT]
- Add a dynamic form using Zod schema for workflow input in the playground UI. [#3607](https://github.com/mastra-ai/mastra/pull/3607)
- Various path changes, including memory document redirection and path fixes. [#3546](https://github.com/mastra-ai/mastra/pull/3546)

## Client SDK - JS

- Client-side tool calling functionality, prepping integration with CopilotKit. [#3549](https://github.com/mastra-ai/mastra/pull/3549)

## Playground

- Fix validation error preventing form submission when the trigger schema is an enum in the playground UI. [#3616](https://github.com/mastra-ai/mastra/pull/3616)
- Allow users to toggle between stream and generate modes in the playground. [#3576](https://github.com/mastra-ai/mastra/pull/3576)
  -Restoring data display functionality. [#3585](https://github.com/mastra-ai/mastra/pull/3585)
- Refactor headers across all pages to use the DS version, eliminating layout shifts and problematic styles. [#3579](https://github.com/mastra-ai/mastra/pull/3579)
- A feature to bundle design tokens as both ESM and CJS in the playground UI to support Tailwind usage in the CLI/playground. [#3577](https://github.com/mastra-ai/mastra/pull/3577)
