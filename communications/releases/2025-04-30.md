# 2025-04-30 - Mastra Release

## Agents

- Dynamic agent properties: instructions, model, and tools are determined at runtime based on the provided context. [BIG NEWS] [#3883](https://github.com/mastra-ai/mastra/pull/3883)

## Workflows

- Getting started with the improvements to the workflow system. [#3771](https://github.com/mastra-ai/mastra/pull/3771)
- VNext workflows to return the full error object instead of just the error message, allowing users greater flexibility in handling errors programmatically. [#3773](https://github.com/mastra-ai/mastra/pull/3773)
- Issues related to suspended paths. [#3772](https://github.com/mastra-ai/mastra/pull/3772)
- Renaming 'container' to 'runtimecontext', adds a steps accessor for stepFlow, introduces getWorkflowRun, and adds vnext_getWorkflows to mastra core. [#3784](https://github.com/mastra-ai/mastra/pull/3784)
- VNextWorkflows handlers and APIs to both the server and deployer components. [#3791](https://github.com/mastra-ai/mastra/pull/3791)
- Issues related to watching from a different run instance. [#3817](https://github.com/mastra-ai/mastra/pull/3817)
- The type inference for result values in the vNext version. [#3840](https://github.com/mastra-ai/mastra/pull/3840)
- VNext to unset the currentStep variable when a workflow status change event occurs. [#3842](https://github.com/mastra-ai/mastra/pull/3842)
- The vNext workflow trigger to use the resumeSchema, addressing a bug. [#3853](https://github.com/mastra-ai/mastra/pull/3853)
- And cleans up issues related to the workflow watch functionality in the vNext branch. [#3861](https://github.com/mastra-ai/mastra/pull/3861)
- Functionality to clone existing workflows while allowing the assignment of a new workflow ID. [#3859](https://github.com/mastra-ai/mastra/pull/3859)
- The watch cleanup function is called when closing the watch stream controller to properly fix a related bug. [#3867](https://github.com/mastra-ai/mastra/pull/3867)
- The documentation to fix the getWorkflow access in vNext examples. [#3968](https://github.com/mastra-ai/mastra/pull/3968)

## Server

- Fix tools endpoint in API [#3778](https://github.com/mastra-ai/mastra/pull/3778)
- An issue in the server handler to ensure audio streams are correctly formatted for playback. [#3789](https://github.com/mastra-ai/mastra/pull/3789)

## Client SDK - JS

- vNext Workflow support in Client SDK. [#3801](https://github.com/mastra-ai/mastra/pull/3801)

## CLI

- The mastra CLI's non-interactive mode by adding a --dir option, enhancing the --no-example flag, and unifying project name input, along with related documentation updates. [#3765](https://github.com/mastra-ai/mastra/pull/3765)
- Configuration for the Mastra server host, removes a related CLI flag, updates documentation, and includes a changeset. [#3868](https://github.com/mastra-ai/mastra/pull/3868)
- A new CLI option to globally configure the cursor for the Mastra MCP docs server, enabling all cursor projects to access the MCP server. [#3880](https://github.com/mastra-ai/mastra/pull/3880)

## Observability

- Mastra Cloud Exporter: enabling trace data to be sent to Mastra Cloud from various deployment platforms including Vercel, Netlify, and Cloudflare. [#3742](https://github.com/mastra-ai/mastra/pull/3742)
- API request logging from the local development server. [#3780](https://github.com/mastra-ai/mastra/pull/3780)
- Span transformation for Mastra Cloud is now handled server-side, allowing updates without requiring users to update their package. [#3828](https://github.com/mastra-ai/mastra/pull/3828)

## Deployer

- Reduce Vercel bundle size by including only generated files from the output directory. [#3819](https://github.com/mastra-ai/mastra/pull/3819)
- Fix Netlify deployer by ensuring that tools.mjs is written to the correct folder. [#3822](https://github.com/mastra-ai/mastra/pull/3822)
- Fix Cloudflare deployer by removing the use of import.meta.url. [#3821](https://github.com/mastra-ai/mastra/pull/3821)

## Playground

- The traces UI by introducing a redesigned trace span interface. [#3812](https://github.com/mastra-ai/mastra/pull/3812)
- Support for running vNext workflows in the playground as a new feature. [#3820](https://github.com/mastra-ai/mastra/pull/3820)
- A bug where the triggerSchema default was not displaying correctly in the workflow UI. [#3827](https://github.com/mastra-ai/mastra/pull/3827)
- The `x-mastra-dev-playground` header to all playground requests to address issue #3741. [#3845](https://github.com/mastra-ai/mastra/pull/3845)
- Playground routing model settings for AgentNetworks, enabling configurable routing options in the playground interface. [#3864](https://github.com/mastra-ai/mastra/pull/3864)
- Overflow scrolling to the agent traces table and allows passing a custom className to override default behaviors such as height. [#3875](https://github.com/mastra-ai/mastra/pull/3875)
- Badges to visually indicate failure or successful traces. [#3929](https://github.com/mastra-ai/mastra/pull/3929)
- An issue by adding a click handler to trace rows to improve interactivity. [#3933](https://github.com/mastra-ai/mastra/pull/3933)
- Trace visibility and accuracy on smaller devices. [#3981](https://github.com/mastra-ai/mastra/pull/3981)

## MCP

- Docker as a new MCP registry option to Mastra's MCP registry list. [#3846](https://github.com/mastra-ai/mastra/pull/3846)
- Streamable HTTP MCP support to MastraMCPClient and MCPConfiguration, enabling session ID handling and maintaining backwards compatibility by falling back to SSE if SHTTP fails. [BIG NEWS] [#3834](https://github.com/mastra-ai/mastra/pull/3834)
- Instance management and caching for MCPConfiguration by caching based on both id and servers, and disconnecting outdated instances to prevent memory leaks. [#3759](https://github.com/mastra-ai/mastra/pull/3759)
- Array schema handling in Zod by defaulting to string type when type is missing, prevents errors when items are absent, adds comprehensive tests, and notes lack of 'allOf' support for future improvement. [#3892](https://github.com/mastra-ai/mastra/pull/3892)
- A bug related to checking for the presence of constant value mappings. [#3978](https://github.com/mastra-ai/mastra/pull/3978)

## Storage

- MongoDBVector to add support for using MongoDB as a vector database. [BIG NEWS] [#3823](https://github.com/mastra-ai/mastra/pull/3823)
- A pgPoolOptions parameter to the PgVector constructor, enabling custom database connection pool configurations such as SSL settings, and includes related tests. [#3833](https://github.com/mastra-ai/mastra/pull/3833)
- Add the ability to retrieve workflow runs by ID and filter workflow runs by resourceID for storage providers. [#3807](https://github.com/mastra-ai/mastra/pull/3807)
- Remove unnecessary function from the ClickHouse codebase. [#3905](https://github.com/mastra-ai/mastra/pull/3905)
- Support for retrieving workflow runs and individual workflow run details for Cloudflare and Cloudflare D1. [#3927](https://github.com/mastra-ai/mastra/pull/3927)
- Deprecation warnings for the PGVector constructor and for passing individual arguments to vectors. [#3973](https://github.com/mastra-ai/mastra/pull/3973)

## Memory

- Warnings to notify developers about upcoming breaking changes to memory defaults, displaying current and future options unless all are explicitly set, and updates project scaffolding to prevent unnecessary warnings in new projects. [#3797](https://github.com/mastra-ai/mastra/pull/3797)

## RAG

- Removes the need for an ingestion pipeline. [#3798](https://github.com/mastra-ai/mastra/pull/3798)
- Remove llamaindex dependency and introduces local types for handling extraction and documents. [#3811](https://github.com/mastra-ai/mastra/pull/3811)

## Examples

- The voice agent examples by incorporating lessons from the voice workshop. [#3794](https://github.com/mastra-ai/mastra/pull/3794)
- The Mastra examples documentation page to better align with the sidebar, improves organization, and sets the Rag section to auto-expand by default. [#3803](https://github.com/mastra-ai/mastra/pull/3803)
- A typo and removes an unnecessary period in the showcase section documentation. [#3962](https://github.com/mastra-ai/mastra/pull/3962)

## CI / Tests

- Restores missing server unit tests, ensuring they are included in the CI process. [#3790](https://github.com/mastra-ai/mastra/pull/3790)
- Previously unrun logging tests for @mastra/mcp and ensures they are executed in the CI pipeline. [#3813](https://github.com/mastra-ai/mastra/pull/3813)
- An issue by ensuring that the 'dir' argument is always treated as an absolute path. [#3824](https://github.com/mastra-ai/mastra/pull/3824)
- This PR re-enables Renovate after it was accidentally disabled, ensuring the configuration matches the desired setup. [#3847](https://github.com/mastra-ai/mastra/pull/3847)
- The issue labeler to use a local version of dane instead of the global one. [#3872](https://github.com/mastra-ai/mastra/pull/3872)
- The reliability of vector tests by skipping Astra, adding a unique index, and wrapping describe, list, and delete operations in try/catch blocks in vectorize. [#3967](https://github.com/mastra-ai/mastra/pull/3967)
- A version rebump, likely updating dependency or package versions without introducing new features or bug fixes. [#3975](https://github.com/mastra-ai/mastra/pull/3975)
