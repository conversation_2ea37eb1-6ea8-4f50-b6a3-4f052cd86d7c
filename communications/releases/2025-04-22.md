# 2025-04-22 Mastra Release

## BREAKING CHANGES

- We have renamed `Container` to `RuntimeContext` #3761

## General

- CommonJS support in TypeScript for non-bundler environments, enabling easier migration of older projects to Mastra and adds end-to-end tests for these scenarios. [#3613](https://github.com/mastra-ai/mastra/pull/3613)

## MONOREPO SUPPORT

- Bundling issues with workspace packages that use native dependencies by detecting such packages among externals, creating tarballs for them, and referencing them locally in package.json to enable proper installation during deployment. [#3602](https://github.com/mastra-ai/mastra/pull/3602)

## CLI/Server

- Issues with nested import paths (such as firebase-admin/firestore) by making these imports external when bundling fails. [#3452](https://github.com/mastra-ai/mastra/pull/3452)
- The bundler to respect the actual NODE_ENV environment variable during development while defaulting to production for standard builds, and adds comprehensive tests to ensure correct environment propagation. [#3642](https://github.com/mastra-ai/mastra/pull/3642)
  -Server configuration fixes [#3669](https://github.com/mastra-ai/mastra/pull/3669)
- Mastra server.build configuration options for swaggerUI, openAPIDocs, and apiReqLogs. [#3674](https://github.com/mastra-ai/mastra/pull/3674)

* The correct log level is used in 'mastra dev' by addressing an environment variable change that caused logs to default to 'WARN' instead of 'INFO'. [#3758](https://github.com/mastra-ai/mastra/pull/3758)

## Storage

- Replace \_\_ functions with a Proxy, fixes a potential infinite loop in storage dev.entry, related bug fixes, code refactoring, and test updates. [#3665](https://github.com/mastra-ai/mastra/pull/3665)
- The Postgres store configuration to use 'schemaName' for consistency with the vector implementation and adds validation to ensure connections are present, throwing an error if not. [#3681](https://github.com/mastra-ai/mastra/pull/3681)
- Support for Cloudflare D1. [#2932](https://github.com/mastra-ai/mastra/pull/2932)
- The $in, $nin, and $contains operators for PG and LibSQL to correctly handle arrays and substring matches, aligning their behavior with Mongo and Upstash. [#3725](https://github.com/mastra-ai/mastra/pull/3725)

## Memory

- All new messages are saved at least 1ms apart by adjusting createdAt timestamps, adds a function to correct previously misordered tool calls, and includes new tests. [#3654](https://github.com/mastra-ai/mastra/pull/3654)
- The Memory module's chunkText function to use character-based chunking instead of tiktoken, improving startup performance while maintaining safe chunk sizes. [#3627](https://github.com/mastra-ai/mastra/pull/3627)
- Message ordering in @mastra/core. [#3672](https://github.com/mastra-ai/mastra/pull/3672)
- The logic for hiding working memory in messages changed to use a non-mutating update function instead of a mutating one. [#3720](https://github.com/mastra-ai/mastra/pull/3720)
- A previous change to TokenLimiter caused new messages to be incorrectly inserted into the memory messages array. [#3763](https://github.com/mastra-ai/mastra/pull/3763)

## Deployer

- Build process include tools directory [#3668](https://github.com/mastra-ai/mastra/pull/3668)

## RAG

- Upgraded 'llamaindex' dependencies to newer versions. [#3503](https://github.com/mastra-ai/mastra/pull/3503)
- Remove LlamaIndex extractors and replace them with custom Mastra extractors. [#3647](https://github.com/mastra-ai/mastra/pull/3647)

## Client SDK

- Fix memory thread creation. [#3753](https://github.com/mastra-ai/mastra/pull/3753)
- Fix voice speak method. [#3766](https://github.com/mastra-ai/mastra/pull/3766)

## MCP

- Error messages to include the name of the MCP tool that failed, making it easier for consumers to identify the source of connection errors. #3664
- `MCPServer` Class, enabling users to create their own MCP server with custom tools. [#3714](https://github.com/mastra-ai/mastra/pull/3714)

## Voice

- The OpenAI realtime voice provider to emit tool call result events, enabling real-time feedback on tool execution outcomes. [#3657](https://github.com/mastra-ai/mastra/pull/3657)
- Fix WebSocket connection issues in OpenAI Realtime [#3671](https://github.com/mastra-ai/mastra/pull/3671)
- OpenAI realtime tool result. [#3680](https://github.com/mastra-ai/mastra/pull/3680)
- Voice reference handling and tool-call-start functionality. [#3683](https://github.com/mastra-ai/mastra/pull/3683)

## Observability

- External dependencies specified in the telemetry configuration, such as those required by third-party exporters like Langfuse, are properly installed to prevent runner failures. [#3762](https://github.com/mastra-ai/mastra/pull/3762)

## Agent Network

- Add RuntimeContext to AgentNetwork. [#3653](https://github.com/mastra-ai/mastra/pull/3653)
- Fix table layout issue in the Agent Network section of the Playground, aligning its design with recent updates made to the Agent and Workflow sections. [#3744](https://github.com/mastra-ai/mastra/pull/3744)
