{"name": "@mastra/auth-auth0", "version": "0.10.0", "description": "Mastra Auth0 Auth integration", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --experimental-dts --clean --treeshake", "build:watch": "pnpm build --watch", "test": "vitest run", "lint": "eslint ."}, "license": "Apache-2.0", "dependencies": {"jose": "^6.0.11"}, "devDependencies": {"@internal/lint": "workspace:*", "@mastra/core": "workspace:*", "@types/node": "^20.19.0", "eslint": "^9.30.1", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}}