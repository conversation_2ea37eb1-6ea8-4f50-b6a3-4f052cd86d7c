services:
  db:
    image: pgvector/pgvector:pg16
    container_name: 'pg-vector-db'
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-mastra}
    volumes:
      - pgdata:/var/lib/postgresql/data

  qdrant:
    image: qdrant/qdrant:latest
    container_name: 'qdrant-db'
    ports:
      - '6333:6333'
    volumes:
      - qdrant_data:/qdrant/storage

  redis:
    image: redis
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data

volumes:
  pgdata:
  qdrant_data:
  redis_data:
