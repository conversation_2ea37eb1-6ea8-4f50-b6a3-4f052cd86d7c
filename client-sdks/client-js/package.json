{"name": "@mastra/client-js", "version": "0.10.15-alpha.0", "description": "The official TypeScript library for the Mastra Client API", "author": "", "type": "module", "types": "dist/index.d.ts", "main": "dist/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/mastra-ai/mastra.git", "directory": "client-sdks/client-js"}, "homepage": "https://github.com/mastra-ai/mastra/tree/main/client-sdks/client-js#readme", "license": "Apache-2.0", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --clean --treeshake=smallest --splitting", "dev": "pnpm build --watch", "test": "vitest run"}, "dependencies": {"@ag-ui/client": "^0.0.27", "@ai-sdk/ui-utils": "^1.2.11", "@lukeed/uuid": "^2.0.1", "@mastra/core": "workspace:*", "json-schema": "^0.4.0", "rxjs": "7.8.1", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.5"}, "peerDependencies": {"zod": "^3.0.0"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@internal/lint": "workspace:*", "@tsconfig/recommended": "^1.0.9", "@types/json-schema": "^7.0.15", "@types/node": "^20.19.0", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}}