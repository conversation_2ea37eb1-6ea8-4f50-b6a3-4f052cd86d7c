{"$schema": "https://unpkg.com/@changesets/config@3.0.4/schema.json", "changelog": "@changesets/cli/changelog", "commit": false, "fixed": [["@mastra/core", "@mastra/server", "@mastra/deployer"]], "linked": [], "access": "public", "baseBranch": "main", "updateInternalDependencies": "patch", "bumpVersionsWithWorkspaceProtocolOnly": true, "ignore": ["*", "!mastra", "!create-mastra", "!@mastra/*", "!docs", "@internal/*", "@mastra/memory-integration-tests", "@mastra/playground"]}